import { UsersTable } from "@/components/UsersTable";
import { api } from "@/helpers/api";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { useQuery } from "@tanstack/react-query";

function Jail() {
    const { isLoading, error, data } = useQuery(
        api.jail.jailList.queryOptions({
            select: (d) => d.sort((a, b) => (a.jailedUntil > b.jailedUntil ? 1 : -1)),
        })
    );
    const { data: currentUser } = useFetchCurrentUser();

    if (error) return "An error has occurred: " + error.message;
    return (
        <div className="mb-8 md:mx-auto md:mb-0 md:max-w-6xl">
            <UsersTable data={data} isLoading={isLoading} type="jail" currentUser={currentUser} />
        </div>
    );
}

export default Jail;
