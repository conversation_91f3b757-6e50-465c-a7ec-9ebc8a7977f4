import type { AppRouterClient } from "@/lib/orpc";

export type SuggestionState = "New" | "Accepted" | "Completed" | "Denied";
export type VoteType = "upvote" | "downvote";

export interface VoteHistory {
    suggestionId: number;
    voteType: VoteType;
}

export type Suggestion = Awaited<ReturnType<AppRouterClient["suggestions"]["getSuggestions"]>>[number];
export type SuggestionComment = Awaited<ReturnType<AppRouterClient["suggestions"]["getComments"]>>[number];
