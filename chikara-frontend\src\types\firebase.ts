/**
 * Frontend Firebase notification types
 * These should match the backend types for consistency
 */

// Firebase notification payload structure
export interface FirebaseNotificationPayload {
    title: string;
    body: string;
    icon?: string;
    image?: string;
    clickAction?: string;
    data?: Record<string, string>;
}

// Notification permission states
export type NotificationPermission = "granted" | "denied" | "default";

// Token management interfaces
export interface TokenValidationResult {
    isValid: boolean;
    error?: string;
}

export interface TokenServerResponse {
    success: boolean;
    data?: any;
    error?: string;
    statusCode?: number;
}

export interface NotificationPermissionResult {
    success: boolean;
    permission: NotificationPermission;
    token?: string;
    error?: string;
}

// Firebase configuration interface
export interface FirebaseClientConfig {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    vapidKey: string;
}

// Service worker message types
export interface ServiceWorkerMessage {
    type: 'NOTIFICATION_RECEIVED' | 'TOKEN_REFRESH' | 'PERMISSION_CHANGED';
    payload?: any;
}

// Notification action types for service worker
export interface NotificationAction {
    action: string;
    title: string;
    icon?: string;
}

export interface NotificationOptions {
    body?: string;
    icon?: string;
    badge?: string;
    image?: string;
    tag?: string;
    requireInteraction?: boolean;
    silent?: boolean;
    data?: any;
    actions?: NotificationAction[];
    timestamp?: number;
}

// Error types for better error handling
export enum FirebaseErrorCode {
    INVALID_TOKEN = 'messaging/invalid-registration-token',
    TOKEN_NOT_REGISTERED = 'messaging/registration-token-not-registered',
    INTERNAL_ERROR = 'messaging/internal-error',
    SERVER_UNAVAILABLE = 'messaging/server-unavailable',
    QUOTA_EXCEEDED = 'messaging/quota-exceeded',
    INVALID_ARGUMENT = 'messaging/invalid-argument',
    THIRD_PARTY_AUTH_ERROR = 'messaging/third-party-auth-error'
}

export interface FirebaseError {
    code: FirebaseErrorCode | string;
    message: string;
    details?: any;
}

// Frontend-specific interfaces
export interface ForegroundMessageHandler {
    (payload: any): void;
}

export interface NotificationClickHandler {
    (notificationData: any): void;
}

// Token management state
export interface TokenState {
    token: string | null;
    isValid: boolean;
    lastRefresh: Date | null;
    error: string | null;
}

// Notification settings
export interface NotificationSettings {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    showPreview: boolean;
    categories: {
        [key: string]: boolean;
    };
}

// Browser notification support detection
export interface NotificationSupport {
    isSupported: boolean;
    hasPermission: boolean;
    hasServiceWorker: boolean;
    hasPushManager: boolean;
    userAgent: string;
}

// Message payload from Firebase
export interface FirebaseMessage {
    notification?: {
        title?: string;
        body?: string;
        icon?: string;
        image?: string;
        click_action?: string;
    };
    data?: Record<string, string>;
    from?: string;
    messageId?: string;
    collapseKey?: string;
}

// Service worker registration state
export interface ServiceWorkerState {
    isRegistered: boolean;
    registration: ServiceWorkerRegistration | null;
    error: string | null;
    updateAvailable: boolean;
}
