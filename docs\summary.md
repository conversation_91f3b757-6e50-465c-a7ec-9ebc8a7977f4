# Chikara Academy PBBG - Game Summary

## Overview

Chikara Academy is an immersive persistent browser-based game (PBBG) that transports players into a vibrant world where martial arts, strategy, and adventure converge. Set in a modern urban environment with mystical elements, players take on the role of aspiring martial artists seeking to master their craft while navigating complex social dynamics, gang rivalries, and personal growth.

The game is GUI and image based and does not contain a game world or active character control.

The game seamlessly integrates traditional PBBG mechanics with modern gaming elements, offering a unique blend of:

- Strategic turn-based combat with deep tactical choices
- Rich character progression systems that affect gameplay
- Dynamic social interactions through gangs and chat rooms
- A player-driven economy with multiple currencies and trading systems
- Roguelike elements that ensure no two play sessions are identical
- Daily activities and long-term progression goals
- Comprehensive story mode with episodic content delivery

Players can forge their path through various gameplay styles - whether as a lone wolf perfecting their combat techniques, a social player building alliances within gangs, or an economic mastermind trading in the marketplace.

## Core Gameplay Features

### Character Progression

- **Leveling & Experience System**
    - Dynamic XP gain from diverse activities: combat, quests, crafting, exploration
    - Current level cap of 30
    - Milestone-based rewards at key level thresholds
    - Activity-specific experience tracking for specialized progression

- **Stats & Talents**
    - Six core combat attributes affecting gameplay:
        - Strength: Physical damage, melee combat, anti-evasion
        - Dexterity: Ranged combat, accuracy, critical hits
        - Intelligence: Tech abilities, control effects, crafting success
        - Endurance: Resource management, buff sustain, stamina pool
        - Defence: Damage reduction, anti-crit/debuff resistance
        - Vitality: Health pool, combat speed, survivability
    - Combat triangle: STR > DEX > INT > STR
    - Specialized talent trees with unique combat and utility abilities
    - Talent points earned from level 5 onward
    - Multiple skill trees: Combat stats, General, Crafting, Social
    - Respec system allowing build experimentation

- **Health & Stamina Management**
    - Strategic resource management during encounters
    - Multiple recovery methods: items, resting, special abilities
    - Stamina-based activity limitations
    - Health regeneration systems varying by location and status

### Combat System

- **Turn-Based Battles**
    - 1v1 Turn-based battles with initiative system
    - Multiple attack types: melee attacks, ranged attacks (limited ammo pool), up to 4 equipped combat skills
    - Combat skills can do damage, CC, buffs, debuffs etc
    - Can run away from battles you initiate (chance to fail)
    - Complex damage calculation with stat scaling and mitigation

- **Battle Variants**
    - Traditional 1v1 PvP and PvE battles
    - Rooftop combat featuring NPC boss battles with unique mechanics
    - Tournament-style competitions with special rules
    - Streets roguelike mode with procedurally generated enemies

- **Status Effects & Abilities**
    - Comprehensive combat modifier system:
        - Temporary buffs and debuffs
        - Damage-over-time effects (bleed, poison)
        - Healing and regeneration
        - Crowd control abilities (stun, sleep, skill lock)
        - Special condition states (disarmed, accuracy debuff)
    - Ability combinations and synergies
    - Counter-play options for all status effects

### Streets Roguelike Mode

- **Dynamic Map Generation**
    - Procedurally generated layouts ensuring unique experiences
    - Zone-based progression system with scaling difficulty
    - 18-20 nodes per map with multiple encounter types
    - Progressive difficulty scaling based on zone level
    - Highscore tracking and leaderboard integration

- **Encounter Types**
    - Combat encounters (most common) - battle NPCs with scaling difficulty
    - Character encounters - NPC interactions with rewards/consequences
    - Scavenge encounters - item and resource discovery
    - Buff encounters (rarest) - temporary stat enhancements
    - Boss encounters - zone completion battles with reduced scaling

- **Progression & Persistence**
    - Zone selection up to highest achieved level
    - Buff carry-over between maps within runs
    - Reset on defeat but maintains zone progression
    - Integration with achievement and quest systems

### Story Mode & Questing

- **Quest-Driven Narrative**
    - Main Story quests as primary progression method
    - Quest Log as central hub for all activities
    - Episodic content delivery with time-gated chapters
    - Auto-starting quest behavior for seamless progression

- **Diverse Quest Types**
    - Main storyline missions advancing the core narrative
    - Daily and weekly challenges for regular rewards
    - Gang-specific missions affecting faction standing
    - Tutorial quests introducing game mechanics
    - Random events providing unexpected opportunities
    - Special seasonal and event-based questlines

- **Story Episodes & Choices**
    - Interactive dialogue with branching conversation paths
    - Story flags system for persistent choice consequences
    - Character relationship tracking
    - Multiple episode types: dialogue, choices, battles, scenes

### Crafting & In-Game Economy

- **Multiple Currency System**
    - Yen: Primary currency for most transactions
    - Gang Creds: Earned through gang activities
    - Classroom Points: Earned through class-related actions

- **Shops & Trading**
    - Multiple vendors with unique inventories and specialties
    - Reputation-based shop access and discounts
    - Auction house with bidding and buyout options
    - Player-driven marketplace with dynamic pricing

- **Item Crafting**
    - Complex crafting system with multiple components
    - Resource gathering from various activities
    - Recipe discovery through exploration and achievements
    - Quality-based crafting outcomes with skill scaling
    - Equipment modification and enhancement
    - Consumable item creation

- **Banking & Jobs**
    - Secure currency storage system
    - Time-based job activities for income generation
    - Investment opportunities and resource management

### Social & Community Features

- **Gangs & Factions**
    - Hierarchical gang structure with roles and permissions
    - Inter-gang warfare and territory control
    - Collaborative gang activities and resource sharing
    - Gang progression and perks system
    - Alliance networks between gangs

- **Real-Time Communication**
    - Comprehensive chat systems: global, gang, private, trade
    - Friend system with social features
    - Activity feed showing important events
    - Real-time notifications for various game events

- **Additional Social Features**
    - Player PvP bounty system
    - Leaderboards for various activities
    - Achievement system with combat, crafting, and social milestones
    - Personal statistics tracking

## Extended Game Features

### Academy Systems

- **Classroom**: Learning activities, exams, and Classroom Points earning
- **Exams**: Academy-themed progression tests
- **Settings**: Comprehensive user and game customization

### Entertainment & Activities

- **Casino**: Gambling activities for risk/reward gameplay
- **Arcade**: Minigames for rewards and entertainment
- **Missions**: Timed activities with specific rewards

### Support Systems

- **Shrine**: Global buff system through player donations
- **Infirmary**: Character healing services
- **Jail System**: Consequences for aggressive actions
- **Pet System**: Companions providing player benefits

### Special Features

- **Events**: Time-limited special activities
- **Real Estate**: Property purchasing system
- **Private Messaging**: Direct player communication

## Technical Overview

- **Modern Architecture**
    - React/TypeScript frontend with Tailwind CSS
    - Node.js/Express.js backend with TypeScript
    - MySQL database with Prisma ORM
    - oRPC for type-safe end-to-end communication

- **Performance & Scalability**
    - Redis for session management and caching
    - BullMQ for reliable task queuing and event processing
    - Socket.io for real-time communications
    - Comprehensive error handling and logging

- **Development Features**
    - Modular, feature-based architecture
    - Automated testing with Vitest
    - Winston logging system
    - Event-driven battle system design
    - TanStack Query integration for optimal caching
