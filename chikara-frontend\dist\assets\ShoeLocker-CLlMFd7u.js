import{a8 as e}from"./index-hZ2cjOe1.js";function r(){return e.jsxs("div",{className:"mt-5 flex flex-col",children:[e.jsx("p",{className:"mx-auto px-4 text-lg",children:"The Shoe Lockers are a place where students can change from outside footwear to school slippers. They are a popular place to confess your feelings for someone"}),e.jsx("p",{className:"mx-auto text-sm",children:"Change this text ^"}),e.jsx("div",{className:"mx-auto mt-10",children:e.jsx("p",{children:"Leave a confession"})}),e.jsxs("div",{className:"mx-auto mt-1 flex w-2/5 flex-col",children:[e.jsxs("div",{className:"my-3",children:[e.jsx("label",{htmlFor:"studentid",className:"block font-medium text-gray-700 text-sm",children:"Student ID"}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm",children:"#"}),e.jsx("input",{type:"text",name:"studentid",id:"studentid",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm",placeholder:"1"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"message",className:"mb-3 block",children:"Message"}),e.jsx("textarea",{id:"message",rows:4,className:"block size-full rounded-lg border border-gray-300 bg-gray-50 p-2 text-gray-900 text-sm focus:border-blue-500 focus:ring-blue-500",placeholder:"Leave confession message here..."})]}),e.jsx("button",{className:"sidebar_button mx-auto my-3 w-28 rounded-md px-3 py-2 font-semibold focus:outline-hidden",children:"Send"})]})]})}export{r as default};
