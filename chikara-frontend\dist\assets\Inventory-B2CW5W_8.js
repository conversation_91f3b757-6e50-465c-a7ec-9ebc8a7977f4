import{ac as P,ad as W,a7 as m,aj as O,bd as pe,a8 as e,be as xe,ae as R,ba as he,bf as K,bg as U,aD as Q,a6 as z,bh as p,av as fe,r as d,au as H,b7 as J,aB as ge,a$ as L,b1 as ye,at as X,R as je,b8 as be,bi as we,aM as ve,bj as Ne,a5 as Ie,bk as Y}from"./index-hZ2cjOe1.js";import{r as Z}from"./rarityColours-Bwgi3gKU.js";import{D as Ce}from"./DataTable-IzuqENlT.js";import{u as ke}from"./useGetInventory-BToI706C.js";import"./ag-theme-quartz-B8VIcOdW.js";const ee=()=>{const r=P(),t=W(m.user.equipItem.mutationOptions({onMutate:async n=>{const i=n._userItem;await r.cancelQueries({queryKey:m.user.getEquippedItems.key()});const o=r.getQueryData(m.user.getEquippedItems.key());return r.setQueryData(m.user.getEquippedItems.key(),g=>({...{...g},[i.item.itemType]:i})),{previousEquippedItems:o}},onError:(n,i,o)=>{r.setQueryData(m.user.getEquippedItems.key(),o.previousEquippedItems),O.error(n.message||"An error occurred")},onSettled:async()=>{await r.invalidateQueries({queryKey:m.user.getEquippedItems.key()})},onSuccess:()=>{O.success("Item equipped!")}}));return{equipItem:{...t,mutate:n=>{const{currentUser:i,userItem:o}=n;return i?.hospitalisedUntil&&i.hospitalisedUntil>0?(O.error("Can't equip items while hospitalised!"),Promise.reject(new Error("Can't equip items while hospitalised!"))):i?.jailedUntil&&i.jailedUntil>0?(O.error("Can't equip items while jailed!"),Promise.reject(new Error("Can't equip items while jailed!"))):t.mutateAsync({userItemId:o.id,_userItem:o})}}}},Me=({inventory:r,currentUser:t,equippedItems:s})=>{const{equipItem:n}=ee(),{hideItemTooltip:i,setHideItemTooltip:o}=pe(),g=a=>a.item.statModifiers?Object.entries(a.item.statModifiers).map(([u,b])=>{const y=Math.max(0,(b-1)*100);return e.jsxs("p",{className:"text-base",children:[e.jsxs("span",{className:"text-custom-yellow",children:["+",Math.round(u==="health"?b:y)]}),u!=="health"&&"%"," ",u!=="health"&&"%"," ",u.replace("health","HP").replace("strength","STR").replace("dexterity","DEX").replace("intelligence","INT").replace("lifesteal","LIFE STEAL").replace("defence","DEF").replace("npcDamage","Increased DMG vs NPCs").replace("fleeChance","chance to Flee").replace("encounterReward","Yen gained from encounters")]},u)}):[],x=a=>{const u=s?.[a.itemType]||null;return u?a.id===u.id:!1};return e.jsx(xe,{openOnClick:!0,clickable:!0,id:"equip-tooltip",afterHide:()=>i&&o(!1),className:"pointer-events-auto z-600 max-h-[40dvh] overflow-y-auto overflow-x-hidden border border-gray-600/50",opacity:"1",style:{backgroundColor:"rgba(7, 6, 7, 0.97)",color:"#FFF",padding:0},render:()=>e.jsxs("div",{className:"flex h-full w-72 flex-col py-2 text-center xl:w-80",children:[(!r||r.length===0)&&e.jsx("p",{className:"text-center text-sm",children:"No items available"}),r?.map((a,u)=>e.jsxs("div",{children:[e.jsx("hr",{className:R("my-1.5 border-gray-600/75",u===0?"hidden":"")}),e.jsxs("div",{className:"flex items-center justify-center gap-3 px-4",children:[e.jsx("div",{className:"w-[13%]",children:e.jsx("img",{className:R("z-10 mx-auto aspect-square max-w-10 grid-cols-2"),src:"https://cdn.battleacademy.io/"+a.item.image,alt:"",onError:b=>b.target.src=he(!0)})}),e.jsxs("div",{className:"flex flex-1 flex-col",children:[e.jsxs("p",{className:R(Z(a.item.rarity),"text-base"),children:[a.item.name," ",a.upgradeLevel>0&&e.jsxs("span",{children:["+",a.upgradeLevel]})]}),e.jsxs("div",{className:"flex min-w-24 flex-col text-gray-200 text-sm",children:[a.item.damage>0&&e.jsxs("div",{children:[e.jsx("span",{className:"mr-1 text-blue-500 text-lg",children:K(a,"damage")})," ","DMG",e.jsx(U,{item:a.item,equippedItems:s,type:"damage"})]}),a.item.armour>0&&e.jsxs("div",{className:"",children:[e.jsx("span",{className:"mr-1 text-blue-500 text-sm",children:K(a,"armour")})," ","ARMOR",e.jsx(U,{item:a.item,equippedItems:s,type:"armour"})]}),a.item.baseAmmo>0||a.item.strength>0||a.item.dexterity>0||a.item.statModifiers?e.jsxs(e.Fragment,{children:[a.item.baseAmmo>0&&e.jsxs("div",{className:"text-base",children:[a.item.baseAmmo," Base Ammo"," ",e.jsx(U,{item:a.item,equippedItems:s,type:"ammo"})]}),a.item.strength>0&&e.jsxs("div",{className:"text-base text-custom-yellow",children:["+",a.item.strength,"% STR"," ",e.jsx(U,{item:a.item,equippedItems:s,type:"strength"})]}),a.item.dexterity>0&&e.jsxs("div",{className:"text-base text-custom-yellow",children:["+",a.item.dexterity,"% DEX"," ",e.jsx(U,{item:a.item,equippedItems:s,type:"dexterity"})]}),a.item.statModifiers&&e.jsxs("div",{className:"mx-auto flex text-base text-custom-yellow",children:[g(a),e.jsx(U,{item:a.item,equippedItems:s,type:"modifiers"})]})]}):null]})]}),e.jsx("div",{className:"-mr-2 flex flex-1 justify-end md:mr-0",children:x(a.item)?e.jsx("span",{className:"mr-2 text-custom-yellow",children:"Equipped"}):e.jsx(Q,{onClick:()=>n.mutate({currentUser:t,userItem:a}),children:"Equip"})})]})]},a.id))]})})},Ee=r=>{const t=P();return W(m.user.useItem.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:m.user.getInventory.key()}),t.invalidateQueries({queryKey:m.user.getCurrentUserInfo.key()}),t.invalidateQueries({queryKey:m.user.getStatusEffects.key()}),O.success("Item used successfully!")},onError:s=>{O.error(s.message||"Failed to use item")}}))},Te=(r={})=>z(m.specialItems.getDailyChestItems.queryOptions({staleTime:3e5,...r}));class ${itemId;itemName;itemQuantity;rarity;itemImage;dropRate;constructor(t,s){this.itemId=t,this.itemName=s.itemName,this.itemQuantity=s.itemQuantity,this.rarity=s.rarity,this.itemImage=s.itemImage}}class qe{winner;allWeapons;rouletteWrapper;weaponWrapper;weaponsRef;weapons;weaponsCount;weaponPrizeId;transitionDuration;itemWidth;constructor(t){this.winner={itemName:"Placeholder",itemId:-1,itemQuantity:"",rarity:"",itemImage:"",dropRate:0},this.allWeapons=t.weapons,this.weapons=[],this.weaponsRef=t.weaponsRef,this.rouletteWrapper=t.weaponsRef,this.weaponWrapper=t.weaponsRef,this.weaponsCount=t.weaponsCount||50,this.weaponPrizeId=this.randomRange(this.weaponsCount/2,this.weaponsCount-5),this.transitionDuration=t.transitionDuration||10,this.itemWidth=t.itemWidth||100}randomRange=(t,s)=>Math.floor(Math.random()*(s-t+1))+t;shuffle=t=>{for(let s=t?.length-1;s>0;s--){const n=Math.floor(Math.random()*(s+1));[t[s],t[n]]=[t[n],t[s]]}};set_weapons=()=>{let t=[];const s=this.allWeapons?.length,n=(i,o)=>{let g=0;const x=[];for(let a=i;a<=o;a+=1)x.push(new $(a,this.allWeapons[g])),g=g===s-1?0:g+1;return this.shuffle(x),x};if(s===0)throw new Error("Ошибка! Нет актёров.");t=t.concat(n(0,this.weaponPrizeId-1)),t[this.weaponPrizeId]=new $(this.weaponPrizeId,this.winner),t=t.concat(n(this.weaponPrizeId+1,this.weaponsCount-1)),this.weapons=t};spin=()=>{const t=Math.floor(this.itemWidth/2),s=Math.floor(this.itemWidth/20),n=(this.weaponPrizeId-4)*this.itemWidth+t+this.randomRange(s,18*s);return this.weaponWrapper.current.style.transition=`left ${this.transitionDuration}s ease-out`,setTimeout(()=>{this.weaponWrapper.current.style.left=`-${n}px`},100),this.weaponPrizeId};setWinner=t=>{this.weapons[this.weaponPrizeId]=t}}const Se=()=>{const r=P();return W(m.specialItems.useDailyChest.mutationOptions({onSuccess:()=>{setTimeout(()=>{r.invalidateQueries({queryKey:m.user.getInventory.key()})},6e3)},onError:t=>{const s=t.message||"Unknown error occurred";console.error(s),p.error(s)}}))},Re=({items:r,itemsCount:t,transitionDuration:s,isSpin:n,setIsSpin:i,handleClose:o})=>{const[g,x]=d.useState(r),[a,u]=d.useState(-1),[b,y]=d.useState(!1),[M,v]=d.useState([]),[E,f]=d.useState(!1),{mutate:C,data:h,isSuccess:I}=Se(),k=H(),F=d.useRef(null),D=d.useRef(null),T=d.useRef(null),_=()=>{v(M.concat(g[a])),i(!1),y(!0)};d.useLayoutEffect(()=>{if(!F.current||!D)return;const w=new qe({winner:null,weapons:r,rouletteContainerRef:F.current,weaponsRef:D,weaponsCount:t,transitionDuration:s,itemWidth:k?100:125});w.set_weapons(),x(w.weapons),T.current=w},[r,t,s,k]);const N=async()=>{C()};return d.useEffect(()=>{if(I&&h){const w=r.find(q=>q.itemId===h.itemId);w&&T.current&&(T.current.setWinner(w),f(!0),i(!0),setTimeout(()=>{if(T.current){const q=T.current.spin();u(q)}},1e3))}},[I,h,r]),e.jsx("div",{children:e.jsxs("div",{className:"rouletteWrapper",children:[e.jsx("div",{ref:F,children:e.jsxs("div",{className:"relative h-[160px] w-[700px] overflow-hidden rounded-[5px] border border-[#232730] md:w-[875px]",children:[e.jsx("div",{className:"evTarget"}),e.jsx("div",{ref:D,className:"evWeapons",onTransitionEnd:_,children:g?.map((w,q)=>e.jsx(We,{id:q,isLoser:q!==a&&!n&&b,itemName:w.itemName,rarity:w.rarity,itemImage:w.itemImage,itemQuantity:w.itemQuantity},q))})]})}),E?e.jsx("div",{className:"mt-5 flex h-16 flex-col gap-2",children:M?.length>0&&e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("p",{className:"font-body font-semibold text-lg text-white md:text-2xl",children:["You received ",e.jsxs("span",{className:"text-custom-yellow",children:[M[0]?.itemName," "]})," ","x",M[0]?.itemQuantity,"!"]}),e.jsx("button",{className:"mx-auto w-1/5 cursor-pointer rounded-md border border-gray-700 bg-red-600 p-2 px-5 text-stroke-sm text-white hover:brightness-95",onClick:()=>o(),children:"Close"})]})}):e.jsxs("button",{type:"button",disabled:n||E,className:"weapons-center darkBlueButtonBGSVG mx-auto mt-5 flex h-16 w-1/2 justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:brightness-75 md:w-3/4 dark:text-slate-200",onClick:N,children:[e.jsx("img",{className:"-ml-4 my-auto mr-2.5 inline-block size-8",src:"https://cdn.battleacademy.io/ui-images/KWEkQyF.png",alt:""}),e.jsx("span",{className:"my-auto text-2xl",children:"Open"})]})]})})},Fe=({potentialChestItems:r,isLoading:t,isSpin:s,setIsSpin:n,handleClose:i})=>e.jsx("div",{className:"LootboxWrapper",children:t?e.jsx(fe,{center:!0}):e.jsx(Re,{items:r,itemsCount:150,transitionDuration:5,isSpin:s,setIsSpin:n,handleClose:i})}),We=({id:r,itemName:t,rarity:s,itemImage:n,isLoser:i,itemQuantity:o})=>e.jsx("div",{className:"evWeapon",style:i?{opacity:"0.5"}:{opacity:"1"},children:e.jsxs("div",{className:"evWeaponInner",id:String(r),children:[e.jsx("div",{className:"evWeaponBorder"}),e.jsx("img",{className:"w-[70px]! h-[70px]! absolute! top-[25px]! left-[50%]! -translate-x-1/2",src:n,alt:t}),e.jsxs("span",{className:"-translate-x-1/2 absolute top-0 left-[25px] font-body font-semibold text-base text-blue-500 text-stroke-s-sm",children:[o,"x"]}),e.jsx("div",{className:"evWeaponRarity "+s}),e.jsx("div",{className:"evWeaponText mt-auto font-body font-semibold text-stroke-sm",children:e.jsx("p",{className:"text-sm! "+s,children:t})})]})});function De({openModal:r,setOpenModal:t}){const[s,n]=d.useState(!1),{data:i,isLoading:o}=Te(),g=()=>{t(!1)};return e.jsx(J,{open:r,showClose:!s,modalMaxWidth:"md:max-w-fit",contentPadding:"px-1 md:px-6 py-10",iconBackground:"shadow-lg",contentHeight:"overflow-x-hidden!",title:"Daily Chest",Icon:()=>e.jsx("img",{src:"https://d13cmcqz8qkryo.cloudfront.net/static/items/special/dailychest.png",alt:"",className:"mt-0.5 size-11"}),onOpenChange:s?null:g,children:e.jsx(Fe,{potentialChestItems:i,isLoading:o,isSpin:s,setIsSpin:n,handleClose:g})})}const Ue=()=>{const r=P();return W(m.specialItems.useMaterialsCrate.mutationOptions({onSuccess:()=>{setTimeout(()=>{r.invalidateQueries({queryKey:m.user.getInventory.key()}),r.invalidateQueries({queryKey:m.user.getCurrentUserInfo.key()})},30),p.success("You redeemed 10 Raw Materials for your gang!")},onError:t=>{const s=t.message||"Unknown error occurred";console.error(s),p.error(s)}}))},Oe=()=>{const r=P();return W(m.specialItems.useToolsCrate.mutationOptions({onSuccess:()=>{setTimeout(()=>{r.invalidateQueries({queryKey:m.user.getInventory.key()}),r.invalidateQueries({queryKey:m.user.getCurrentUserInfo.key()})},30),p.success("You redeemed 10 Tools for your gang!")},onError:t=>{const s=t.message||"Unknown error occurred";console.error(s),p.error(s)}}))};function Pe({open:r,setOpen:t,title:s,item:n,currentUser:i}){const[o,g]=d.useState(null),[x,a]=d.useState(""),[u,b]=d.useState("accuracy"),y=ge(),M=P(),{HOSPITALISE_ITEM_NAME:v,REVIVE_ITEM_NAME:E,JAIL_ITEM_NAME:f,MEGAPHONE_ITEM_NAME:C}=y,h=N=>W({...N,onSuccess:()=>{p.success("Item used successfully"),t(!1),M.invalidateQueries({queryKey:m.user.getInventory.key()})},onError:w=>{console.error("Failed to use item:",w),p.error(w.message||"Failed to use item")}}),I=h(m.specialItems.useDeathNote.mutationOptions()),k=h(m.specialItems.useLifeNote.mutationOptions()),F=h(m.specialItems.useKompromat.mutationOptions()),D=h(m.specialItems.useMegaphone.mutationOptions()),T=()=>{if(n.name===v){if(x===""){p.error("You must enter an injury name!");return}I.mutate({userId:o,injuryName:x,injuryType:u})}else if(n.name===E){if(!o){p.error("You must enter a target student ID!");return}k.mutate({userId:o})}else if(n.name===f){if(x===""){p.error("You must enter a reason!");return}if(!o){p.error("You must enter a target student ID!");return}F.mutate({userId:o,reason:x})}else if(n.name===C){if(!x||x.length<5){p.error("Enter a message with at least 5 characters!");return}D.mutate({message:x})}else{p.error("Unknown item type!");return}},_=N=>{N.preventDefault(),T()};return e.jsx(J,{showClose:!0,open:r,title:s,iconBackground:"shadow-lg",Icon:()=>e.jsx("img",{src:"https://cdn.battleacademy.io/ui-images/JXozQjh.png",alt:"",className:"mt-0.5 h-11 w-auto"}),onOpenChange:t,children:e.jsxs("form",{className:"mt-4 w-full max-w-lg",onSubmit:_,children:[n.name!==C&&e.jsx("div",{className:"-mx-3 flex flex-wrap md:mb-2",children:e.jsxs("div",{className:"mb-6 w-full px-3 md:mb-0",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"studentid",children:["Target Student ID",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",children:"#"}),e.jsx("input",{type:"number",name:"studentid",min:1,id:"studentid",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"0",onChange:N=>g(Number.parseInt(N.target.value)||null)})]})]})}),n.name!==E&&n.name!==C&&e.jsx("div",{className:"-mx-3 flex flex-wrap md:mt-5",children:e.jsxs("div",{className:"mb-2 w-full px-3 md:mb-0",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"grid-first-name",children:[n.name===v?"Injury Name":"Reason",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("input",{type:"text",name:"text",id:"text",maxLength:100,className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"",onChange:N=>a(N.target.value)})})]})}),n.name===C&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"-mt-2 text-center text-gray-200 text-lg",children:[e.jsx("p",{className:"text-custom-yellow",children:"Broadcast a global message to all students"}),e.jsx("p",{className:"text-red-400 text-xs",children:"Messages are monitored by Staff, inappropriate messages may result in punishment"})]}),e.jsx("div",{className:"-mx-3 flex flex-wrap md:mt-5",children:e.jsxs("div",{className:"mb-2 w-full px-3 md:mb-0",children:[e.jsxs("label",{className:"mb-2 flex flex-row items-center text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200",htmlFor:"grid-first-name",children:["Global Message",e.jsx("span",{className:"text-red-500",children:" *"}),e.jsx("small",{className:"my-auto ml-4 text-gray-400",children:"(Max length: 400 characters)"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("textarea",{id:"globalMessage",name:"globalMessage",rows:10,maxLength:i?.userType==="admin"?4e3:400,className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"Add a message..",value:x,onChange:N=>a(N.target.value)})})]})})]}),n.name===v&&e.jsx("div",{className:"-mx-3 flex flex-wrap md:mt-5",children:e.jsx("div",{className:"mb-4 flex w-full px-3 md:mb-0",children:e.jsxs("fieldset",{className:"mx-auto mt-1 flex w-3/4 flex-col gap-3 rounded-md border-2 border-gray-600 bg-slate-800 px-4 py-1 text-gray-200 text-stroke-sm md:p-4",children:[e.jsxs("legend",{className:"text-stroke-sm!",children:["Select an Injury Debuff ",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"concussion",name:"injury",value:"concussion",checked:u==="accuracy",onChange:()=>b("accuracy")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"concussion",children:["Concussion"," ",e.jsx("img",{src:L.concussion_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Increased chance to miss attacks"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"bleed",name:"injury",value:"bleed",checked:u==="bleed",onChange:()=>b("bleed")}),e.jsxs("label",{className:"ml-2 inline-flex",htmlFor:"bleed",children:["Bleed",e.jsx("img",{src:L.bleeding_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"HP loss each combat turn"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"fracture",name:"injury",value:"fracture",checked:u==="damage",onChange:()=>b("damage")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"fracture",children:["Fracture",e.jsx("img",{src:L.fracture_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Reduced damage output"})]}),e.jsxs("div",{children:[e.jsx("input",{className:"",type:"radio",id:"fatigue",name:"injury",value:"fatigue",checked:u==="ability_lock",onChange:()=>b("ability_lock")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"fatigue",children:["Fatigue",e.jsx("img",{src:L.fatigue_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Can't use abilities"})]}),e.jsxs("div",{children:[e.jsx("input",{type:"radio",id:"muscle",name:"injury",value:"muscle",checked:u==="defence",onChange:()=>b("defence")}),e.jsxs("label",{className:"ml-2 inline-flex font-medium",htmlFor:"muscle",children:["Muscle Injury",e.jsx("img",{src:L.muscle_injury?.icon,alt:"",className:"my-auto ml-2 h-5 w-auto"})]}),e.jsx("p",{className:"font-body font-semibold text-indigo-500 text-sm",children:"Reduced defence"})]})]})})}),e.jsx("div",{className:"sm:mt-6",children:e.jsx("button",{type:"submit",className:"inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-base text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm",children:"Use Item"})})]})})}function V({equippedItems:r}){const t=H(),{isLoading:s,error:n,data:i}=ke(),[o,g]=d.useState("All"),[x,a]=d.useState(!1),[u,b]=d.useState({}),y=ye("good_stomach"),[M,v]=d.useState(!1),{equipItem:E}=ee(),{data:f}=X(),C=Ee(),[h,I]=d.useState(null),k=d.useRef(null),F=["Death Book","Megaphone","Life Book","Kompromat","Daily Chest","Small Raw Materials Crate","Small Tools Crate"],T=W({mutationFn:async l=>{const{item:c}=l;if(c.health&&c.health>0&&f?.currentHealth===f?.health){p.error("You're already full hp!");return}if(f?.jailedUntil&&f.jailedUntil>0||f?.hospitalisedUntil&&f.hospitalisedUntil>0){p.error("You can't use this in your current state!");return}try{const j=await C.mutateAsync({userItemId:l.id});return c.health&&c.health>0&&p.success(`You recovered ${c.health} HP!`),c.energy&&c.energy>0&&p.success(`You recovered ${c.energy} Energy!`),c.actionPoints&&c.actionPoints>0&&p.success(`You recovered ${c.actionPoints} AP!`),j}catch(j){throw console.error("Error using item:",j),j}},onError:l=>{console.log(l)},onSuccess:l=>{if(l?.info?.injuryRemoved){p.success("Injury treated successfully!");return}if(l?.info?.recipeUnlocked){p.success("Recipe unlocked successfully!");return}!l?.info?.injuryRemoved&&!l?.info?.recipeUnlocked&&p.success("Item used successfully!")}}),{mutate:_}=Ue(),{mutate:N}=Oe(),w=l=>{const{item:c}=l;if(f?.hospitalisedUntil&&f.hospitalisedUntil>0){p.error("Cant use items while hospitalised!");return}if(f?.jailedUntil&&f.jailedUntil>0){p.error("Cant use items while jailed!");return}if(f?.missionEnds&&f.missionEnds>0){p.error("Cant use items while on a mission!");return}if(c?.itemType==="special"){if(c?.name==="Daily Chest"){v(!0);return}if(c?.name==="Small Raw Materials Crate"){_();return}if(c?.name==="Small Tools Crate"){N();return}a(!0),b(c)}else T.mutate(l)},q=l=>{E.mutate({currentUser:f,userItem:l})},te=l=>{const{equippedItems:c}=l,j=l.data;if(!j)return null;const{item:S}=j,ie=A=>["consumable","crafting","upgrade","quest","junk","recipe","pet"].includes(A),le=A=>F.includes(A),oe=A=>["consumable","special","recipe","pet"].includes(A),ce=c?.[S.itemType]?.userItemId===j.id,de=f?.level&&S.level?f.level>=S.level:!1,me=()=>e.jsx(Q,{className:"text-base",onClick:()=>w(j),children:"Use"}),ue=()=>e.jsx(Q,{disabled:!de,className:"text-base",onClick:()=>q(j),children:"Equip"});return e.jsx("div",{className:"flex size-full items-center justify-center",children:ie(S.itemType)||le(S.name)?e.jsx(e.Fragment,{children:oe(S.itemType)?me():null}):e.jsx(e.Fragment,{children:ce?e.jsx("p",{className:"m-auto inline font-semibold text-base text-custom-yellow md:text-sm",children:"Equipped"}):ue()})})};d.useEffect(()=>{k.current&&k.current.api&&(o==="All"?k.current.api.setFilterModel({itemTypeFilter:{type:"notEqual",filter:null}}):k.current.api.setFilterModel({itemTypeFilter:{type:"includes",filter:se[o]}}))},[o]);const B=je.memo(l=>{const{value:c}=l,j=l.data.upgradeLevel||0;return e.jsxs("div",{className:"relative flex h-full items-center gap-3 px-1 py-2 md:w-full md:flex-row md:items-start md:gap-4 md:p-1",children:[e.jsx(be,{itemTypeFrame:!0,item:l.data,className:"my-auto size-14"}),e.jsxs("div",{className:"flex flex-1 flex-col gap-1.5 py-1.5 md:my-auto md:gap-0",children:[e.jsxs("p",{className:R(c.name&&c.name.length>15?"md:text-sm! text-[0.65rem]":"text-sm! md:text-base!","text-wrap! leading-none! truncate text-left font-semibold text-custom-yellow md:text-base"),children:[c.name,j>0&&e.jsxs("span",{children:[" +",j]})]}),e.jsx("p",{className:R(Z(c.rarity),"text-xs! leading-none! md:text-sm! text-left font-semibold"),children:G(c.itemType)}),e.jsx("div",{className:"mt-1 flex flex-col font-bold text-slate-700 text-xs dark:text-indigo-400",children:we(c,y).map(S=>e.jsx("p",{children:S},S))})]})]})});B.displayName="DisplayItemCell";const se={All:[],Weapons:["weapon","ranged","offhand"],Armor:["head","chest","hands","legs","feet","finger","shield"],Consumables:["consumable","recipe","pet"],Material:["crafting","upgrade"],Misc:["special","quest","junk"]},G=l=>l==="weapon"?"Melee Weapon":l==="ranged"?"Ranged Weapon":l==="quest"?"Task Item":l==="crafting"?"Material":l==="special"?"Special Item":ve(l);d.useEffect(()=>{i&&I(i)},[i]);const ae=[{headerName:"Item",field:"item",cellRenderer:B,cellClass:"items-center! flex!",minWidth:t?183:250,sortable:!1,autoHeight:!0,wrapText:!0,getQuickFilterText:l=>l.data.item.itemType,filter:"agTextColumnFilter",valueFormatter:l=>l.data.item,filterValueGetter:l=>l.data.item.name,filterParams:{filterOptions:["contains"],defaultOption:"contains"}},{headerName:t?"Qty":"Quantity",field:"count",headerClass:"centerGridHeader",maxWidth:120,cellClass:"items-center! justify-center! flex! font-semibold font-body text-base",filter:"agNumberColumnFilter",filterParams:{filterOptions:["equals","greaterThan","lessThan","inRange"],defaultOption:"equals"}},{headerName:"Category",field:"item.itemType",headerClass:"centerGridHeader",hide:t,cellClass:"md:text-base text-sm font-semibold text-center flex! items-center! justify-center! truncate",valueFormatter:l=>G(l.data.item.itemType)},{headerName:"Level",field:"item.level",hide:t,initialSortIndex:1,maxWidth:120,sort:"desc",cellClass:"items-center! justify-center! flex! font-semibold font-body text-base",headerClass:"centerGridHeader",filterParams:{filterOptions:["equals","greaterThan","lessThan","inRange"],defaultOption:"equals"}},{headerName:"Actions",field:"actions",sortable:!1,headerClass:"centerGridHeader",cellClass:"flex! items-center! justify-center!",cellRenderer:te,cellRendererParams:{equippedItems:r},valueFormatter:l=>l.data,filter:!1,floatingFilter:!1},{headerName:"CategoryHidden",field:"itemTypeFilter",hide:!0,filterParams:{filterOptions:[{displayKey:"includes",displayName:"Includes",predicate:([l],c)=>l.includes(c)}]},filterValueGetter:l=>l.data.item?.itemType}],[re,ne]=d.useState(ae);return d.useEffect(()=>{const l={force:!0,suppressFlash:!0,columns:["actions"]};k?.current?.api?.refreshCells(l),ne(c=>c.map(j=>j.field==="actions"?{...j,cellRendererParams:{equippedItems:r}}:j))},[r]),n?"An error has occurred: "+n.message:e.jsxs(e.Fragment,{children:[e.jsx(_e,{selectedTab:o,setSelectedTab:g}),e.jsx(De,{openModal:M,setOpenModal:v}),e.jsx(Pe,{open:x,setOpen:a,title:u.name,item:u,currentUser:f}),e.jsx("div",{className:"mb-8 md:mb-0 md:max-w-6xl 2xl:mx-auto",children:e.jsx(Ce,{keyProp:JSON.stringify(r),dataList:h,colDefs:re,isLoading:s,customGridRef:k})})]})}const _e=({selectedTab:r,setSelectedTab:t})=>{const s=i=>r===i,n=[{name:"All",current:s("All")},{name:"Weapons",current:s("Weapons")},{name:"Armor",current:s("Armor")},{name:"Consumables",current:s("Consumables")},{name:"Material",current:s("Material")},{name:"Misc",current:s("Misc")}];return e.jsxs("div",{children:[e.jsxs("div",{className:"mx-2 my-1 2xl:hidden",children:[e.jsx("label",{htmlFor:"tabs",className:"sr-only",children:"Select a tab"}),e.jsx("select",{id:"tabs",name:"tabs",className:"mb-3 block w-full rounded-md border-gray-300 px-2 text-stroke-md focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white",defaultValue:n.find(i=>i.current).name,onChange:i=>t(i.target.value),children:n.map(i=>e.jsx("option",{children:i.name},i.name))})]}),e.jsx("div",{className:"hidden 2xl:block",children:e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-200 border-gray-600 border-b shadow-sm dark:divide-gray-600","aria-label":"Tabs",children:n.map((i,o)=>e.jsxs("button",{"aria-current":i.current?"page":void 0,className:R(i.current?"text-gray-900":"text-gray-500 hover:text-gray-700",o===0?"rounded-tl-lg":"",o===n.length-1?"rounded-tr-lg":"","group relative min-w-0 flex-1 overflow-hidden bg-white p-4 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 dark:bg-gray-800 dark:text-white"),onClick:()=>t(i.name),children:[e.jsx("span",{children:i.name}),e.jsx("span",{"aria-hidden":"true",className:R(i.current?"bg-indigo-500":"bg-transparent","absolute inset-x-0 bottom-0 h-0.5")})]},i.name))})})]})};function Be(){const[r,t]=d.useState("/inventory"),[s,n]=d.useState("head"),i=H(!0),o=Ne(),g=Ie();d.useLayoutEffect(()=>{o.pathname&&o.pathname!==r&&t(o.pathname)},[o.pathname]);const{data:x,error:a,isLoading:u}=X(),{data:b}=z(m.user.getInventory.queryOptions()),{data:y}=z(m.user.getEquippedItems.queryOptions());if(u)return null;if(a)return"An error has occurred: "+a.message;const v=["weapon","offhand","ranged"].includes(s)?"damage":"armour",E=s==="shield"?"offhand":null,f=b?.filter(h=>h.item?.itemType===s||h.item?.itemType===E)?.sort((h,I)=>h.item[v]>I.item[v]?-1:h.item[v]<I.item[v]?1:0)||[];y&&y.shield&&(y.offhand=y.shield);const C=[{name:"/inventory",text:"Items",current:o.pathname==="/inventory"},{name:"/equipment",text:"Equipment",current:o.pathname==="/equipment"}];return e.jsxs("div",{className:"flex h-full flex-col gap-2 md:mx-auto md:max-w-208 2xl:max-w-(--breakpoint-xl) 2xl:flex-row",children:[e.jsxs("div",{children:[e.jsx(Me,{inventory:f,currentUser:x,equippedItems:y||[]}),e.jsx("div",{className:"block 2xl:hidden",children:e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-700 shadow-sm","aria-label":"Tabs",children:C.map((h,I)=>e.jsxs("a",{"aria-current":h.current?"page":void 0,className:R(h.current?"text-gray-900":"text-gray-500 hover:text-gray-700",I===C.length-1?"md:rounded-r-lg":"",I===0?"md:rounded-l-lg":"","group relative min-w-0 flex-1 cursor-pointer overflow-hidden bg-white p-4 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 dark:bg-gray-800 dark:text-slate-200"),onClick:()=>g(h.name),children:[e.jsx("span",{children:h.text}),e.jsx("span",{"aria-hidden":"true",className:R(h.current?"bg-indigo-500":"bg-transparent","absolute inset-x-0 bottom-0 h-0.5")})]},h.name))})})]}),typeof i=="number"&&i<=1536?r==="/inventory"?e.jsx(V,{currentUser:x,equippedItems:y}):e.jsx("div",{className:"flex flex-col gap-4",children:e.jsx(Y,{mobile:!0,equippedItems:y,setEquipTooltipFilter:n})}):e.jsxs("div",{className:"flex w-full gap-6",children:[e.jsx("div",{className:"2xl:mt-0 2xl:w-[75%]",children:e.jsx(V,{currentUser:x,equippedItems:y})}),e.jsx("div",{className:"2xl:w-[30%]",children:e.jsx(Y,{equippedItems:y,setEquipTooltipFilter:n})})]})]})}export{Be as default};
