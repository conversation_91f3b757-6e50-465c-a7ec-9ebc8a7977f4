/**
 * Comprehensive error handling for Firebase notifications
 */

import type { FirebaseError, FirebaseErrorCode } from "@/types/firebase";

// Error severity levels
export enum ErrorSeverity {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

// Error categories
export enum ErrorCategory {
    PERMISSION = 'permission',
    TOKEN = 'token',
    NETWORK = 'network',
    CONFIGURATION = 'configuration',
    SERVICE_WORKER = 'service_worker',
    UNKNOWN = 'unknown'
}

// Structured error interface
export interface NotificationError {
    code: string;
    message: string;
    category: ErrorCategory;
    severity: ErrorSeverity;
    timestamp: Date;
    context?: Record<string, any>;
    userAgent?: string;
    url?: string;
}

// Error mapping for Firebase errors
const FIREBASE_ERROR_MAP: Record<string, { category: ErrorCategory; severity: ErrorSeverity; userMessage: string }> = {
    'messaging/invalid-registration-token': {
        category: ErrorCategory.TOKEN,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Your notification token has expired. Please refresh the page to re-enable notifications.'
    },
    'messaging/registration-token-not-registered': {
        category: ErrorCategory.TOKEN,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Your device is not registered for notifications. Please enable notifications again.'
    },
    'messaging/internal-error': {
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.HIGH,
        userMessage: 'A temporary error occurred with notifications. Please try again later.'
    },
    'messaging/server-unavailable': {
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Notification service is temporarily unavailable. Please try again later.'
    },
    'messaging/quota-exceeded': {
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Too many notification requests. Please wait a moment before trying again.'
    },
    'messaging/invalid-argument': {
        category: ErrorCategory.CONFIGURATION,
        severity: ErrorSeverity.HIGH,
        userMessage: 'There was a configuration error with notifications. Please contact support.'
    },
    'messaging/third-party-auth-error': {
        category: ErrorCategory.CONFIGURATION,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Authentication error with notification service. Please contact support.'
    }
};

// Permission error mapping
const PERMISSION_ERROR_MAP: Record<string, { category: ErrorCategory; severity: ErrorSeverity; userMessage: string }> = {
    'denied': {
        category: ErrorCategory.PERMISSION,
        severity: ErrorSeverity.LOW,
        userMessage: 'Notifications are blocked. You can enable them in your browser settings.'
    },
    'default': {
        category: ErrorCategory.PERMISSION,
        severity: ErrorSeverity.LOW,
        userMessage: 'Please allow notifications to receive updates.'
    }
};

// Create structured error from Firebase error
export const createNotificationError = (
    error: any,
    context?: Record<string, any>
): NotificationError => {
    const timestamp = new Date();
    const userAgent = navigator.userAgent;
    const url = window.location.href;

    // Handle Firebase errors
    if (error?.code && FIREBASE_ERROR_MAP[error.code]) {
        const mapping = FIREBASE_ERROR_MAP[error.code];
        return {
            code: error.code,
            message: error.message || mapping.userMessage,
            category: mapping.category,
            severity: mapping.severity,
            timestamp,
            context,
            userAgent,
            url
        };
    }

    // Handle permission errors
    if (typeof error === 'string' && PERMISSION_ERROR_MAP[error]) {
        const mapping = PERMISSION_ERROR_MAP[error];
        return {
            code: `permission/${error}`,
            message: mapping.userMessage,
            category: mapping.category,
            severity: mapping.severity,
            timestamp,
            context,
            userAgent,
            url
        };
    }

    // Handle generic errors
    const message = error?.message || error?.toString() || 'Unknown notification error';
    return {
        code: 'unknown',
        message,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        timestamp,
        context,
        userAgent,
        url
    };
};

// Log error to console with structured format
export const logNotificationError = (error: NotificationError): void => {
    const logData = {
        timestamp: error.timestamp.toISOString(),
        code: error.code,
        message: error.message,
        category: error.category,
        severity: error.severity,
        context: error.context,
        userAgent: error.userAgent,
        url: error.url
    };

    switch (error.severity) {
        case ErrorSeverity.CRITICAL:
        case ErrorSeverity.HIGH:
            console.error('[Notification Error]', logData);
            break;
        case ErrorSeverity.MEDIUM:
            console.warn('[Notification Warning]', logData);
            break;
        case ErrorSeverity.LOW:
        default:
            console.info('[Notification Info]', logData);
            break;
    }
};

// Send error to monitoring service (placeholder for future implementation)
export const reportNotificationError = async (error: NotificationError): Promise<void> => {
    // TODO: Implement error reporting to monitoring service (e.g., Sentry, LogRocket)
    // For now, just log to console
    logNotificationError(error);
    
    // Example implementation:
    // if (window.Sentry) {
    //     window.Sentry.captureException(new Error(error.message), {
    //         tags: {
    //             category: error.category,
    //             severity: error.severity,
    //             code: error.code
    //         },
    //         extra: error.context
    //     });
    // }
};

// Get user-friendly error message
export const getUserFriendlyErrorMessage = (error: any): string => {
    const notificationError = createNotificationError(error);
    return notificationError.message;
};

// Check if error is recoverable
export const isRecoverableError = (error: NotificationError): boolean => {
    const recoverableCategories = [ErrorCategory.NETWORK, ErrorCategory.TOKEN];
    const recoverableCodes = [
        'messaging/internal-error',
        'messaging/server-unavailable',
        'messaging/quota-exceeded'
    ];
    
    return recoverableCategories.includes(error.category) || 
           recoverableCodes.includes(error.code);
};

// Get retry delay for recoverable errors
export const getRetryDelay = (error: NotificationError, attemptNumber: number): number => {
    if (!isRecoverableError(error)) return 0;
    
    // Exponential backoff with jitter
    const baseDelay = 1000; // 1 second
    const maxDelay = 30000; // 30 seconds
    const delay = Math.min(baseDelay * Math.pow(2, attemptNumber), maxDelay);
    const jitter = Math.random() * 0.1 * delay; // 10% jitter
    
    return delay + jitter;
};

// Error handler wrapper for async functions
export const withErrorHandling = <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    context?: Record<string, any>
) => {
    return async (...args: T): Promise<R | null> => {
        try {
            return await fn(...args);
        } catch (error) {
            const notificationError = createNotificationError(error, context);
            await reportNotificationError(notificationError);
            return null;
        }
    };
};
