import{a8 as t,ae as c,ba as E,r as x,ac as M,ad as z,a7 as N,bh as L,bN as F,bO as I,bL as G,ap as B,at as $,a5 as O,a6 as K,bM as P}from"./index-hZ2cjOe1.js";import{t as Q}from"./talentData-CZzZ7076.js";import{u as V}from"./useGetUserSkills-DZP4ur7i.js";function X(){return t.jsx("div",{className:"flex"})}const Y="/assets/locked-ly7i4j0n.png";function R({talent:e,setSelectedTalent:s,selectedTalent:l,isTalentDisabled:a}){const d={id:0,name:"LOCKED",image:Y,description:"This talent is not available in Alpha.",talentType:"locked"},n=i=>{l?.name==="LOCKED"?s():s(d)};return t.jsxs("div",{className:c(a&&"brightness-50 grayscale","relative m-auto flex h-3/4 drop-shadow-xl"),onClick:i=>n(),children:[t.jsx("img",{src:"https://cdn.battleacademy.io/static/talents/icons/lockBG.png",className:"m-auto max-h-full cursor-pointer",alt:""}),t.jsx("img",{className:"-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 m-auto max-h-[40%] cursor-pointer brightness-125",src:"https://cdn.battleacademy.io/static/talents/icons/lock.png",alt:""}),e?.connectedAbove&&t.jsx(H,{})]})}const H=()=>t.jsx("div",{className:"-translate-x-1/2 -top-13 absolute left-1/2 h-3/5 w-4 border-gray-600 border-x-2 bg-amber-300",children:t.jsx("div",{className:"-translate-x-1/2 -bottom-5 absolute left-1/2 inline-block w-7 overflow-hidden drop-shadow-lg",children:t.jsx("div",{className:"-rotate-45 size-5 origin-top-left border border-gray-600 bg-amber-300"})})});function J({talent:e,selectedTalent:s,setSelectedTalent:l,isTalentDisabled:a,talentUnlocked:d}){const n=i=>{s?.name===e.name?l():l(e)};return t.jsxs("div",{className:c(a&&"brightness-50 grayscale","relative m-auto flex h-[55%] w-auto cursor-pointer select-none shadow-2xl"),onClick:i=>n(),children:[e?.talentType==="passive"&&t.jsx("img",{src:E(e.image),alt:"",className:c("m-auto max-h-full rounded-md border-2 border-black ring-2 ring-blue-500",s?.name===e.name&&"contrast-125 saturate-150")}),e?.talentType==="ability"&&t.jsx("img",{src:E(e.image),alt:"",className:c("m-auto max-h-[105%] rounded-full border-2 border-black outline-solid outline-2 outline-slate-800 ring-4 ring-yellow-500",s?.name===e.name&&"contrast-125 saturate-150")}),d?.level!==e?.maxPoints&&t.jsx("div",{className:c("absolute h-[103%] w-full bg-gray-400 opacity-50",e?.talentType==="ability"?"rounded-full":"rounded-md")}),e?.talentType==="passive"&&t.jsx("div",{className:"-bottom-3 absolute left-[30%] flex h-5 w-8 rounded-md border border-slate-300 bg-slate-700 font-lili text-white text-xs tracking-widest md:h-6 md:w-10 md:text-sm",children:t.jsxs("p",{className:"m-auto",children:[d?d.level:0,"/",e?.maxPoints]})})]})}function W({talent:e,selectedTalent:s,setSelectedTalent:l,isTalentDisabled:a,showEmptySpaces:d,unlockedTalents:n}){const[i,f]=x.useState(null),o=()=>n?.find(m=>m?.talentId===e?.id)||null;return x.useEffect(()=>{f(o())},[n,e?.id]),e?.talentType==="locked"?t.jsx(R,{talent:e,selectedTalent:s,setSelectedTalent:l}):e?.talentType==="empty"?d?t.jsx(X,{}):t.jsx(R,{talent:e,selectedTalent:s,setSelectedTalent:l,isTalentDisabled:a}):e?.talentType==="passive"||e?.talentType==="ability"?t.jsx(J,{talent:e,selectedTalent:s,setSelectedTalent:l,isTalentDisabled:a,talentUnlocked:i}):null}function Z({selectedTalent:e,setSelectedTalent:s,treePoints:l,unlockedTalents:a,talentPoints:d,isTalentDisabled:n}){const i=M(),f=()=>e?.pointsInTreeRequired>(l||0)?e.pointsInTreeRequired-(l||0):0,o=()=>a?.find(p=>p?.talentId===e?.id)||null,[g,m]=x.useState(null);x.useEffect(()=>{m(o())},[a,e?.id]);const y=()=>!!(n||f()>0||(d||0)===0),k=()=>e?.talentType==="locked"?!1:g?.level===e?.maxPoints,w=z(N.talents.unlockTalent.mutationOptions({onSuccess:()=>{i.invalidateQueries({queryKey:N.talents.getUnlockedTalents.key()}),i.invalidateQueries({queryKey:N.user.getCurrentUserInfo.key()}),L.success("Talent Unlocked!")},onError:b=>{const p=b.message||"Unknown error occurred";console.error(p),L.error(p)}})),D=()=>{if(e?.disabled){L.error("This talent is temporarily disabled!");return}w.mutate({talentId:e?.id})};return t.jsx(F,{children:t.jsxs(I.div,{initial:{y:"100%"},animate:{y:0},transition:{ease:"easeIn",duration:.01},className:c(k()?"border-green-600":"border-blue-600","dark talentInfoModalAnim fixed inset-x-0 bottom-0 z-300 min-h-[20%] w-full transform-none overflow-y-auto border-2 bg-gray-800 px-4 pt-1 pb-4 text-stroke-sm transition-transform md:bottom-5 md:left-[31.5%] md:w-1/3"),children:[e.pointsCost&&e.pointsCost>0&&t.jsxs("div",{className:"absolute right-4 bottom-2 flex gap-2 text-3xl text-white",children:[t.jsx("span",{className:"font-accent text-purple-500",children:e.pointsCost}),t.jsx("img",{className:"inline h-10 w-auto",src:"https://cdn.battleacademy.io/static/talents/icons/talentpoints.png",alt:"Talent Points"})]}),t.jsxs("h5",{className:c("mt-0.5 mb-1 inline-flex items-center text-2xl uppercase",e?.talentType==="locked"?"text-gray-500":"text-indigo-600 dark:text-indigo-500"),children:[t.jsx(I.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:t.jsx("img",{alt:"",src:E(e?.image),className:c("mr-2.5 h-14 rounded-md text-gray-500 dark:text-gray-200",e?.talentType==="locked"?"w-10 brightness-90":"w-14")})},e?.image),t.jsxs("div",{children:[e?.talentType==="ability"&&t.jsx("p",{className:"text-orange-400 text-stroke-sm text-xs",children:"Ability"}),e?.talentType==="passive"&&t.jsx("p",{className:"text-sky-400 text-stroke-sm text-xs",children:"Passive"}),t.jsx("span",{className:"font-display font-bold",children:e?.displayName?e?.displayName:e?.name}),e?.maxPoints&&t.jsxs("span",{className:"ml-3 text-blue-500 dark:text-blue-300 font-mono font-bold",children:[g?g?.level:0,"/",e?.maxPoints]})]})]}),t.jsxs("button",{type:"button",className:"absolute top-2.5 right-2.5 inline-flex size-8 items-center justify-center rounded-lg bg-gray-600 text-slate-200 text-sm hover:bg-gray-200 hover:text-gray-900 md:bg-transparent dark:hover:bg-gray-600 dark:hover:text-white",onClick:()=>s(),children:[t.jsx("svg",{className:"size-3 ","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14",children:t.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"})}),t.jsx("span",{className:"sr-only",children:"Close menu"})]}),t.jsx("p",{className:"max-w-lg text-gray-500 text-sm dark:text-gray-200",children:typeof e?.description=="function"?e.description(e):e?.description}),e?.subDescription&&t.jsx("p",{className:"max-w-lg text-gray-500 text-sm dark:text-gray-300",children:typeof e?.subDescription=="function"?e.subDescription(e):e?.subDescription}),e?.staminaCost>0&&t.jsxs("p",{className:"max-w-lg text-orange-400 text-sm dark:text-orange-400",children:[e.staminaCost," STA"]}),e?.level1Desc&&t.jsxs("p",{className:"mt-2 max-w-lg text-gray-500 text-sm dark:text-gray-200",children:[t.jsx("span",{className:c("mr-2 font-display font-bold",g?.level>0?"text-green-600 dark:text-green-500":"text-blue-500"),children:"Level 1:"}),typeof e?.level1Desc=="function"?e.level1Desc(e):e?.level1Desc]}),e?.level2Desc&&t.jsxs("p",{className:"mt-2 max-w-lg text-gray-500 text-sm dark:text-gray-200",children:[t.jsx("span",{className:c("mr-2 font-display font-bold",g?.level>1?"text-green-600 dark:text-green-500":"text-blue-500"),children:"Level 2:"}),typeof e?.level2Desc=="function"?e.level2Desc(e):e?.level2Desc]}),e?.level3Desc&&t.jsxs("p",{className:"mt-2 max-w-lg text-gray-500 text-sm dark:text-gray-200",children:[t.jsx("span",{className:c("mr-2 font-display font-bold",g?.level>2?"text-green-600 dark:text-green-500":"text-blue-500"),children:"Level 3:"}),typeof e?.level3Desc=="function"?e.level3Desc(e):e?.level3Desc]}),e?.talentType!=="locked"&&!k()?t.jsxs("div",{className:"mt-6 flex w-full flex-col",children:[f()>0&&t.jsxs("p",{className:"-mt-3 mx-auto mb-2 text-red-700 text-sm",children:["Requires ",f()," more talent points spent in Strength"]}),t.jsx("button",{disabled:y(),className:c("font-display mx-auto rounded-lg border border-gray-900 px-4 py-2 text-center font-medium text-base text-stroke-sm! focus:outline-hidden focus:ring-4",y()?"bg-gray-500 text-gray-400":"bg-blue-700 text-white hover:bg-blue-800 focus:ring-blue-300 dark:bg-blue-600 dark:focus:ring-blue-800 dark:hover:bg-blue-700"),onClick:()=>y()?null:D(),children:"Purchase"})]}):null]})})}const _="/assets/leftarrow-DZyJmGgR.png",ee="/assets/rightarrow-DmLB-4mF.png",j=({disabled:e,link:s,onClick:l,type:a})=>t.jsx(B,{as:"button",to:e?"#":s||"#",onClick:()=>e?null:l?.(),children:t.jsx("div",{className:"squareBtnBlueBG relative flex size-14",children:t.jsx("img",{src:a==="left"?_:ee,alt:"",className:c("-translate-x-1/2 -translate-y-1/2 absolute top-[40%] left-[45%] h-7 w-5",e&&"opacity-75 brightness-50")})})});function te({tabs:e,setSelectedTalent:s,talentPoints:l,currentTreePoints:a,isTalentViewer:d=!1}){return t.jsxs("ul",{className:c(d?"rounded-t-xl":"md:rounded-t-xl","flex h-26 select-none flex-col border-white/10 border-b bg-gray-400 px-6 pt-4 dark:bg-gray-900"),children:[t.jsx("li",{className:"flex min-w-full flex-none select-none justify-between gap-x-6 px-2 text-gray-400 text-sm leading-6",children:e.map(n=>n.current?t.jsxs(x.Fragment,{children:[n.leftLink?t.jsx(j,{link:n.leftLink,type:"left",onClick:s}):t.jsx(j,{disabled:!0,type:"left"}),t.jsx("div",{className:"mt-1 flex h-fit w-full rounded-lg bg-blue-900 py-2 text-center shadow-2xl",children:t.jsxs("a",{href:n.href,className:"m-auto font-accent text-gray-200 text-lg text-stroke-s-md",children:[t.jsx(G,{children:n.name})," ",t.jsxs("span",{className:"align-middle font-lili text-base tracking-wider",children:["(",a||0,")"]})]})},n.name),n.rightLink?t.jsx(j,{link:n.rightLink,type:"right",onClick:s}):t.jsx(j,{disabled:!0,type:"right"})]},n.name):null)}),t.jsxs("p",{className:"mx-auto flex gap-1 text-lg dark:text-slate-200",children:[l||0," ",t.jsx("img",{className:"mt-0.5 size-6",src:"https://cdn.battleacademy.io/static/talents/icons/talentpoints.png",alt:""})]})]})}const C={"/talents/strength":{long:"strength",short:"STR"},"/talents/dexterity":{long:"dexterity",short:"DEX"},"/talents/defence":{long:"defence",short:"DEF"},"/talents/intelligence":{long:"intelligence",short:"INT"},"/talents/endurance":{long:"endurance",short:"END"},"/talents/vitality":{long:"vitality",short:"VIT"}},T=[{tier:"0",statReq:0,className:"",indexes:[0,1,2]},{tier:"1",statReq:5,className:"topDivider",indexes:[3,4,5]},{tier:"2",statReq:10,className:"secondDivider",indexes:[6,7,8]},{tier:"3",statReq:20,className:"thirdDivider",indexes:[9,10,11]},{tier:"4",statReq:40,className:"fourthDivider",indexes:[12,13,14]}],se=!1,re=!1;function ie({pathname:e}){const[s,l]=x.useState(),[a,d]=x.useState(),{data:n}=$(),{data:i}=V(),f=O(),o=C[e]?.long,g=C[e]?.short,m=r=>e===`/talents/${r}`,y=[{name:"STRENGTH",leftLink:null,rightLink:"dexterity",current:m("strength")},{name:"DEXTERITY",leftLink:"strength",rightLink:"defence",current:m("dexterity")},{name:"DEFENCE",leftLink:"dexterity",rightLink:"intelligence",current:m("defence")},{name:"INTELLIGENCE",leftLink:"defence",rightLink:"endurance",current:m("intelligence")},{name:"ENDURANCE",leftLink:"intelligence",rightLink:"vitality",current:m("endurance")},{name:"VITALITY",leftLink:"endurance",rightLink:null,current:m("vitality")}];x.useEffect(()=>{e&&!C[e]&&f("/talents");const r=o;r!==s&&l(r)},[e,o,s,f]);const k=x.useCallback(r=>s?r.filter(h=>h.tree===s).map(h=>{const A=Q[h.name];return{...h,...A||{}}}):null,[s]),w=x.useCallback(r=>!s||!r?null:r.talentList?.filter(h=>h.talentInfo.tree===s),[s]),{data:D}=K(N.talents.getTalents.queryOptions({staleTime:1/0,select:k})),{data:b}=P(),{data:p}=P({select:w}),S=new Array(15).fill({talentType:"empty"});D?.forEach(r=>{if(r.position!==void 0){const u=r.position-1;S[u]=r}});const q=r=>{for(const u of T)if(u.indexes.includes(r))return i&&o?(i[o]?.level||1)<u.statReq:!0;return!1},U=()=>!i||!o||!a||(i[o]?.level||1)<a.skillLevelRequired;return t.jsx("div",{className:"mx-auto h-[calc(100dvh-9.5rem)] md:h-[75dvh] md:max-w-4xl md:rounded-xl md:border-2 md:border-gray-600 md:bg-gray-200 md:dark:bg-gray-800",children:t.jsxs("div",{className:"h-full flex-1 flex-col",children:[t.jsx(te,{tabs:y,setSelectedTalent:d,talentPoints:n?.talentPoints,currentTreePoints:b?.treePoints?.[o]}),t.jsx(I.span,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.5},children:t.jsxs("div",{className:"relative grid h-[calc(100%-6rem)] w-full grid-cols-3 grid-rows-5 gap-4 md:h-[calc(100%-120px)]",children:[T.map(({tier:r,statReq:u,className:v})=>t.jsx("div",{className:c(`absolute w-full ${v} flex shadow-2xl`,i&&o&&u<=(i[o]?.level||1)&&"brightness-75",r!=="0"&&"h-1 bg-slate-700"),children:i&&o&&u>(i[o]?.level||1)?t.jsxs(t.Fragment,{children:[r!=="0"&&t.jsx("img",{className:"-translate-x-1/2 -top-3 md:-top-5 absolute left-1/2 h-6 w-auto bg-[#f8f8fb] px-2 md:h-10 md:bg-gray-200 md:px-3 dark:bg-[#111521] md:dark:bg-gray-800",src:"https://cdn.battleacademy.io/static/talents/icons/lock.png",alt:""}),t.jsxs("p",{className:c(r!=="0"?"mt-1":"mt-0.5","z-20 ml-2 text-gray-900 text-shadow-s-md text-sm dark:text-gray-300"),children:["Requires ",u," ",g]})]}):null},r)),S.map((r,u)=>t.jsx("div",{className:"relative flex size-full",children:t.jsx(W,{talent:r,selectedTalent:a,setSelectedTalent:d,isTalentDisabled:q(u),showEmptySpaces:re,unlockedTalents:p})},u)),a&&t.jsx(Z,{selectedTalent:a,setSelectedTalent:d,treePoints:b?.treePoints?.[o],unlockedTalents:p,talentPoints:n?.talentPoints,isTalentDisabled:U()})]})},s),se]})})}export{ie as default};
