import{r as n,a6 as x,a7 as u,at as g,a8 as e,ap as m,aD as p,ao as h,c_ as j}from"./index-hZ2cjOe1.js";import{V as f}from"./ViewGangModal-C5BcUNfn.js";function y(){const[r,s]=n.useState(!1),[l,t]=n.useState(null),{data:o,isLoading:c}=x(u.gang.getGangList.queryOptions()),{data:i}=g(),d=a=>{t(a),s(!0)};return e.jsx(e.Fragment,{children:e.jsx("section",{className:"mx-auto rounded-lg bg-gray-100 py-3 md:max-w-3xl dark:bg-gray-800",children:e.jsxs("div",{className:"flex flex-col gap-2 p-1.5",children:[e.jsxs("div",{className:"relative mb-6 text-center text-2xl text-custom-yellow",children:[e.jsxs(m,{to:-1,children:[" ",e.jsx(p,{className:"!absolute left-0! h-10!",children:"Back"})]}),e.jsx("p",{children:"Gang List"})]}),e.jsxs(h,{isLoading:c,children:[e.jsx(f,{open:r,setOpen:s,selectedGang:l,setSelectedGang:t,currentUser:i}),o?.map(a=>e.jsx(j,{gang:a,className:"h-24",onClick:()=>d(a)},a.id))]})]})})})}export{y as default};
