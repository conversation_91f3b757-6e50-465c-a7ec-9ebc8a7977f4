import { z } from "zod";
import { DropChanceTypes, LocationTypes } from "@prisma/client";

// Schema for creating a new drop chance
const createDropChanceSchema = z.object({
    itemId: z.number().int().positive(),
    dropRate: z.number().min(0).max(1),
    dropChanceType: z.nativeEnum(DropChanceTypes),
    location: z.nativeEnum(LocationTypes).optional(),
    minLevel: z.number().int().min(0).optional(),
    maxLevel: z.number().int().min(0).optional(),
    scavengeType: z.string().optional(),
    quantity: z.number().int().min(1).optional(),
    creatureId: z.number().int().positive().optional(),
});

// Schema for editing an existing drop chance
const editDropChanceSchema = z.object({
    id: z.number().int().positive(),
    itemId: z.number().int().positive(),
    dropRate: z.number().min(0).max(1),
    dropChanceType: z.nativeEnum(DropChanceTypes),
    location: z.nativeEnum(LocationTypes).optional(),
    minLevel: z.number().int().min(0).optional(),
    maxLevel: z.number().int().min(0).optional(),
    scavengeType: z.string().optional(),
    quantity: z.number().int().min(1).optional(),
    creatureId: z.number().int().positive().optional(),
});

// Schema for deleting a drop chance
const deleteDropChanceSchema = z.object({
    id: z.number().int().positive(),
});

const dropChanceSchema = {
    create: createDropChanceSchema,
    edit: editDropChanceSchema,
    delete: deleteDropChanceSchema,
};

export default dropChanceSchema;
