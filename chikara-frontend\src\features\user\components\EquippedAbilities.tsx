/* eslint-disable no-use-before-define */
import useGetEquippedAbilities from "@/features/talents/api/useGetEquippedAbilities";
import useGetUnlockedTalents from "@/features/talents/api/useGetUnlockedTalents";
import useEquipAbility from "@/features/talents/api/useEquipAbility";
import useUnequipAbility from "@/features/talents/api/useUnequipAbility";
import { talentData } from "@/features/talents/data/talentData";
import type { TalentNameType, UserTalent, Ability } from "@/features/talents/types/talents";
import { cn } from "@/lib/utils";
import { Zap, X } from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";

export default function EquippedAbilities() {
    const { data: equippedAbilities, isLoading } = useGetEquippedAbilities();
    const [selectedSlot, setSelectedSlot] = useState<number | null>(null);

    if (isLoading) {
        return (
            <div className="bg-gray-800/50 rounded-lg p-4 border border-purple-900/30">
                <h3 className="text-white mb-4 text-lg font-semibold">Equipped Abilities</h3>
                <div className="text-center text-gray-400">Loading abilities...</div>
            </div>
        );
    }

    return (
        <>
            <div className="bg-gray-800/50 rounded-lg p-4 border border-purple-900/30">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white text-lg font-semibold flex items-center gap-2">
                        <Zap className="size-5 text-purple-400" />
                        Equipped Abilities
                    </h3>
                    <Link to="/abilities" className="text-purple-400 hover:text-purple-300 text-sm transition-colors">
                        Manage →
                    </Link>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {[0, 1, 2, 3].map((index) => {
                        const ability = equippedAbilities?.[index];
                        return (
                            <AbilitySlot
                                key={index}
                                ability={ability}
                                slotNumber={index + 1}
                                onClick={() => setSelectedSlot(index + 1)}
                            />
                        );
                    })}
                </div>

                {equippedAbilities?.some((ability) => ability) && (
                    <div className="mt-4 pt-3 border-t border-gray-700">
                        <div className="text-xs text-gray-400 text-center">
                            Use abilities in battle to gain tactical advantages
                        </div>
                    </div>
                )}
            </div>

            {selectedSlot && (
                <AbilitySelectionModal
                    slot={selectedSlot as 1 | 2 | 3 | 4}
                    currentAbility={equippedAbilities?.[selectedSlot - 1]}
                    onClose={() => setSelectedSlot(null)}
                />
            )}
        </>
    );
}

interface AbilitySlotProps {
    ability: Ability | null | undefined;
    slotNumber: number;
    onClick: () => void;
}

function AbilitySlot({ ability, slotNumber, onClick }: AbilitySlotProps) {
    if (!ability) {
        return (
            <button
                className="bg-gray-900/50 rounded-lg p-3 border-2 border-dashed border-gray-700 aspect-square flex flex-col items-center justify-center hover:border-purple-700/50 hover:bg-gray-900/70 transition-all cursor-pointer"
                onClick={onClick}
            >
                <div className="text-gray-400 text-xs mb-1">Slot {slotNumber}</div>
                <div className="text-gray-500 text-xs text-center">Empty</div>
            </button>
        );
    }

    const talentInfo = ability?.name ? talentData[ability.name as TalentNameType] : undefined;
    const abilityImage = talentInfo?.image || ability?.image;

    return (
        <button
            className="bg-gray-900/50 rounded-lg p-2 border border-purple-900/30 hover:border-purple-700/50 transition-colors group cursor-pointer"
            onClick={onClick}
        >
            <div className="text-xs text-gray-400 mb-1 text-center">Slot {slotNumber}</div>
            <div className="aspect-square relative">
                {abilityImage ? (
                    <img src={abilityImage} alt={ability.name} className="w-full h-full object-cover rounded-md" />
                ) : (
                    <div className="w-full h-full bg-purple-900/30 rounded-md flex items-center justify-center">
                        <Zap className="size-6 text-purple-400" />
                    </div>
                )}

                {/* Ability name overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-black/70 text-white text-xs p-1 rounded-b-md">
                    <div className="truncate text-center">{ability.displayName}</div>
                </div>
            </div>

            {/* Stamina cost */}
            {ability.staminaCost && (
                <div className="mt-1 text-center">
                    <span className="text-xs text-blue-400">{ability.staminaCost} Stamina</span>
                </div>
            )}
        </button>
    );
}

interface AbilitySelectionModalProps {
    slot: 1 | 2 | 3 | 4;
    onClose: () => void;
    currentAbility: Ability | null | undefined;
}

function AbilitySelectionModal({ slot, onClose, currentAbility }: AbilitySelectionModalProps) {
    const { data: unlockedTalents, isLoading } = useGetUnlockedTalents();
    const { data: equippedAbilities } = useGetEquippedAbilities();
    const { mutate: equipAbility, isPending: isEquipping } = useEquipAbility({
        onClose,
        successMessage: undefined, // Let the hook use default message
    });
    const { mutate: unequipAbility, isPending: isUnequipping } = useUnequipAbility({
        onClose,
        successMessage: undefined, // Let the hook use default message
    });

    // Filter combat abilities, ensuring they are not already equipped in another slot
    const combatAbilities = unlockedTalents?.talentList?.filter((talent) => {
        const isCombatAbility = talent.talentInfo.staminaCost && talent.talentInfo.staminaCost > 0;
        const isAlreadyEquippedInOtherSlot = equippedAbilities?.some(
            (ability) => ability?.id === talent.talentInfo.id && ability?.id !== currentAbility?.id
        );
        return isCombatAbility && !isAlreadyEquippedInOtherSlot;
    });

    const handleSelectAbility = (talent: UserTalent) => {
        equipAbility({ talentId: talent.talentInfo.id, slot });
    };

    const handleUnequip = () => {
        unequipAbility({ slot });
    };

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-gray-900 rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden border border-purple-900/30">
                <div className="flex items-center justify-between p-4 border-b border-gray-800">
                    <h3 className="text-xl font-semibold text-white">Select Ability for Slot {slot}</h3>
                    <button
                        className="text-gray-400 hover:text-white transition-colors"
                        disabled={isEquipping}
                        onClick={onClose}
                    >
                        <X className="size-5" />
                    </button>
                </div>

                <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
                    {isLoading ? (
                        <div className="text-center text-gray-400 py-8">Loading abilities...</div>
                    ) : !combatAbilities || combatAbilities.length === 0 ? (
                        <div className="text-center text-gray-400 py-8">
                            <p>No combat abilities unlocked yet.</p>
                            <p className="text-sm mt-2">Visit the talents page to unlock abilities.</p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            {/* Option to unequip current ability */}
                            {currentAbility && (
                                <button
                                    disabled={isUnequipping}
                                    className="bg-gray-800/50 rounded-lg p-4 border transition-all text-left cursor-pointer border-red-700 hover:bg-gray-800/70 hover:border-red-500"
                                    onClick={handleUnequip}
                                >
                                    <div className="flex gap-3">
                                        <div className="w-16 h-16 flex-shrink-0 flex items-center justify-center bg-red-900/30 rounded-md">
                                            <X className="size-8 text-red-400" />
                                        </div>
                                        <div className="flex-1">
                                            <h4 className="text-white font-medium mb-1">Unequip Ability</h4>
                                            <p className="text-gray-400 text-sm mb-2">
                                                Remove the ability from this slot
                                            </p>
                                        </div>
                                    </div>
                                </button>
                            )}

                            {combatAbilities.map((talent) => {
                                const isCurrentlyEquipped = currentAbility?.id === talent.talentInfo.id;
                                const talentImage =
                                    talentData[talent.talentInfo.name as TalentNameType]?.image ||
                                    talent.talentInfo.image;

                                return (
                                    <button
                                        key={talent.talentInfo.id}
                                        disabled={isEquipping || isCurrentlyEquipped}
                                        className={cn(
                                            "bg-gray-800/50 rounded-lg p-4 border transition-all text-left cursor-pointer",
                                            isCurrentlyEquipped
                                                ? "border-green-700 bg-green-900/20 cursor-not-allowed"
                                                : "border-gray-700 hover:border-purple-700 hover:bg-gray-800/70"
                                        )}
                                        onClick={() => handleSelectAbility(talent)}
                                    >
                                        <div className="flex gap-3">
                                            <div className="w-16 h-16 flex-shrink-0">
                                                {talentImage ? (
                                                    <img
                                                        src={talentImage}
                                                        alt={talent.talentInfo.displayName}
                                                        className="w-full h-full object-cover rounded-md"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full bg-purple-900/30 rounded-md flex items-center justify-center">
                                                        <Zap className="size-8 text-purple-400" />
                                                    </div>
                                                )}
                                            </div>
                                            <div className="flex-1">
                                                <h4 className="text-white font-medium mb-1">
                                                    {talent.talentInfo.displayName}
                                                    {isCurrentlyEquipped && (
                                                        <span className="text-xs text-green-400 ml-2">(Equipped)</span>
                                                    )}
                                                </h4>
                                                <p className="text-gray-400 text-sm mb-2">
                                                    {talent.talentInfo.description || "No description available"}
                                                </p>
                                                <div className="flex items-center gap-4 text-xs">
                                                    <span className="text-blue-400">
                                                        {talent.talentInfo.staminaCost} Stamina
                                                    </span>
                                                    <span className="text-gray-500">
                                                        Level {talent.level}/{talent.talentInfo.maxPoints}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </button>
                                );
                            })}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
