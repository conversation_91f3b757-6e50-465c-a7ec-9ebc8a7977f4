/**
 * Shared Firebase notification types between frontend and backend
 */

// Firebase notification payload structure
export interface FirebaseNotificationPayload {
    title: string;
    body: string;
    icon?: string;
    image?: string;
    clickAction?: string;
    data?: Record<string, string>;
}

// Notification permission states
export type NotificationPermission = "granted" | "denied" | "default";

// Token management interfaces
export interface TokenValidationResult {
    isValid: boolean;
    error?: string;
}

export interface TokenServerResponse {
    success: boolean;
    data?: any;
    error?: string;
    statusCode?: number;
}

export interface NotificationPermissionResult {
    success: boolean;
    permission: NotificationPermission;
    token?: string;
    error?: string;
}

// Push notification result interfaces
export interface NotificationResult {
    success: boolean;
    messageId?: string;
    error?: string;
    token?: string;
}

export interface BatchNotificationResult {
    totalSent: number;
    totalFailed: number;
    results: NotificationResult[];
    invalidTokens: string[];
}

// Firebase configuration interface
export interface FirebaseClientConfig {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    vapidKey: string;
}

// Service worker message types
export interface ServiceWorkerMessage {
    type: 'NOTIFICATION_RECEIVED' | 'TOKEN_REFRESH' | 'PERMISSION_CHANGED';
    payload?: any;
}

// Notification action types for service worker
export interface NotificationAction {
    action: string;
    title: string;
    icon?: string;
}

export interface NotificationOptions {
    body?: string;
    icon?: string;
    badge?: string;
    image?: string;
    tag?: string;
    requireInteraction?: boolean;
    silent?: boolean;
    data?: any;
    actions?: NotificationAction[];
    timestamp?: number;
}

// Error types for better error handling
export enum FirebaseErrorCode {
    INVALID_TOKEN = 'messaging/invalid-registration-token',
    TOKEN_NOT_REGISTERED = 'messaging/registration-token-not-registered',
    INTERNAL_ERROR = 'messaging/internal-error',
    SERVER_UNAVAILABLE = 'messaging/server-unavailable',
    QUOTA_EXCEEDED = 'messaging/quota-exceeded',
    INVALID_ARGUMENT = 'messaging/invalid-argument',
    THIRD_PARTY_AUTH_ERROR = 'messaging/third-party-auth-error'
}

export interface FirebaseError {
    code: FirebaseErrorCode | string;
    message: string;
    details?: any;
}

// Token cleanup interface
export interface TokenCleanupResult {
    removedCount: number;
    errors: string[];
}

// Notification delivery status
export enum NotificationDeliveryStatus {
    PENDING = 'pending',
    SENT = 'sent',
    DELIVERED = 'delivered',
    FAILED = 'failed',
    EXPIRED = 'expired'
}

export interface NotificationDeliveryRecord {
    id: string;
    userId: number;
    token: string;
    status: NotificationDeliveryStatus;
    messageId?: string;
    error?: string;
    sentAt?: Date;
    deliveredAt?: Date;
    retryCount: number;
    maxRetries: number;
}
