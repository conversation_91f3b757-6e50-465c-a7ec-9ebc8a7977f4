# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/dist

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local


npm-debug.log*
yarn-debug.log*
yarn-error.log*

/certs

/logs

.cursor/mcp.json
.tsbuildinfo
.vscode
.env
.idea
.repomix
repomix-output.txt
.early.coverage
src/data/prisma

fullcode.txt

prisma/seeders/alpha2
prisma/seeders/latest
prisma/seeders/old.ts

/.xata