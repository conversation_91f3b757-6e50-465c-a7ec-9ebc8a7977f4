import { beforeEach, describe, expect, it, vi } from "vitest";
import type { ExploreNodeLocation, TravelMethod } from "@prisma/client";
import { db } from "../../../lib/db.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";

const mockedDb = vi.mocked(db);

describe("ExploreRepository - Travel Status Functions", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("Race condition prevention", () => {
        it("should handle concurrent travel completion calls atomically", async () => {
            // Arrange
            const userId = 1;
            const now = new Date();
            const travelEndTime = new Date(now.getTime() - 1000); // Travel ended 1 second ago
            const mockUser = {
                id: userId,
                currentMapLocation: "shinjuku" as ExploreNodeLocation,
                travelStartTime: new Date(now.getTime() - 60000), // Started 1 minute ago
                travelEndTime,
                travelMethod: "walking" as TravelMethod,
            };

            // Mock the transaction for autoCompleteTravelIfNeeded
            let callCount = 0;
            mockedDb.$transaction.mockImplementation(async (callback) => {
                callCount++;
                return await callback({
                    user: {
                        updateMany: vi.fn().mockResolvedValue({
                            count: callCount === 1 ? 1 : 0, // First call succeeds, second fails
                        }),
                    },
                } as any);
            });

            // Act - Test the autoCompleteTravelIfNeeded function directly
            const [result1, result2] = await Promise.all([
                ExploreRepository.autoCompleteTravelIfNeeded(mockUser as any),
                ExploreRepository.autoCompleteTravelIfNeeded(mockUser as any),
            ]);

            // Assert
            expect(mockedDb.$transaction).toHaveBeenCalledTimes(2);

            // One call should have auto-completed the travel
            const autoCompletedResults = [result1, result2].filter((r) => r.autoCompleted);
            const nonAutoCompletedResults = [result1, result2].filter((r) => !r.autoCompleted);

            expect(autoCompletedResults).toHaveLength(1);
            expect(nonAutoCompletedResults).toHaveLength(1);

            // The auto-completed result should have the destination
            expect(autoCompletedResults[0].completedDestination).toBe("shinjuku");

            // The non-auto-completed result should not have the destination
            expect(nonAutoCompletedResults[0].completedDestination).toBeUndefined();
        });

        it("should use conditional update to prevent duplicate completion", async () => {
            // Arrange
            const userId = 1;
            const now = new Date();
            const travelEndTime = new Date(now.getTime() - 1000);
            const mockUser = {
                id: userId,
                currentMapLocation: "shinjuku" as ExploreNodeLocation,
                travelStartTime: new Date(now.getTime() - 60000),
                travelEndTime,
                travelMethod: "walking" as TravelMethod,
            };

            const mockTx = {
                user: {
                    updateMany: vi.fn().mockResolvedValue({ count: 1 }),
                },
            };

            mockedDb.$transaction.mockImplementation(async (callback) => {
                return await callback(mockTx as any);
            });

            // Act
            const result = await ExploreRepository.autoCompleteTravelIfNeeded(mockUser as any);

            // Assert - Check that updateMany was called with the correct conditions
            expect(mockTx.user.updateMany).toHaveBeenCalledWith({
                where: {
                    id: userId,
                    travelEndTime: {
                        not: null,
                        lte: expect.any(Date),
                    },
                },
                data: {
                    travelStartTime: null,
                    travelEndTime: null,
                    travelMethod: null,
                },
            });

            expect(result.autoCompleted).toBe(true);
            expect(result.completedDestination).toBe("shinjuku");
        });
    });

    describe("Normal travel status scenarios", () => {
        it("should return active travel status when user is still traveling", async () => {
            // Arrange
            const userId = 1;
            const now = new Date();
            const travelEndTime = new Date(now.getTime() + 60000); // Travel ends in 1 minute
            const mockUser = {
                currentMapLocation: "shinjuku" as ExploreNodeLocation,
                travelStartTime: new Date(now.getTime() - 30000), // Started 30 seconds ago
                travelEndTime,
                travelMethod: "walking" as TravelMethod,
            };

            mockedDb.user.findUnique.mockResolvedValue(mockUser);

            // Act
            const result = await ExploreRepository.getUserTravelStatus(userId);

            // Assert
            expect(result.isTravel).toBe(true);
            expect(result.travelingTo).toBe("shinjuku");
            expect(result.travelStartTime).toEqual(mockUser.travelStartTime);
            expect(result.travelEndTime).toEqual(mockUser.travelEndTime);
            expect(result.travelMethod).toBe("walking");
            expect(result.remainingTime).toBeGreaterThan(0);
        });

        it("should return non-travel status when user is not traveling", async () => {
            // Arrange
            const userId = 1;
            const mockUser = {
                currentMapLocation: "shinjuku" as ExploreNodeLocation,
                travelStartTime: null,
                travelEndTime: null,
                travelMethod: null,
            };

            mockedDb.user.findUnique.mockResolvedValue(mockUser);

            // Act
            const result = await ExploreRepository.getUserTravelStatus(userId);

            // Assert
            expect(result.isTravel).toBe(false);
            expect(result.travelingTo).toBeUndefined();
            expect(result.travelStartTime).toBeUndefined();
            expect(result.travelEndTime).toBeUndefined();
            expect(result.travelMethod).toBeUndefined();
            expect(result.remainingTime).toBeUndefined();
        });
    });

    describe("getUserTravelStatus", () => {
        it("should return travel status correctly", async () => {
            // Arrange
            const userId = 1;
            const now = new Date();
            const travelEndTime = new Date(now.getTime() + 60000); // Travel ends in 1 minute
            const mockUser = {
                currentMapLocation: "shinjuku" as ExploreNodeLocation,
                travelStartTime: new Date(now.getTime() - 30000), // Started 30 seconds ago
                travelEndTime,
                travelMethod: "walking" as TravelMethod,
            };

            mockedDb.user.findUnique.mockResolvedValue(mockUser);

            // Act
            const result = await ExploreRepository.getUserTravelStatus(userId);

            // Assert
            expect(result.isTravel).toBe(true);
            expect(result.travelingTo).toBe("shinjuku");
            expect(result.travelStartTime).toEqual(mockUser.travelStartTime);
            expect(result.travelEndTime).toEqual(mockUser.travelEndTime);
            expect(result.travelMethod).toBe("walking");
            expect(result.remainingTime).toBeGreaterThan(0);
        });

        it("should return non-travel status when user is not traveling", async () => {
            // Arrange
            const userId = 1;
            const mockUser = {
                currentMapLocation: "shinjuku" as ExploreNodeLocation,
                travelStartTime: null,
                travelEndTime: null,
                travelMethod: null,
            };

            mockedDb.user.findUnique.mockResolvedValue(mockUser);

            // Act
            const result = await ExploreRepository.getUserTravelStatus(userId);

            // Assert
            expect(result.isTravel).toBe(false);
            expect(result.travelingTo).toBeUndefined();
            expect(result.travelStartTime).toBeUndefined();
            expect(result.travelEndTime).toBeUndefined();
            expect(result.travelMethod).toBeUndefined();
            expect(result.remainingTime).toBeUndefined();
        });
    });

    describe("autoCompleteTravelIfNeeded", () => {
        it("should auto-complete travel when time has elapsed", async () => {
            // Arrange
            const userId = 1;
            const now = new Date();
            const travelEndTime = new Date(now.getTime() - 1000); // Travel ended 1 second ago
            const mockUser = {
                id: userId,
                currentMapLocation: "shinjuku" as ExploreNodeLocation,
                travelStartTime: new Date(now.getTime() - 60000), // Started 1 minute ago
                travelEndTime,
                travelMethod: "walking" as TravelMethod,
            };

            const mockTx = {
                user: {
                    updateMany: vi.fn().mockResolvedValue({ count: 1 }),
                },
            };

            mockedDb.$transaction.mockImplementation(async (callback) => {
                return await callback(mockTx as any);
            });

            // Act
            const result = await ExploreRepository.autoCompleteTravelIfNeeded(mockUser as any);

            // Assert
            expect(result.autoCompleted).toBe(true);
            expect(result.completedDestination).toBe("shinjuku");

            // Should have called updateMany to clear travel fields
            expect(mockTx.user.updateMany).toHaveBeenCalledWith({
                where: {
                    id: userId,
                    travelEndTime: {
                        not: null,
                        lte: expect.any(Date),
                    },
                },
                data: {
                    travelStartTime: null,
                    travelEndTime: null,
                    travelMethod: null,
                },
            });
        });

        it("should not auto-complete when travel is still active", async () => {
            // Arrange
            const userId = 1;
            const now = new Date();
            const travelEndTime = new Date(now.getTime() + 60000); // Travel ends in 1 minute
            const mockUser = {
                id: userId,
                currentMapLocation: "shinjuku" as ExploreNodeLocation,
                travelStartTime: new Date(now.getTime() - 30000), // Started 30 seconds ago
                travelEndTime,
                travelMethod: "walking" as TravelMethod,
            };

            const mockTx = {
                user: {
                    updateMany: vi.fn(),
                },
            };

            mockedDb.$transaction.mockImplementation(async (callback) => {
                return await callback(mockTx as any);
            });

            // Act
            const result = await ExploreRepository.autoCompleteTravelIfNeeded(mockUser as any);

            // Assert
            expect(result.autoCompleted).toBe(false);
            expect(result.completedDestination).toBeUndefined();

            // Should not have called updateMany since travel is still active
            expect(mockTx.user.updateMany).not.toHaveBeenCalled();
        });

        it("should not auto-complete when user is not traveling", async () => {
            // Arrange
            const userId = 1;
            const mockUser = {
                id: userId,
                currentMapLocation: "shinjuku" as ExploreNodeLocation,
                travelStartTime: null,
                travelEndTime: null,
                travelMethod: null,
            };

            const mockTx = {
                user: {
                    updateMany: vi.fn(),
                },
            };

            mockedDb.$transaction.mockImplementation(async (callback) => {
                return await callback(mockTx as any);
            });

            // Act
            const result = await ExploreRepository.autoCompleteTravelIfNeeded(mockUser as any);

            // Assert
            expect(result.autoCompleted).toBe(false);
            expect(result.completedDestination).toBeUndefined();

            // Should not have called updateMany since user is not traveling
            expect(mockTx.user.updateMany).not.toHaveBeenCalled();
        });
    });
});
