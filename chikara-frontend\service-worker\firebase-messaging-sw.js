import { initializeApp } from "firebase/app";
import { getMessaging, onBackgroundMessage } from "firebase/messaging/sw";
import { clientsClaim } from "workbox-core";
import { precacheAndRoute } from "workbox-precaching";

precacheAndRoute(self.__WB_MANIFEST);

// Firebase configuration - should match the main app configuration
// In a production environment, these should come from environment variables
const firebaseConfig = {
    apiKey: "AIzaSyCQ-QeWMrBZw8v9idybo_cFwAIlDO6j-z0",
    authDomain: "chikara-academy.firebaseapp.com",
    projectId: "chikara-academy",
    storageBucket: "chikara-academy.appspot.com",
    messagingSenderId: "75175802639",
    appId: "1:75175802639:web:a1cc5e6073f185ec46707a",
};

// Initialize Firebase app and messaging
let firebaseApp;
let messaging;

try {
    firebaseApp = initializeApp(firebaseConfig);
    messaging = getMessaging(firebaseApp);
    console.log("[firebase-messaging-sw.js] Firebase initialized successfully");
} catch (error) {
    console.error("[firebase-messaging-sw.js] Failed to initialize Firebase:", error);
}

// Enhanced background message handling
onBackgroundMessage(messaging, (payload) => {
    console.log("[firebase-messaging-sw.js] Received background message:", payload);

    try {
        // Extract notification data
        const notificationTitle = payload.notification?.title || payload.data?.title || "Chikara Academy";
        const notificationBody = payload.notification?.body || payload.data?.body || "You have a new notification";
        const notificationIcon = payload.notification?.icon || payload.data?.icon || "/logo192.png";
        const notificationImage = payload.notification?.image || payload.data?.image;
        const clickAction = payload.notification?.click_action || payload.data?.click_action || "/";

        // Notification options
        const notificationOptions = {
            body: notificationBody,
            icon: notificationIcon,
            badge: "/logo192.png",
            tag: "chikara-notification",
            requireInteraction: false,
            data: {
                url: clickAction,
                ...payload.data,
            },
            actions: [
                {
                    action: "open",
                    title: "Open App",
                },
                {
                    action: "close",
                    title: "Dismiss",
                },
            ],
        };

        // Add image if available
        if (notificationImage) {
            notificationOptions.image = notificationImage;
        }

        // Show the notification
        self.registration.showNotification(notificationTitle, notificationOptions);
    } catch (error) {
        console.error("[firebase-messaging-sw.js] Error processing background message:", error);

        // Fallback notification
        self.registration.showNotification("Chikara Academy", {
            body: "You have a new notification",
            icon: "/logo192.png",
            badge: "/logo192.png",
            tag: "chikara-notification-fallback",
        });
    }
});

// Handle notification click events
self.addEventListener("notificationclick", (event) => {
    console.log("[firebase-messaging-sw.js] Notification clicked:", event);

    event.notification.close();

    const urlToOpen = event.notification.data?.url || "/";

    if (event.action === "close") {
        // Just close the notification
        return;
    }

    // Open or focus the app
    event.waitUntil(
        clients.matchAll({ type: "window", includeUncontrolled: true }).then((clientList) => {
            // Check if there's already a window/tab open with the target URL
            for (const client of clientList) {
                if (client.url === urlToOpen && "focus" in client) {
                    return client.focus();
                }
            }

            // If no existing window/tab, open a new one
            if (clients.openWindow) {
                return clients.openWindow(urlToOpen);
            }
        })
    );
});

// Handle notification close events
self.addEventListener("notificationclose", (event) => {
    console.log("[firebase-messaging-sw.js] Notification closed:", event);
    // You can track notification dismissals here if needed
});

self.skipWaiting();
clientsClaim();
