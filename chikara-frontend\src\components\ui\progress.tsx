import { Progress as ProgressPrimitive } from "radix-ui";
import * as React from "react";
import { cn } from "@/lib/utils";

const Progress = React.forwardRef<
    React.ComponentRef<typeof ProgressPrimitive.Root>,
    React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({ className, barClassName, value, showPercentage, displayText, frontGradient, backGradient, ...props }, ref) => (
    <ProgressPrimitive.Root
        ref={ref}
        className={cn("relative h-4 w-full overflow-hidden rounded-xs bg-zinc-900/20 dark:bg-zinc-50/20", className)}
        {...props}
    >
        {showPercentage && <p className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 z-10">{value}%</p>}
        {displayText && (
            <p className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 z-10 tracking-wide text-gray-200">
                {displayText}
            </p>
        )}
        {backGradient && <div className={cn(backGradient, "absolute top-0 z-5 h-2 w-full")}></div>}
        <ProgressPrimitive.Indicator
            className={cn(barClassName, "size-full flex-1 transition-all")}
            style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
        />
        {frontGradient && (
            <div
                style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
                className={cn(frontGradient, "absolute top-0 z-5 h-2 w-full rounded-b-md")}
            ></div>
        )}
    </ProgressPrimitive.Root>
));
Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
