import{R as S,K as T,r as l,y as x,cP as Q,o as I,L as h,cQ as j,q as z,u as k,z as J,B as L,s as C,G as W,cR as X,$ as Y,O as Z,Q as ee,U as te,_ as V,V as U,a1 as le,W as $,d as se,f as _}from"./index-hZ2cjOe1.js";var q;let ne=(q=S.startTransition)!=null?q:function(e){e()};var re=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(re||{}),ae=(e=>(e[e.ToggleDisclosure=0]="ToggleDisclosure",e[e.CloseDisclosure=1]="CloseDisclosure",e[e.SetButtonId=2]="SetButtonId",e[e.SetPanelId=3]="SetPanelId",e[e.SetButtonElement=4]="SetButtonElement",e[e.SetPanelElement=5]="SetPanelElement",e))(ae||{});let oe={0:e=>({...e,disclosureState:k(e.disclosureState,{0:1,1:0})}),1:e=>e.disclosureState===1?e:{...e,disclosureState:1},2(e,t){return e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId}},3(e,t){return e.panelId===t.panelId?e:{...e,panelId:t.panelId}},4(e,t){return e.buttonElement===t.element?e:{...e,buttonElement:t.element}},5(e,t){return e.panelElement===t.element?e:{...e,panelElement:t.element}}},w=l.createContext(null);w.displayName="DisclosureContext";function B(e){let t=l.useContext(w);if(t===null){let c=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(c,B),c}return t}let O=l.createContext(null);O.displayName="DisclosureAPIContext";function A(e){let t=l.useContext(O);if(t===null){let c=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(c,A),c}return t}let R=l.createContext(null);R.displayName="DisclosurePanelContext";function ue(){return l.useContext(R)}function ce(e,t){return k(t.type,oe,e,t)}let ie=l.Fragment;function de(e,t){let{defaultOpen:c=!1,...i}=e,u=l.useRef(null),d=x(t,Q(a=>{u.current=a},e.as===void 0||e.as===l.Fragment)),p=l.useReducer(ce,{disclosureState:c?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:s,buttonId:n},b]=p,r=I(a=>{b({type:1});let f=se(u);if(!f||!n)return;let E=a?_(a)?a:"current"in a&&_(a.current)?a.current:f.getElementById(n):f.getElementById(n);E?.focus()}),P=l.useMemo(()=>({close:r}),[r]),m=l.useMemo(()=>({open:s===0,close:r}),[s,r]),y={ref:d},v=h();return S.createElement(w.Provider,{value:p},S.createElement(O.Provider,{value:P},S.createElement(j,{value:r},S.createElement(z,{value:k(s,{0:C.Open,1:C.Closed})},v({ourProps:y,theirProps:i,slot:m,defaultTag:ie,name:"Disclosure"})))))}let pe="button";function fe(e,t){let c=l.useId(),{id:i=`headlessui-disclosure-button-${c}`,disabled:u=!1,autoFocus:d=!1,...p}=e,[s,n]=B("Disclosure.Button"),b=ue(),r=b===null?!1:b===s.panelId,P=l.useRef(null),m=x(P,t,I(o=>{if(!r)return n({type:4,element:o})}));l.useEffect(()=>{if(!r)return n({type:2,buttonId:i}),()=>{n({type:2,buttonId:null})}},[i,n,r]);let y=I(o=>{var D;if(r){if(s.disclosureState===1)return;switch(o.key){case $.Space:case $.Enter:o.preventDefault(),o.stopPropagation(),n({type:0}),(D=s.buttonElement)==null||D.focus();break}}else switch(o.key){case $.Space:case $.Enter:o.preventDefault(),o.stopPropagation(),n({type:0});break}}),v=I(o=>{switch(o.key){case $.Space:o.preventDefault();break}}),a=I(o=>{var D;le(o.currentTarget)||u||(r?(n({type:0}),(D=s.buttonElement)==null||D.focus()):n({type:0}))}),{isFocusVisible:f,focusProps:E}=Y({autoFocus:d}),{isHovered:g,hoverProps:F}=Z({isDisabled:u}),{pressed:M,pressProps:K}=ee({disabled:u}),G=l.useMemo(()=>({open:s.disclosureState===0,hover:g,active:M,disabled:u,focus:f,autofocus:d}),[s,g,M,f,u,d]),N=te(e,s.buttonElement),H=r?V({ref:m,type:N,disabled:u||void 0,autoFocus:d,onKeyDown:y,onClick:a},E,F,K):V({ref:m,id:i,type:N,"aria-expanded":s.disclosureState===0,"aria-controls":s.panelElement?s.panelId:void 0,disabled:u||void 0,autoFocus:d,onKeyDown:y,onKeyUp:v,onClick:a},E,F,K);return h()({ourProps:H,theirProps:p,slot:G,defaultTag:pe,name:"Disclosure.Button"})}let me="div",Ee=U.RenderStrategy|U.Static;function Se(e,t){let c=l.useId(),{id:i=`headlessui-disclosure-panel-${c}`,transition:u=!1,...d}=e,[p,s]=B("Disclosure.Panel"),{close:n}=A("Disclosure.Panel"),[b,r]=l.useState(null),P=x(t,I(g=>{ne(()=>s({type:5,element:g}))}),r);l.useEffect(()=>(s({type:3,panelId:i}),()=>{s({type:3,panelId:null})}),[i,s]);let m=J(),[y,v]=L(u,b,m!==null?(m&C.Open)===C.Open:p.disclosureState===0),a=l.useMemo(()=>({open:p.disclosureState===0,close:n}),[p.disclosureState,n]),f={ref:P,id:i,...W(v)},E=h();return S.createElement(X,null,S.createElement(R.Provider,{value:p.panelId},E({ourProps:f,theirProps:d,slot:a,defaultTag:me,features:Ee,visible:y,name:"Disclosure.Panel"})))}let be=T(de),ye=T(fe),Ie=T(Se),ve=Object.assign(be,{Button:ye,Panel:Ie});export{ve as V};
