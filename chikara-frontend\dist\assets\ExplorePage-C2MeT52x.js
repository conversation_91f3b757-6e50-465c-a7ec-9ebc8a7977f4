import{aE as R,df as it,dg as Qe,dh as ot,di as nt,r as p,ae as m,ax as Fe,a8 as e,bz as G,dj as ae,dk as He,aL as Ue,dl as he,d4 as lt,d5 as ct,d6 as dt,d7 as ut,dm as mt,au as xe,X as ht,R as Te,ac as Y,a5 as V,bd as We,ad as q,a7 as S,dn as ge,bG as xt,aI as Je,br as gt,db as pt,bO as M,bv as se,b8 as re,az as U,at as Ce,by as bt,dp as Ae,aD as pe,dq as ft,dr as yt,cT as te,bA as vt,bt as Ee,ds as wt,dt as jt,du as Nt,dv as kt,dw as Ct,bZ as Ve,dx as At,bm as qe,aj as _,al as It,dy as St,aJ as Tt,a6 as Et,aM as Ot,dz as Mt}from"./index-hZ2cjOe1.js";import{C as Ie}from"./circle-check-big-DQ2fqhUl.js";import{C as Oe}from"./circle-question-mark-BuSWc7Nv.js";import{P as zt,W as Dt}from"./wallet-YHLq2N47.js";import{C as Ke}from"./circle-alert-Bn7osUsn.js";import{r as Se}from"./rarityColours-Bwgi3gKU.js";import{P as Lt}from"./progress-CDoyyAYz.js";import{A as ue}from"./arrow-left-COghmPG7.js";import{B as Bt}from"./book-open-CFpdvdWh.js";import{M as $}from"./map-pin-iNE5pFw1.js";import{T as Rt}from"./trending-up-pOk7QEuQ.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pt=[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]],Yt=R("book",Pt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gt=[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],Qt=R("compass",Gt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ft=[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]],Ht=R("leaf",Ft);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ut=[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]],Wt=R("list",Ut);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],me=R("loader-circle",Jt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vt=[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]],Xe=R("map",Vt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qt=[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]],Kt=R("navigation",qt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xt=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],_e=R("play",Xt);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _t=[["circle",{cx:"6",cy:"19",r:"3",key:"1kj8tv"}],["path",{d:"M9 19h8.5a3.5 3.5 0 0 0 0-7h-11a3.5 3.5 0 0 1 0-7H15",key:"1d8sl"}],["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}]],Zt=R("route",_t);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $t=[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]],ea=R("shopping-bag",$t);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ta=[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]],aa=R("skip-forward",ta);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ra=[["rect",{width:"16",height:"16",x:"4",y:"3",rx:"2",key:"1wxw4b"}],["path",{d:"M4 11h16",key:"mpoxn0"}],["path",{d:"M12 3v8",key:"1h2ygw"}],["path",{d:"m8 19-2 3",key:"13i0xs"}],["path",{d:"m18 22-2-3",key:"1p0ohu"}],["path",{d:"M8 15h.01",key:"a7atzg"}],["path",{d:"M16 15h.01",key:"rnfrdf"}]],Ne=R("tram-front",ra);function sa(t){t.values.forEach(a=>a.stop())}function ke(t,a){[...a].reverse().forEach(i=>{const s=t.getVariant(i);s&&Qe(t,s),t.variantChildren&&t.variantChildren.forEach(n=>{ke(n,a)})})}function ia(t,a){if(Array.isArray(a))return ke(t,a);if(typeof a=="string")return ke(t,[a]);Qe(t,a)}function oa(){const t=new Set,a={subscribe(r){return t.add(r),()=>void t.delete(r)},start(r,i){const s=[];return t.forEach(n=>{s.push(it(n,r,{transitionOverride:i}))}),Promise.all(s)},set(r){return t.forEach(i=>{ia(i,r)})},stop(){t.forEach(r=>{sa(r)})},mount(){return()=>{a.stop()}}};return a}function na(){const t=ot(oa);return nt(t.mount,[]),t}const be=na,O={SIZE:5,MIN_COORD:0,MAX_COORD:4};function Z(t,a=800,r=600,i){if(t.x<O.MIN_COORD||t.x>O.MAX_COORD||t.y<O.MIN_COORD||t.y>O.MAX_COORD)return console.warn(`Invalid grid position: ${t.x}, ${t.y}`),{x:80,y:80};let s=Math.max(60,Math.min(a,r)*.1),n=a-2*s,o=r-2*s,x=n/(O.SIZE-1),u=o/(O.SIZE-1);const f=50;if(x<f||u<f){const b=(a-f*(O.SIZE-1))/2,h=(r-f*(O.SIZE-1))/2;s=Math.max(50,Math.min(b,h)),n=a-2*s,o=r-2*s,x=n/(O.SIZE-1),u=o/(O.SIZE-1)}const c=s+t.x*x,d=s+t.y*u;return{x:Math.round(c),y:Math.round(d)}}function la(t,a,r="curved"){const i=t,s=a;let n,o=[];switch(r){case"straight":n=`M ${t.x} ${t.y} L ${a.x} ${a.y}`;break;case"curved":n=ca(t,a);break;case"bezier":const u=da(t,a);n=u.path,o=u.controlPoints;break;default:n=`M ${t.x} ${t.y} L ${a.x} ${a.y}`}const x=ua(t,a);return{d:n,length:x,startPoint:i,endPoint:s,controlPoints:o}}function ca(t,a){const r=a.x-t.x,i=a.y-t.y,s=t.x+r*.5,n=t.y+i*.3-Math.abs(r)*.2;return`M ${t.x} ${t.y} Q ${s} ${n} ${a.x} ${a.y}`}function da(t,a){const r=a.x-t.x,i=a.y-t.y,s={x:t.x+r*.25,y:t.y+i*.1},n={x:t.x+r*.75,y:t.y+i*.9};return{path:`M ${t.x} ${t.y} C ${s.x} ${s.y} ${n.x} ${n.y} ${a.x} ${a.y}`,controlPoints:[s,n]}}function ua(t,a){const r=a.x-t.x,i=a.y-t.y;return Math.sqrt(r*r+i*i)}function ma(t,a){const r=a.x-t.x,i=a.y-t.y;return Math.atan2(i,r)}function Ze(t){if(t.length===0)return{minX:0,minY:0,maxX:0,maxY:0,width:0,height:0};const a=t.map(o=>o.position),r=Math.min(...a.map(o=>o.x)),i=Math.min(...a.map(o=>o.y)),s=Math.max(...a.map(o=>o.x)),n=Math.max(...a.map(o=>o.y));return{minX:r,minY:i,maxX:s,maxY:n,width:s-r,height:n-i}}function ha(t,a,r,i=50){const s=Ze(t);if(s.width===0||s.height===0)return t;const n=(a-2*i)/s.width,o=(r-2*i)/s.height,x=Math.min(n,o);return t.map(u=>({...u,position:{x:(u.position.x-s.minX)*x+i,y:(u.position.y-s.minY)*x+i}}))}function xa(t,a){return a==="isolated"?[]:t}function ga(t,a){return a==="isolated"?t.map(r=>({...r,status:r.status==="locked"?"locked":"available"})):t}const pa=Fe(["relative flex items-center justify-center","border-2 rounded-full cursor-pointer","transition-all duration-300 ease-in-out","font-medium text-sm","shadow-lg backdrop-blur-xs","select-none","hover:scale-110 hover:shadow-xl","hover:z-10","focus:outline-hidden focus:ring-2 focus:ring-offset-2"],{variants:{status:{completed:["bg-green-500/90 border-green-400","text-white shadow-green-500/25","hover:bg-green-400 hover:border-green-300","focus:ring-green-500","after:absolute after:inset-0 after:rounded-full","after:bg-green-400/20 after:blur-xs after:-z-10"],available:["bg-blue-500/90 border-blue-400","text-white shadow-blue-500/25","hover:bg-blue-400 hover:border-blue-300","focus:ring-blue-500"],locked:["bg-gray-400/60 border-gray-500","text-gray-300 shadow-gray-500/25","cursor-not-allowed opacity-75","hover:scale-100 hover:shadow-lg","focus:ring-gray-500"],current:["bg-yellow-500/90 border-yellow-400","text-white shadow-yellow-500/25","hover:bg-yellow-400 hover:border-yellow-300","focus:ring-yellow-500","after:absolute after:inset-0 after:rounded-full","after:bg-yellow-400/30 after:blur-md after:-z-10","before:absolute before:inset-0 before:rounded-full","before:bg-yellow-400/20 before:animate-ping before:-z-10"]},size:{small:"w-8 h-8 text-xs",medium:"w-12 h-12 text-sm",large:"w-16 h-16 text-base",xlarge:"w-20 h-20 text-lg"},nodeType:{STORY:"border-dashed",CHOICE:"border-double border-4",CONDITION:"border-dotted",ACTION:"border-solid",BATTLE:"border-solid shadow-red-500/20",CHARACTER_ENCOUNTER:"border-solid shadow-purple-500/20",SHOP:"border-solid shadow-orange-500/20",HOUSING:"border-solid shadow-yellow-500/20",MINING_NODE:"border-solid shadow-stone-500/20",SCAVENGE_NODE:"border-solid shadow-emerald-500/20",FORAGING_NODE:"border-solid shadow-lime-500/20"}},defaultVariants:{status:"locked",size:"medium",nodeType:"STORY"}});function ba(t){switch(t){case"STORY":return"📖";case"CHOICE":return"🔀";case"CONDITION":return"❓";case"ACTION":return"⚡";case"BATTLE":return"⚔️";case"CHARACTER_ENCOUNTER":return"👥";case"SHOP":return"💰";case"HOUSING":return"🏠";case"MINING_NODE":return"⛏️";case"SCAVENGE_NODE":return"🔍";case"FORAGING_NODE":return"🌱";default:return"●"}}function fa(t){switch(t){case"completed":return"Completed";case"available":return"Available";case"locked":return"Locked";case"current":return"Current";default:return"Unknown"}}const ya=({node:t,position:a,isSelected:r=!1,showLabel:i=!0,showTypeIcon:s=!0,onClick:n,onMouseEnter:o,onMouseLeave:x,className:u,style:f,debugMode:c=!1,status:d,size:b,nodeType:h})=>{const[N,C]=p.useState(!1),l=d||t.status,g=b||"medium",A=h||t.nodeType,j=g==="small"?16:g==="medium"?24:g==="large"?32:40,k=p.useCallback(L=>{L.preventDefault(),L.stopPropagation(),l!=="locked"&&n&&n(t.id)},[l,n,t.id]),P=p.useCallback(()=>{C(!0),o&&o(t.id)},[o,t.id]),y=p.useCallback(()=>{C(!1),x&&x()},[x]),E=m(pa({status:l,size:g,nodeType:A}),r&&"ring-4 ring-white ring-opacity-60",u),T={position:"absolute",left:a.x-j,top:a.y-j,zIndex:N||r?20:10,...f};return e.jsxs("div",{className:"relative",children:[e.jsxs("button",{className:E,style:T,disabled:l==="locked","aria-label":`${t.title} - ${fa(l)}`,"aria-describedby":`node-${t.id}-description`,role:"button",tabIndex:l==="locked"?-1:0,onClick:k,onMouseEnter:P,onMouseLeave:y,children:[e.jsx("div",{className:"flex flex-col items-center justify-center",children:s&&e.jsx("span",{className:"text-xs opacity-90 scale-200",children:ba(A)})}),t.expiresAt&&!t.isStatic&&e.jsx("div",{className:m("absolute -top-1 -right-1 w-4 h-4 rounded-full border border-white","flex items-center justify-center text-xs font-bold",ae(t.expiresAt)?"bg-orange-500 text-white":"bg-blue-500 text-white"),children:e.jsx(G,{className:"w-2 h-2"})})]}),i&&e.jsxs("div",{className:"absolute pointer-events-none z-30 flex flex-col items-center",style:{left:a.x,top:a.y+j+8,transform:"translateX(-50%)"},children:[e.jsx("div",{className:m("bg-white/95 dark:bg-gray-900/95 text-gray-900 dark:text-white","px-2 py-1 rounded-md shadow-lg backdrop-blur-sm","font-medium text-center whitespace-nowrap","border border-gray-200/50 dark:border-gray-700/50",t.title.length<=8?"text-xs":t.title.length<=12?"text-[10px]":"text-[9px]",N&&"bg-white dark:bg-gray-800 shadow-xl"),children:t.title}),t.expiresAt&&!t.isStatic&&e.jsxs("div",{className:m("mt-1 px-2 py-0.5 rounded text-xs flex items-center gap-1","bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm","border border-gray-200/50 dark:border-gray-700/50",ae(t.expiresAt)?"text-orange-600 dark:text-orange-400":"text-blue-600 dark:text-blue-400"),children:[e.jsx(G,{className:"w-2.5 h-2.5"}),e.jsx("span",{className:"font-medium",children:He(t.expiresAt)})]}),c&&e.jsxs("div",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400 bg-white/90 dark:bg-gray-900/90 px-2 py-0.5 rounded border border-gray-200/50 dark:border-gray-700/50",children:[e.jsxs("div",{children:["Type: ",A]}),e.jsxs("div",{children:["Status: ",l]}),e.jsxs("div",{children:["Pos: (",a.x,", ",a.y,")"]}),e.jsxs("div",{children:["ID: ",t.id]})]})]}),e.jsxs("div",{id:`node-${t.id}-description`,className:"sr-only",children:[t.description,". Type: ",A,"."]})]})};function va(t){switch(t){case"completed":return{icon:Ie,color:"text-green-400"};case"available":return{icon:_e,color:"text-blue-400"};case"locked":return{icon:he,color:"text-gray-400"};case"current":return{icon:G,color:"text-yellow-400"};default:return{icon:G,color:"text-gray-400"}}}function wa(t){switch(t){case"STORY":return"📖";case"CHOICE":return"🔀";case"CONDITION":return"❓";case"ACTION":return"⚡";case"BATTLE":return"⚔️";case"CHARACTER_ENCOUNTER":return"👥";default:return"●"}}function ja(t){switch(t){case"completed":return"Completed";case"available":return"Available";case"locked":return"Locked";case"current":return"Current";default:return"Unknown"}}function Na(t){return t.replace(/_/g," ").toLowerCase().replace(/\b\w/g,a=>a.toUpperCase())}const ka=({node:t,isOpen:a,onClose:r,onAccessNode:i})=>{if(!t)return null;const{icon:s,color:n}=va(t.status),o=t.status==="available"||t.status==="current";return e.jsx(Ca,{open:a,onOpenChange:r,children:e.jsx(Aa,{children:e.jsxs("div",{className:"bg-gray-900 rounded-t-lg p-4 border-t border-x border-gray-700/50 max-h-[80vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"shrink-0 text-2xl",children:wa(t.nodeType)}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-semibold text-lg",children:t.title}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(s,{className:m("w-4 h-4",n)}),e.jsx("span",{className:m("font-medium",n),children:ja(t.status)})]})]})]}),e.jsx("button",{className:"rounded-full p-1 bg-gray-800 hover:bg-gray-700 transition-colors",onClick:r,children:e.jsx(Ue,{className:"size-5 text-gray-400"})})]}),e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-300 leading-relaxed",children:t.description})}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm mb-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-gray-400",children:"Type"}),e.jsx("div",{className:"text-white font-medium",children:Na(t.nodeType)})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-gray-400",children:"Node ID"}),e.jsxs("div",{className:"text-white font-medium",children:["#",t.id]})]})]}),t.connections&&t.connections.length>0&&e.jsxs("div",{className:"text-sm mb-4",children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Connected to"}),e.jsxs("div",{className:"text-white",children:[t.connections.length," node",t.connections.length!==1?"s":""]})]}),e.jsx("button",{disabled:!o,className:m("w-full py-3 px-4 rounded-lg font-medium text-sm","transition-all duration-200 ease-out",o?["bg-blue-600 hover:bg-blue-500 text-white","hover:shadow-lg hover:shadow-blue-500/25","active:scale-95"]:["bg-gray-700 text-gray-400 cursor-not-allowed"]),onClick:()=>o&&i?.(t.id),children:t.status==="completed"?"Review Node":t.status==="current"?"Continue":t.status==="available"?"Access Node":"Locked"})]})})})},Ca=({children:t,...a})=>e.jsx(lt,{...a,children:e.jsxs(ct,{children:[e.jsx(dt,{className:"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"}),t]})}),Aa=({children:t,...a})=>e.jsxs(ut,{...a,className:m("fixed inset-x-0 bottom-0 z-500 mt-24 flex flex-col rounded-t-lg border border-gray-700/50 bg-gray-950","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0","data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom","duration-300"),children:[e.jsx("div",{className:"mx-auto w-12 h-1.5 shrink-0 rounded-full bg-gray-700 my-2"}),t]});function Ia(t){switch(t){case"completed":return{icon:Ie,color:"text-green-400"};case"available":return{icon:_e,color:"text-blue-400"};case"locked":return{icon:he,color:"text-gray-400"};case"current":return{icon:G,color:"text-yellow-400"};default:return{icon:G,color:"text-gray-400"}}}function Sa(t){switch(t){case"STORY":return"📖";case"CHOICE":return"🔀";case"CONDITION":return"❓";case"ACTION":return"⚡";case"BATTLE":return"⚔️";case"CHARACTER_ENCOUNTER":return"👥";default:return"●"}}function Ta(t){switch(t){case"completed":return"Completed";case"available":return"Available";case"locked":return"Locked";case"current":return"Current";default:return"Unknown"}}const Ea=({node:t,position:a,isVisible:r,onClose:i,containerRef:s,zIndex:n=1e3})=>{const[o,x]=p.useState(!1);if(p.useEffect(()=>{if(r)x(!0);else{const c=setTimeout(()=>x(!1),200);return()=>clearTimeout(c)}},[r]),p.useEffect(()=>{if(!r||!s.current)return;const c=b=>{b.target.closest(".node-tooltip")||i()},d=s.current;return d.addEventListener("mousedown",c),()=>d.removeEventListener("mousedown",c)},[r,i,s]),p.useEffect(()=>{if(!r)return;const c=d=>{d.key==="Escape"&&i()};return document.addEventListener("keydown",c),()=>document.removeEventListener("keydown",c)},[r,i]),!t||!o||!a)return null;const{icon:u,color:f}=Ia(t.status);return e.jsxs("div",{className:m("node-tooltip absolute bg-gray-900/95 backdrop-blur-xs","border border-gray-700/50 rounded-lg shadow-2xl","w-80 max-w-[90vw] pointer-events-auto","transition-all duration-200 ease-out",r?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-95 translate-y-2"),style:{left:a.x,top:a.y,zIndex:n},children:[e.jsxs("div",{className:"relative p-4 pb-3 border-b border-gray-800/50",children:[e.jsx("button",{"aria-label":"Close tooltip",className:m("absolute top-3 right-3 p-1 rounded-full","text-gray-400 hover:text-white hover:bg-gray-800/50","transition-colors duration-150","focus:outline-hidden focus:ring-2 focus:ring-gray-600"),onClick:i,children:e.jsx(Ue,{className:"w-4 h-4"})}),e.jsxs("div",{className:"flex items-start gap-3 pr-8",children:[e.jsx("div",{className:"shrink-0 text-2xl",children:Sa(t.nodeType)}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-1 truncate",children:t.title}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(u,{className:m("w-4 h-4",f)}),e.jsx("span",{className:m("font-medium",f),children:Ta(t.status)})]})]})]})]}),e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsx("div",{children:e.jsx("p",{className:"text-gray-300 text-sm leading-relaxed",children:t.description})}),t.expiresAt&&!t.isStatic&&t.nodeType!=="STORY"&&e.jsxs("div",{className:m("p-3 rounded-lg border text-sm",ae(t.expiresAt)?"bg-orange-900/30 border-orange-600/50 text-orange-200":"bg-blue-900/30 border-blue-600/50 text-blue-200"),children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(G,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:ae(t.expiresAt)?"Expiring Soon":"Expires In"})]}),e.jsx("div",{className:"text-white font-medium",children:mt(t.expiresAt)})]}),t.connections&&t.connections.length>0&&e.jsxs("div",{className:"text-sm",children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Connected to"}),e.jsxs("div",{className:"text-white",children:[t.connections.length," node",t.connections.length!==1?"s":""]})]})]})]})},Oa={small:32,medium:48,large:64,xlarge:80},Ma=({node:t,position:a,size:r,isSelected:i=!1,showLabel:s=!0,showTypeIcon:n=!0,onClick:o,onMouseEnter:x,onMouseLeave:u,onAccessNode:f,containerRef:c,viewport:d,className:b,style:h,debugMode:N=!1})=>{const[C,l]=p.useState(!1),[g,A]=p.useState(!1),[j,k]=p.useState(null),P=p.useRef(null),y=xe(),E=p.useCallback(D=>{o?.(D),(t.status==="available"||t.status==="current")&&f&&f(D),y&&t.status!=="locked"&&A(!0)},[o,f,t.status,y]),T=p.useCallback(D=>{if(x?.(D),y||t.status==="locked"||!c?.current||!d)return;const F=c.current.getBoundingClientRect(),J=a.x*d.scale+d.x,ie=a.y*d.scale+d.y,oe=320,ee=280,ne=Oa[r||"medium"],v=16;let I=J+ne/2+10,w=ie-ee/2;I+oe>F.width-v&&(I=J-ne/2-oe-10),I<v&&(I=v),w<v?w=v:w+ee>F.height-v&&(w=F.height-ee-v),k({x:I,y:w}),l(!0)},[x,y,t.status,c,d,a,r]),L=p.useCallback(()=>{u?.(),l(!1)},[u]),Q=p.useCallback(()=>{l(!1)},[]),B=p.useCallback(()=>{A(!1)},[]),K=p.useCallback(D=>{l(!1),A(!1),f&&f(D)},[f]);return e.jsxs(e.Fragment,{children:[e.jsx("div",{ref:P,children:e.jsx(ya,{node:t,position:a,size:r,isSelected:i,showLabel:s,showTypeIcon:n,className:b,style:h,debugMode:N,onClick:E,onMouseEnter:T,onMouseLeave:L})}),!y&&c?.current&&ht.createPortal(e.jsx(Ea,{node:t,position:j,isVisible:C,containerRef:c,zIndex:1e3,onClose:Q}),c.current.querySelector('div[style*="z-index: 1000"]')||c.current),y&&e.jsx(ka,{node:t,isOpen:g,onClose:B,onAccessNode:K})]})},Me=({id:t,color:a,size:r=6})=>e.jsx("defs",{children:e.jsx("marker",{id:t,markerWidth:r*2,markerHeight:r*2,refX:r,refY:r,orient:"auto",markerUnits:"strokeWidth",children:e.jsx("polygon",{points:`0,0 ${r*2},${r} 0,${r*2}`,fill:a,className:"drop-shadow-xs"})})});function za(t,a){const r="transition-all duration-300 ease-in-out";return t?{className:m(r,"stroke-blue-400",a&&"animate-pulse"),strokeWidth:3,strokeDasharray:a?"8,4":void 0,opacity:.8,color:"#60a5fa"}:{className:m(r,"stroke-gray-400"),strokeWidth:2,strokeDasharray:"4,4",opacity:.4,color:"#9ca3af"}}function Da(t,a,r){return la(t,a,r)}const La=({connection:t,isUnlocked:a=!0,showArrows:r=!0,animated:i=!1,variant:s="curved",className:n,debugMode:o=!1})=>{const x=p.useMemo(()=>Da(t.fromPosition,t.toPosition,s),[t.fromPosition,t.toPosition,s]),u=p.useMemo(()=>za(a,i),[a,i]);p.useMemo(()=>ma(t.fromPosition,t.toPosition),[t.fromPosition,t.toPosition]);const f=`arrow-${t.id}`,c=`arrow-glow-${t.id}`,d=p.useMemo(()=>({x:(t.fromPosition.x+t.toPosition.x)/2,y:(t.fromPosition.y+t.toPosition.y)/2}),[t.fromPosition,t.toPosition]);return e.jsxs("g",{className:m("connection-group",n),children:[r&&e.jsxs(e.Fragment,{children:[e.jsx(Me,{id:f,color:u.color}),a&&e.jsx(Me,{id:c,color:u.color,size:8})]}),a&&e.jsx("path",{d:x.d,className:"stroke-current opacity-20",strokeWidth:u.strokeWidth+4,stroke:u.color,fill:"none",filter:"blur(2px)"}),e.jsx("path",{d:x.d,className:u.className,strokeWidth:u.strokeWidth,strokeDasharray:u.strokeDasharray,opacity:u.opacity,fill:"none",markerEnd:r?`url(#${f})`:void 0,children:i&&a&&e.jsx("animate",{attributeName:"stroke-dashoffset",values:"0;12",dur:"1s",repeatCount:"indefinite"})}),e.jsx("path",{d:x.d,className:"stroke-transparent hover:stroke-blue-300 cursor-pointer",strokeWidth:u.strokeWidth+8,fill:"none",opacity:0,children:e.jsxs("title",{children:["Connection from Node ",t.fromNodeId," to Node ",t.toNodeId,t.conditionType&&` (${t.conditionType})`]})}),o&&e.jsxs("g",{className:"debug-info",children:[e.jsx("text",{x:d.x,y:d.y,className:"text-xs fill-gray-500 pointer-events-none",textAnchor:"middle",dominantBaseline:"middle",children:t.id}),e.jsx("circle",{cx:t.fromPosition.x,cy:t.fromPosition.y,r:3,className:"fill-green-500 opacity-60"}),e.jsx("circle",{cx:t.toPosition.x,cy:t.toPosition.y,r:3,className:"fill-red-500 opacity-60"}),x.controlPoints?.map((b,h)=>e.jsx("circle",{cx:b.x,cy:b.y,r:2,className:"fill-yellow-500 opacity-60"},h))]}),t.conditionType&&t.conditionType!=="ALL"&&e.jsxs("g",{className:"condition-indicator",children:[e.jsx("circle",{cx:d.x,cy:d.y-20,r:8,className:"fill-purple-500 opacity-80"}),e.jsx("text",{x:d.x,y:d.y-20,className:"text-xs fill-white font-bold pointer-events-none",textAnchor:"middle",dominantBaseline:"middle",children:"?"})]})]})},Ba=({connections:t,viewportWidth:a,viewportHeight:r,animated:i=!1,variant:s="curved",debugMode:n=!1,className:o})=>e.jsx("svg",{width:a,height:r,className:m("absolute inset-0 pointer-events-none","overflow-visible",o),style:{zIndex:1},children:t.map(x=>e.jsx(La,{connection:x,isUnlocked:x.isUnlocked,animated:i,variant:s,debugMode:n},x.id))}),Ra=({containerWidth:t=800,containerHeight:a=600,showCoordinates:r=!1,showReservedPositions:i=!1,reservedPositions:s=[],className:n,opacity:o=.3})=>{const x=Te.useMemo(()=>{const c=[];for(let d=O.MIN_COORD;d<=O.MAX_COORD;d++)for(let b=O.MIN_COORD;b<=O.MAX_COORD;b++){const h=Z({x:d,y:b},t,a);c.push({gridX:d,gridY:b,pixelX:h.x,pixelY:h.y})}return c},[t,a]),u=(c,d)=>s.some(b=>b.x===c&&b.y===d),f=Te.useMemo(()=>{const c=[];for(let d=O.MIN_COORD;d<=O.MAX_COORD;d++){const b=Z({x:d,y:0},t,a),h=Z({x:d,y:4},t,a);c.push({type:"vertical",x1:b.x,y1:b.y,x2:h.x,y2:h.y})}for(let d=O.MIN_COORD;d<=O.MAX_COORD;d++){const b=Z({x:0,y:d},t,a),h=Z({x:4,y:d},t,a);c.push({type:"horizontal",x1:b.x,y1:b.y,x2:h.x,y2:h.y})}return c},[t,a]);return e.jsxs("div",{className:m("absolute inset-0 pointer-events-none overflow-hidden",n),style:{zIndex:5},children:[e.jsx("svg",{width:"100%",height:"100%",viewBox:`0 0 ${t} ${a}`,className:"absolute inset-0",style:{opacity:o},children:f.map((c,d)=>e.jsx("line",{x1:c.x1,y1:c.y1,x2:c.x2,y2:c.y2,stroke:"currentColor",strokeWidth:1,strokeDasharray:"2,2",className:"text-blue-400"},`${c.type}-${d}`))}),x.map(c=>{const d=i&&u(c.gridX,c.gridY);return e.jsxs("div",{className:"absolute",style:{left:c.pixelX-8,top:c.pixelY-8,transform:"translate(-50%, -50%)"},children:[e.jsx("div",{style:{opacity:o+.2},className:m("w-4 h-4 rounded-full border-2 transition-colors",d?"bg-red-500/30 border-red-500":"bg-blue-500/20 border-blue-500")}),r&&e.jsxs("div",{className:m("absolute text-xs font-mono px-1 py-0.5 rounded","bg-black/70 text-white whitespace-nowrap",d?"text-red-300":"text-blue-300"),style:{left:"50%",top:"100%",transform:"translateX(-50%)",marginTop:"4px",opacity:o+.3},children:["(",c.gridX,",",c.gridY,")"]}),d&&e.jsx("div",{className:"absolute text-xs font-bold text-red-500",style:{left:"50%",top:"50%",transform:"translate(-50%, -50%)",opacity:o+.4},children:"R"})]},`${c.gridX}-${c.gridY}`)}),(r||i)&&e.jsxs("div",{className:"absolute top-4 right-4 bg-black/80 text-white p-3 rounded text-xs font-mono",style:{opacity:o+.5},children:[e.jsx("div",{className:"font-bold mb-2",children:"5x5 Grid Overlay"}),r&&e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-blue-500/20 border border-blue-500"}),e.jsx("span",{children:"Grid Position"})]}),i&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-red-500/30 border border-red-500"}),e.jsx("span",{children:"Reserved (Static)"})]})]})]})},Pa={x:0,y:0,scale:1,width:800,height:600},de={min:.2,max:3},ze={boundary:2e3},Ya=({nodes:t,connections:a,mapType:r="connected",currentNodeId:i,onNodeClick:s,onNodeHover:n,onAccessNode:o,interactive:x=!0,showTooltips:u=!0,showAnimations:f=!0,showGridOverlay:c=!1,className:d,debugMode:b=!1})=>{const[h,N]=p.useState(Pa),[C,l]=p.useState(i||null),[g,A]=p.useState(null),[j,k]=p.useState(!1),[P,y]=p.useState({x:0,y:0}),E=p.useRef(null),T=p.useMemo(()=>{if(t.length===0)return[];const v=ga(t,r);return r==="isolated"&&v.length>0&&v.every(w=>w.position&&Number.isInteger(w.position.x)&&Number.isInteger(w.position.y)&&w.position.x>=O.MIN_COORD&&w.position.x<=O.MAX_COORD&&w.position.y>=O.MIN_COORD&&w.position.y<=O.MAX_COORD)?v.map(w=>({...w,position:Z(w.position,h.width,h.height)})):ha(v,h.width,h.height,100)},[t,h.width,h.height,r]),L=p.useMemo(()=>{const v=xa(a,r),I=new Map(T.map(w=>[w.id,w.position]));return v.map(w=>({...w,fromPosition:I.get(w.fromNodeId)||{x:0,y:0},toPosition:I.get(w.toNodeId)||{x:0,y:0}}))},[a,T,r]);p.useEffect(()=>{const v=()=>{if(E.current){const I=E.current.getBoundingClientRect();N(w=>({...w,width:I.width,height:I.height}))}};return v(),window.addEventListener("resize",v),()=>window.removeEventListener("resize",v)},[]);const Q=p.useCallback(v=>{x&&(l(v),s&&s(v))},[x,s]),B=p.useCallback(v=>{x&&(A(v),n&&n(v))},[x,n]),K=p.useCallback(v=>{if(!x||v.button!==0)return;const I=E.current?.getBoundingClientRect();if(!I)return;const w={x:v.clientX-I.left,y:v.clientY-I.top};k(!0),y(w),v.preventDefault()},[x]),D=p.useCallback(v=>{if(!j||!x)return;const I=E.current?.getBoundingClientRect();if(!I)return;const w={x:v.clientX-I.left,y:v.clientY-I.top},X=w.x-P.x,H=w.y-P.y;N(z=>{const le=(z.x+X)/z.scale,ce=(z.y+H)/z.scale,fe=Math.max(-2e3,Math.min(ze.boundary,le)),ye=Math.max(-2e3,Math.min(ze.boundary,ce)),rt=fe*z.scale,st=ye*z.scale;return{...z,x:rt,y:st}}),y(w)},[j,x,P]),F=p.useCallback(()=>{k(!1)},[]),J=p.useCallback(v=>{if(!x)return;v.preventDefault();const I=E.current?.getBoundingClientRect();if(!I)return;const w=v.clientX-I.left,X=v.clientY-I.top,H=v.deltaY>0?.9:1.1;N(z=>{const le=Math.max(de.min,Math.min(de.max,z.scale*H)),ce=le/z.scale,fe=w-(w-z.x)*ce,ye=X-(X-z.y)*ce;return{...z,scale:le,x:fe,y:ye}})},[x]),ie=p.useCallback(v=>{const I=T.find(w=>w.id===v);I&&N(w=>({...w,x:w.width/2-I.position.x*w.scale,y:w.height/2-I.position.y*w.scale}))},[T]),oe=p.useCallback(()=>{const v=Ze(T);if(v.width===0||v.height===0)return;const I=100,w=(h.width-2*I)/v.width,X=(h.height-2*I)/v.height,H=Math.min(w,X,1);N(z=>({...z,scale:H,x:(z.width-v.width*H)/2-v.minX*H,y:(z.height-v.height*H)/2-v.minY*H}))},[T,h.width,h.height]);p.useEffect(()=>{i&&i!==C&&(l(i),ie(i))},[i,C,ie]);const ee={cursor:j?"grabbing":x?"grab":"default"},ne=`translate(${h.x}px, ${h.y}px) scale(${h.scale})`;return e.jsxs("div",{ref:E,style:ee,className:m("relative w-full h-full overflow-hidden",!d?.includes("bg-transparent")&&"bg-linear-to-br from-slate-900 via-slate-800 to-slate-900",!d?.includes("border-0")&&"border border-slate-700","rounded-lg",x&&"select-none",d),onMouseDown:K,onMouseMove:D,onMouseUp:F,onMouseLeave:F,onWheel:J,children:[!d?.includes("bg-transparent")&&e.jsx("div",{className:"absolute inset-0 opacity-10",style:{backgroundImage:`
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
              `,backgroundSize:`${50*h.scale}px ${50*h.scale}px`,backgroundPosition:`${h.x}px ${h.y}px`}}),c&&r==="isolated"&&e.jsx(Ra,{containerWidth:h.width,containerHeight:h.height,showCoordinates:b,showReservedPositions:b,opacity:.4}),e.jsxs("div",{className:"absolute inset-0",style:{transform:ne,transformOrigin:"0 0"},children:[e.jsx(Ba,{connections:L,viewportWidth:h.width,viewportHeight:h.height,animated:f,debugMode:b}),e.jsx("div",{className:"relative",children:T.map(v=>e.jsx(Ma,{node:v,position:v.position,isSelected:C===v.id,showLabel:u,containerRef:E,viewport:h,debugMode:b,onClick:Q,onMouseEnter:B,onMouseLeave:()=>B(null),onAccessNode:o},v.id))})]}),e.jsx("div",{className:"absolute inset-0 pointer-events-none",style:{zIndex:1e3}}),b&&e.jsxs("div",{className:"absolute top-4 left-4 bg-black/80 text-white p-3 rounded-sm text-xs font-mono",children:[e.jsxs("div",{children:["Viewport: ",h.x.toFixed(0),", ",h.y.toFixed(0)]}),e.jsxs("div",{children:["Scale: ",h.scale.toFixed(2)]}),e.jsxs("div",{children:["Map Type: ",r]}),e.jsxs("div",{children:["Nodes: ",T.length]}),e.jsxs("div",{children:["Connections: ",L.length]}),e.jsxs("div",{children:["Selected: ",C||"None"]}),e.jsxs("div",{children:["Hovered: ",g||"None"]}),e.jsxs("div",{children:["Panning: ",j?"Yes":"No"]})]}),x&&e.jsxs("div",{className:"absolute bottom-4 right-4 flex flex-col gap-2",children:[e.jsx("button",{className:"px-3 py-2 bg-slate-800/90 text-white rounded-sm hover:bg-slate-700 text-xs",title:"Reset view",onClick:oe,children:"🔄"}),e.jsx("button",{className:"px-3 py-2 bg-slate-800/90 text-white rounded-sm hover:bg-slate-700 text-xs",title:"Zoom in",onClick:()=>N(v=>({...v,scale:Math.min(de.max,v.scale*1.2)})),children:"➕"}),e.jsx("button",{className:"px-3 py-2 bg-slate-800/90 text-white rounded-sm hover:bg-slate-700 text-xs",title:"Zoom out",onClick:()=>N(v=>({...v,scale:Math.max(de.min,v.scale*.8)})),children:"➖"})]})]})},Ga=(t,a)=>{const r=t.dialogue.rewards;typeof r=="number"&&r>0&&a.setQueryData(S.user.getCurrentUserInfo.key(),i=>{if(!i)return i;let s=i.cash;return t.dialogue.mugged?(s-=r,s=Math.max(0,s)):s+=r,{...i,cash:s}})},Qa=t=>{const a=Y(),r=V(),{setJustJailed:i}=We();return q(S.explore.interactWithNode.mutationOptions({onSuccess:s=>{if(!s.success)return;const{data:n}=s;if(n&&n.action==="character_encounter"){const o=n;a.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),Ga(o,a),o.dialogue.jailed&&i(!0)}else if(n&&n.action==="battle")a.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()}),r("/fight");else if(n&&n.action==="story"){const o=n;o.redirectToStoryPlayer&&o.episodeData?.id&&a.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})}else n&&n.action&&a.invalidateQueries({queryKey:S.explore.getMapByLocation.key()});t?.onSelectedNodeChange&&t.onSelectedNodeChange(null)},onError:s=>{console.error("Node interaction error:",s),t?.onSelectedNodeChange&&t.onSelectedNodeChange(null)}}))},$e=ge({base:"flex items-center justify-center h-96 rounded-2xl border",variants:{state:{loading:"bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 border-blue-200/30 dark:border-gray-700",error:"bg-gradient-to-br from-red-50 to-orange-100 dark:from-red-900/20 dark:to-orange-900/20 border-red-200/50 dark:border-red-800/50"}}}),Fa=ge({base:"w-full p-3 rounded-lg border-2 transition-all duration-300 text-left cursor-pointer relative overflow-hidden group focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 dark:focus-visible:ring-blue-400 dark:focus-visible:ring-offset-gray-800",variants:{status:{current:"bg-gradient-to-br from-amber-50 to-amber-100/50 dark:from-amber-900/20 dark:to-amber-800/10 border-amber-300 dark:border-amber-700",completed:"bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-300 dark:border-blue-700",available:"bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-800 dark:to-gray-700/50 border-gray-200 dark:border-gray-600",locked:"bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-800 dark:to-gray-700/50 border-gray-300 dark:border-gray-600 opacity-60 cursor-not-allowed"},interactive:{true:"hover:shadow-xl hover:shadow-emerald-500/20 dark:hover:shadow-emerald-400/10 hover:-translate-y-1 hover:border-emerald-400 dark:hover:border-emerald-500",false:""},nodeType:{BATTLE:"hover:border-red-400 hover:shadow-red-500/20 dark:hover:shadow-red-400/10",SHOP:"hover:border-green-400 hover:shadow-green-500/20 dark:hover:shadow-green-400/10",STORY:"hover:border-blue-400 hover:shadow-blue-500/20 dark:hover:shadow-blue-400/10",CHARACTER_ENCOUNTER:"hover:border-teal-400 hover:shadow-teal-500/20 dark:hover:shadow-teal-400/10",MINING_NODE:"hover:border-stone-400 hover:shadow-stone-500/20 dark:hover:shadow-stone-400/10",SCAVENGE_NODE:"hover:border-emerald-400 hover:shadow-emerald-500/20 dark:hover:shadow-emerald-400/10",FORAGING_NODE:"hover:border-lime-400 hover:shadow-lime-500/20 dark:hover:shadow-lime-400/10",ACTION:"hover:border-orange-400 hover:shadow-orange-500/20 dark:hover:shadow-orange-400/10",CHOICE:"hover:border-yellow-400 hover:shadow-yellow-500/20 dark:hover:shadow-yellow-400/10",CONDITION:"hover:border-indigo-400 hover:shadow-indigo-500/20 dark:hover:shadow-indigo-400/10"}},compoundVariants:[{status:"available",interactive:!0,className:"hover:scale-[1.02]"}]}),Ha=ge({base:"w-full min-h-[600px] rounded-lg",variants:{background:{default:"bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800"}}}),Ua=ge({base:"relative before:absolute before:inset-0 before:opacity-30 before:pointer-events-none before:rounded-lg",variants:{nodeType:{BATTLE:"before:bg-gradient-to-br before:from-red-500/20 before:to-orange-500/10 dark:before:from-red-600/30 dark:before:to-orange-600/20",SHOP:"before:bg-gradient-to-br before:from-green-500/20 before:to-emerald-500/10 dark:before:from-green-600/30 dark:before:to-emerald-600/20",STORY:"before:bg-gradient-to-br before:from-blue-500/20 before:to-cyan-500/10 dark:before:from-blue-600/30 dark:before:to-cyan-600/20",CHARACTER_ENCOUNTER:"before:bg-gradient-to-br before:from-teal-500/20 before:to-cyan-500/10 dark:before:from-teal-600/30 dark:before:to-cyan-600/20",MINING_NODE:"before:bg-gradient-to-br before:from-stone-500/20 before:to-slate-500/10 dark:before:from-stone-600/30 dark:before:to-slate-600/20",SCAVENGE_NODE:"before:bg-gradient-to-br before:from-emerald-500/20 before:to-green-500/10 dark:before:from-emerald-600/30 dark:before:to-green-600/20",FORAGING_NODE:"before:bg-gradient-to-br before:from-lime-500/20 before:to-green-500/10 dark:before:from-lime-600/30 dark:before:to-green-600/20",ACTION:"before:bg-gradient-to-br before:from-orange-500/20 before:to-amber-500/10 dark:before:from-orange-600/30 dark:before:to-amber-600/20",CHOICE:"before:bg-gradient-to-br before:from-yellow-500/20 before:to-amber-500/10 dark:before:from-yellow-600/30 dark:before:to-amber-600/20",CONDITION:"before:bg-gradient-to-br before:from-indigo-500/20 before:to-purple-500/10 dark:before:from-indigo-600/30 dark:before:to-purple-600/20"}}}),Wa={"pink-600":"rgb(219, 39, 119)","purple-700":"rgb(126, 34, 206)","red-600":"rgb(220, 38, 38)","orange-700":"rgb(194, 65, 12)","blue-600":"rgb(37, 99, 235)","cyan-700":"rgb(14, 116, 144)","emerald-600":"rgb(5, 150, 105)","green-700":"rgb(21, 128, 61)","indigo-600":"rgb(79, 70, 229)","violet-700":"rgb(109, 40, 217)"},Ja=t=>t.replace("from-","").replace("to-","").split(" ").map(r=>Wa[r]||r).join(", "),Va=t=>{switch(t){case"STORY":return e.jsx(Yt,{className:"w-5 h-5"});case"BATTLE":return e.jsx(pt,{className:"w-5 h-5"});case"SHOP":return e.jsx(ea,{className:"w-5 h-5"});case"CHARACTER_ENCOUNTER":return e.jsx(gt,{className:"w-5 h-5"});case"CHOICE":return e.jsx(Oe,{className:"w-5 h-5"});case"ACTION":return e.jsx(Je,{className:"w-5 h-5"});case"CONDITION":return e.jsx(he,{className:"w-5 h-5"});case"MINING_NODE":return e.jsx(zt,{className:"w-5 h-5"});case"SCAVENGE_NODE":return e.jsx(xt,{className:"w-5 h-5"});case"FORAGING_NODE":return e.jsx(Ht,{className:"w-5 h-5"});default:return e.jsx(Oe,{className:"w-5 h-5"})}},qa=t=>{switch(t){case"STORY":return"from-amber-400 to-yellow-500";case"BATTLE":return"from-red-500 to-red-600";case"SHOP":return"from-green-500 to-green-600";case"CHARACTER_ENCOUNTER":return"from-teal-600 to-cyan-700";case"CHOICE":return"from-yellow-500 to-yellow-600";case"ACTION":return"from-orange-500 to-orange-600";case"CONDITION":return"from-indigo-500 to-indigo-600";case"MINING_NODE":return"from-stone-500 to-stone-600";case"SCAVENGE_NODE":return"from-emerald-500 to-emerald-600";case"FORAGING_NODE":return"from-lime-500 to-lime-600";default:return"from-gray-500 to-gray-600"}},Ka=t=>{switch(t){case"STORY":return{text:"Story",color:"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700"};case"BATTLE":return{text:"Battle",color:"bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-700"};case"SHOP":return{text:"Shop",color:"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700"};case"CHARACTER_ENCOUNTER":return{text:"Character",color:"bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300 border border-teal-200 dark:border-teal-700"};case"CHOICE":return{text:"Choice",color:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-700"};case"ACTION":return{text:"Action",color:"bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700"};case"CONDITION":return{text:"Condition",color:"bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700"};case"MINING_NODE":return{text:"Mining",color:"bg-stone-100 dark:bg-stone-900/30 text-stone-700 dark:text-stone-300 border border-stone-200 dark:border-stone-700"};case"SCAVENGE_NODE":return{text:"Scavenge",color:"bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border border-emerald-200 dark:border-emerald-700"};case"FORAGING_NODE":return{text:"Foraging",color:"bg-lime-100 dark:bg-lime-900/30 text-lime-700 dark:text-lime-300 border border-lime-200 dark:border-lime-700"};default:return{text:"Unknown",color:"bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700"}}},Xa=t=>{switch(t){case"available":return{text:"Available",color:"bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800"};case"locked":return{text:"Locked",color:"bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700"};case"completed":return{text:"Completed",color:"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800"};case"current":return{text:"Current",color:"bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800"};default:return{text:"Unknown",color:"bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700"}}},_a=t=>{switch(t){case"BATTLE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath d="M20 80L50 20L80 80Z" fill="%23ef4444" opacity="0.1"/%3E%3C/svg%3E')]`;case"SHOP":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Crect x="20" y="30" width="60" height="50" fill="%2322c55e" opacity="0.1"/%3E%3C/svg%3E')]`;case"STORY":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Crect x="25" y="20" width="50" height="60" fill="%233b82f6" opacity="0.1"/%3E%3C/svg%3E')]`;case"CHARACTER_ENCOUNTER":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Ccircle cx="50" cy="50" r="30" fill="%230d9488" opacity="0.1"/%3E%3C/svg%3E')]`;case"MINING_NODE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpolygon points="50,20 80,80 20,80" fill="%236b7280" opacity="0.1"/%3E%3C/svg%3E')]`;case"SCAVENGE_NODE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Ccircle cx="50" cy="50" r="25" fill="%2310b981" opacity="0.1"/%3E%3C/svg%3E')]`;case"FORAGING_NODE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath d="M50 20Q60 30 70 50Q60 70 50 80Q40 70 30 50Q40 30 50 20Z" fill="%2384cc16" opacity="0.1"/%3E%3C/svg%3E')]`;case"ACTION":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath d="M30 50L50 30L70 50L50 70Z" fill="%23f97316" opacity="0.1"/%3E%3C/svg%3E')]`;case"CHOICE":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Cpath d="M25 40L50 20L75 40L50 60Z" fill="%23eab308" opacity="0.1"/%3E%3C/svg%3E')]`;case"CONDITION":return`bg-[url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"%3E%3Crect x="30" y="30" width="40" height="40" fill="%236366f1" opacity="0.1"/%3E%3C/svg%3E')]`;default:return""}},Za=t=>{switch(t){case"BATTLE":return{borderColor:"border-red-300 dark:border-red-700",shadowColor:"shadow-red-500/20 dark:shadow-red-400/10",hoverBorder:"hover:border-red-400",description:"Combat encounter awaits"};case"SHOP":return{borderColor:"border-green-300 dark:border-green-700",shadowColor:"shadow-green-500/20 dark:shadow-green-400/10",hoverBorder:"hover:border-green-400",description:"Merchant establishment"};case"STORY":return{borderColor:"border-amber-300 dark:border-yellow-600",shadowColor:"shadow-amber-500/30 dark:shadow-yellow-400/20",hoverBorder:"hover:border-amber-400",description:"Story episode awaits"};case"CHARACTER_ENCOUNTER":return{borderColor:"border-teal-300 dark:border-teal-700",shadowColor:"shadow-teal-500/20 dark:shadow-teal-400/10",hoverBorder:"hover:border-teal-400",description:"Meet interesting characters"};case"MINING_NODE":return{borderColor:"border-stone-300 dark:border-stone-700",shadowColor:"shadow-stone-500/20 dark:shadow-stone-400/10",hoverBorder:"hover:border-stone-400",description:"Extract valuable resources"};case"SCAVENGE_NODE":return{borderColor:"border-emerald-300 dark:border-emerald-700",shadowColor:"shadow-emerald-500/20 dark:shadow-emerald-400/10",hoverBorder:"hover:border-emerald-400",description:"Search for hidden items"};case"FORAGING_NODE":return{borderColor:"border-lime-300 dark:border-lime-700",shadowColor:"shadow-lime-500/20 dark:shadow-lime-400/10",hoverBorder:"hover:border-lime-400",description:"Gather natural materials"};default:return{borderColor:"border-gray-300 dark:border-gray-700",shadowColor:"shadow-gray-500/20 dark:shadow-gray-400/10",hoverBorder:"hover:border-gray-400",description:"Unknown location type"}}},$a=t=>{const a={available:0,current:1,completed:2,locked:3};return[...t].sort((r,i)=>a[r.status]-a[i.status])},er={izumi:{positive:"/Izumi/happyclosed.webp",neutral:"/Izumi/happyopen.webp",sad:"/Izumi/sad.webp"},"???":{positive:"/Sakura/hidden.webp"},daiki:{positive:"/Daiki/happy.webp",neutral:"/Daiki/neutral.webp",sad:"/Daiki/sad.webp"},hana:{positive:"/Hana/happyopen.webp",neutral:"/Hana/shy.webp",sad:"/Hana/sad.webp"},hiroshi:{positive:"/Hiroshi/happyclosed.webp",neutral:"/Hiroshi/smug.webp",sad:"/Hiroshi/worried.webp"},kenzo:{positive:"/Kenzo/happy.webp",neutral:"/Kenzo/neutral.webp",sad:"/Kenzo/annoyed.webp"},mai:{positive:"/Mai/happyopen.webp",neutral:"/Mai/happyclosed.webp",sad:"/Mai/sad.webp"},tashiro:{positive:"/Tashiro/happyopen.webp",neutral:"/Tashiro/happyopen.webp",sad:"/Tashiro/worried.webp",negative:"/Tashiro/smug.webp"},apollo:{positive:"/Apollo/happyopen.webp"},yuta:{positive:"/Yuta/embarrassed.webp"},kazuya:{positive:"/Kazuya/neutral.webp"},katsuro:{positive:"/Katsuro/happyopen.webp"}},De=(t,a)=>{const r="https://cdn.battleacademy.io/static/characters";if(!t)return a?"":null;const i=t.toLowerCase();if(a)return i==="apollo"?"scale-[0.6] h-auto md:h-3/4 top-[55%] left-1/2 -translate-x-1/2 -translate-y-1/2":"scale-110 -left-[0.2rem] md:left-40 h-3/4 md:h-full md:-bottom-36 bottom-0";const s=er[i],n=s?s.positive:null;return n?`${r}${n}`:null},tr=()=>{const t=Y();return q(S.explore.completeNode.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})}}))},ar=({dialogue:t,healed:a,nodeId:r,onClose:i})=>{const s=be(),n=V(),{mutate:o}=tr(),x={hidden:{opacity:0},visible:{opacity:1,transition:{duration:1}}},u={hidden:{width:"100%",left:0},visible:{width:0,left:"50%",transition:{duration:.75}}},f={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.5}}},c={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.1}}};p.useEffect(()=>{s.start("visible")},[s]);const d=()=>{o({nodeId:r},{onSettled:()=>{i()}})},b=(N,C,l)=>typeof N!="number"||N===0?l?"You were caught littering! You were sent to jail.":`${C} went through your wallet but found nothing!`:N>0?l?`You were caught littering! You received a ${U(N)} fine and were sent to jail.`:`${C} stole ${U(N)} from your wallet!`:"…",h=t.isItemDrop;return e.jsxs(M.div,{className:"shadow md:rounded-b-lg",variants:x,initial:"hidden",animate:s,children:[e.jsx(M.div,{className:"wipe",variants:u,initial:"hidden",animate:"visible"}),e.jsxs("div",{className:"relative h-full overflow-hidden md:h-full md:rounded-lg",children:[e.jsx("img",{className:"h-full object-cover md:h-full md:scale-105 md:rounded-lg",src:se(t?.location)||"",alt:""}),e.jsx("div",{className:"absolute bottom-0 size-full bg-black opacity-20 md:rounded-b-lg"}),e.jsx(M.div,{variants:c,initial:"hidden",animate:"visible",children:e.jsxs("div",{className:m("-translate-x-1/2 absolute left-1/2 z-50 w-[90%] skew-x-2 justify-end border-4 bg-slate-800 text-white opacity-95 shadow-lg md:h-44 md:skew-x-1",h?"-translate-y-1/2 top-1/2 border-blue-500 md:w-2/4":"bottom-14 md:w-3/4"),children:[!h&&e.jsx("div",{className:"-top-12 -translate-x-1/2 -rotate-3 -skew-x-2 md:-top-14 md:-skew-x-3 absolute left-[4.2rem] z-50 h-14 w-28 justify-end border-4 bg-slate-800 text-center text-white md:left-20 md:h-16 md:w-32",children:e.jsx("p",{className:"m-2 skew-x-2 text-2xl text-[#6dc7ff] md:skew-x-3 md:text-3xl",children:t?.character})}),e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"-skew-x-2 md:-skew-x-3 mx-6 my-4 flex h-[52%] flex-col md:mx-8 md:h-[55%] md:text-lg xl:text-xl 2xl:text-2xl",children:[h?e.jsx("p",{className:"mx-auto inline-block h-[70%]",children:"You found an item while exploring the zone!"}):e.jsx(e.Fragment,{children:t?.jailed?e.jsx("p",{className:"mx-auto inline-block h-[70%]",children:"Hey, stop right there!"}):e.jsx("p",{className:m(t?.line.length<16?"text-center":"text-left","inline-block h-[70%]"),children:t?.line})}),t?.mugged?e.jsxs("p",{className:"text-center text-red-500 md:mt-0",children:[b(t?.rewards,t?.character,t?.jailed),t?.hospitalised&&e.jsx("span",{children:" You were injured in the attack."})]}):e.jsx("div",{className:"text-center text-yellow-400 md:mt-0",children:a?e.jsx(e.Fragment,{children:" You were healed by the dog's presence "}):e.jsxs(e.Fragment,{children:[t?.isItemDrop?e.jsx("div",{className:"mt-1.5 flex",children:e.jsxs("span",{className:"mx-auto flex gap-2",children:["You gained 1x"," ",e.jsx(re,{item:t?.rewards,className:"inline-block h-6 md:h-8"})," ",e.jsx("span",{className:"text-sky-400 text-stroke-sm",children:t?.rewards?.name})]})}):e.jsx(p.Fragment,{children:typeof t?.rewards=="number"?`You gained ${U(t?.rewards)}`:""}),t?.crateReward&&e.jsx("div",{className:"mt-1.5 flex",children:e.jsxs("span",{className:"mx-auto flex gap-1 text-sm md:text-base",children:["You found a",e.jsx(re,{item:t?.crateReward,className:"inline-block h-6 md:h-6"})," ",e.jsx("span",{className:"text-sky-400 text-sm text-stroke-sm md:text-base",children:t?.crateReward?.name}),e.jsx("span",{className:"hidden md:block",children:"in the trash"})]})})]})})]}),t?.jailed?e.jsx("button",{className:"-skew-x-2 md:-skew-x-3 mx-2 mb-[0.35rem] rounded-md border border-transparent bg-indigo-600 px-3 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mt-0 md:px-4",onClick:()=>{d(),n("/jail")},children:"Go to jail"}):e.jsx("button",{className:"-skew-x-2 md:-skew-x-3 mx-2 mb-[0.35rem] rounded-md border border-transparent bg-indigo-600 px-3 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mt-0 md:px-4",onClick:d,children:"Return to Explore"})]})]})}),!h&&e.jsx(M.div,{variants:f,initial:"hidden",animate:"visible",children:e.jsx("img",{src:De(t?.character)||"",alt:"Encounter character",className:m(De(t?.character,!0),"absolute z-0")})})]})]})},rr="/assets/arcade-BIkkTRDG.png",sr="data:image/webp;base64,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",ir="data:image/webp;base64,UklGRpAFAABXRUJQVlA4WAoAAAAQAAAAYwAAYwAAQUxQSEQBAAABkGtr27FHT2zWSWWnsmujsu0cA6p0tpO/Ts7ATipjHNvGexe/gucbz6yImAD6P1B32S7Y3JW5vlD8PVi9i3sRt3swe+f6EjKw2/sSe/wc+BurJ/gB7prM1AHPi5ZSgFZJOJME/CeekIQ9SZBJguuTFFC6kALyXn5i6Eot5Rope4wsvYRORQh5ToLT6pfwwN2cAKePNi9BNeBVZNPL5h4IPsRGKL1f4BHE/q+Ip18Oz4xF7q76qOazthvJz8789IGnKp4Hswvb/GwuYXrPQ06AyGNvCkvf+InZ3cbUjpMip51RHO/E8kMpO6cY23GQs9kawd3XMuK4cOMaY1/lvg5BfKgknsu2HjEkNwis9mgwReXfgEFgEPgwoEVsV21A4caUIfGt0bght79iQZxrdOwBF9vWxLt2//H1oSNxr5VSakU/swFWUDggJgQAANAYAJ0BKmQAZAA+kTyZSCWjoqEttJwAsBIJYwDUDONBqPz6CNu5zwfnwdL36rnoV9MV/nsFA/u78nWBPF+EDSWY3f8l9wHyE57fqX0Zuqqj56qFIs/2so66pGuOSZzKrcOaloUPlRsAZyB7pxMc4ACSb7CzWFqjoiGSdToPxpkEdb9IiwsT4wxK3fssClmw2NDXYNJ0/MNKIhQKSjTbZHVuddhgq1dVJcSCdM6xMQssHcZMTJ/KBG0J5NwVEvZBnYa373y1hZY2oGnAinXKAAD+/FzQDO0d3lcV0/F/2In7/kxYf9ul2szT7KYK/x5AUvagzmFHjHKw51YgDOa8GHaceCVmYtLaWzUvxakmqCMbqPf4tV4P+hke2Juy8uOKUHTcsgH0nniH4xTVr3Zndawh+ObmJTHz6fYbEwbXcfrVGpxREu4x15w1MX5iv840NQtrr3EoRe0OpfcE8kcakSpm5hvwd34ckV9s/hnwicjjRtuOTG+brMk24UzvQROB6tk8YvsLMsUJpwBWboYYcqk+HBtI6QWN0AP5TBk1G4+fsGWwv2LKWmekonIJ3fpBkLmU5zax//wnyM84vXwM6CVBHCn/TENctaHD5BZDzc85oleRkAY3voUG/Rtx7srDuwlJRjQY7xRuSgPLPhJStfXokxqXRlYwlO0M/Lnk/B3U+QlhyvsbKGhfgIt+cldbf4wHTB+CZAGKrgawgPpXFuVPkxtNoEa/W8eOYiA9ZXe2qzGHZCY5p0qpA+VhiyfXwsb8AM8dMaSkW5TWX4CHPFs+O5VKNbq3avLWwjWzefp/mPco+jU0wqVDmlHO9icuQL+u1W3/8sfqSCDAJx6C2avPmMNjIze8kcxr6+YQ9MQcLmmsxdP3462U11v+A9oihm0c1cWx8umNlfy5skDQ3s5JPmzDgKQYU5Yd3074cNM0r0bylApoSlJrDyAgTbaTUt8eAJy8wa8/sfec0iyjfxv0QKH3JhKuL2NTTiCWbdrwDLM/a84Pjr3836UKyAJ+M0MP/uxeBwG5z0rDHYt/vrUpN9Axb7rzLzWXacJDbhqUi8P249FSvKF9tYU/IJ4ewj8RHN8a4OdY+gNoUkNzahq+tOjIgY8dIY0V4uv19y/6/SUv2NZdsdFBHPBXonGxERIS6GldlHHsbD52hJ1UqdenTjjtdAHaBWPtk+U2QLodknZs51KH7a5QKPv+yVCs8/YBJenI4mvywPrGStybABba+Q/IIqJ7WcWZ5a+UM3q+9/P8aH1uuPoBie2ZUuyaDA+4n4dUk/FOy7uSKkYCsFvBz7VfY+Blxw/bYpoVq9Atp2etFKBCH0yq30LUo10SqpJD+YCPAzRjU6NkLR/EYLR7GOnPS1HwWJqWWr2WNoBjf+tZXRRzYAm+rI/yHgaTH+orMAAAAA==",or="/assets/casino-esO0707x.webp",nr="data:image/webp;base64,UklGRgoFAABXRUJQVlA4WAoAAAAQAAAAYwAAYwAAQUxQSJMBAAABkGtr27FHT6yvjJPSrt3ZVuXOqe3kDMb2HIBd2p7pYtvJ0w3u+38zLCJiAuQ/nbGtV17Ob+s3b8+/uNIaY0Ostrf6w9+2WrbBsWVOf+pss6MNCHmmP/1pMF32igIuZ5FV7ijkdgVV9o6CbmcQBS0r7EoojeMzBX7swNKi0I0k1hzWjAdHm4K3cLxFe00Rq/CRDG14zQxX8C4xvMJ7zjCLNwOXcvSD4m+ChTxQTqzkebV9IfNqgAdqgBQ1wVEjfDDCihF2jLBihGmebaSPPItId3gmkE7zfEVq53mNlM3zCCmQ5x6SfKU5D3WIZgAqj6YeymWeJQVKjrAEY8WQrDlhyS2OuwIet0/RiyZXKdLgApYI1iw4qSI4J4TH8NIYnK6hfXZgEOsOWIlwupyCum9HIlI5gTPuL7xWw0OUIOFGkQOP8wiKF9Wwoi4VMo3B6F4hkQIveplAe4zwzAjLRtA/DktGeGqEbhMsevKM4ezlC+8QzGKBEDsPoXgKN4ocgMYwRtmGMAbZnIfGft7ooDPbfxQBAFZQOCBQAwAAMBMAnQEqZABkAD6RQJlJJaQioSsXisCwEglAGoiD5a8LrgS7YrxJfWG9De8degB0sVeH6Du9mUr9E8wNLjMs/Uv0Q6ahn59XEumr1sxZhOWAhDts9lyKz37gBqdwvgL2obSgWEaEQqmD6aCTT5HrYVlSAUynVxIuxR/WlKktuvE42w1lYlKAh4nEkYOP0mqWXStgHNZsiJida/vPsbV/KzgAAP78XNX3M5elK6I9dCpPPmvf35Fp7LIG1rXrCQgQGqzQVWj5O8RMyYH36+WA8Ei06xmzewbKs1VDCA/CTcFl67+MEOR2y+WZQqSfFrwbhZx565+FstfxtPhNLsBNgcf5abwZYZ1MftAcYaJYpvaPLbJcDBmw34CVsFke13Aa3J332lt3WUvTxqCPHhvXWIuU0n8QGTisNs3Hmq28y9LkguCPV/SESDAmu0HLwNZi/bGG7nz7LiGMuPKKm/2mJ5/HzNLMIvmQNqdcKZOZs+Z9EBFhURq9RJpXeCeVFU0dqHXUQhkBGcfNqFbFCJsoElAE7cGP5O5DmOl/Orn6jw39hpIcN8GO6MRpQoCQ30mv9Ji/U1nFJKKV6Y9bG42IisRMKdvvJUIH7IR+yLv/nkUBWH1HctPWOo+pCzOvwNDR3Z/UYd+v2t1patNjKxpJTuXsB+mO8I6heY6oyhs298b1Y1AjhuRPnSS4yrCZzF845EjNOXdt59CTVJJjhF4JoA0zrdsmMrKyLHBEj7gpu6ex/xA29Y4HlinZzOgF69u6bw+J0a1Mg1cK1wH7OO+fDPZxZDjzZ3yzcXFwMYSPaDf54FkQNF8rAi3Q78LD2boSpjK7JtPXu5/6NzF6OZL7I3l1FN989QxsvUhBJebG/DFhYkeSblen+E8yI3mvoqOEGs8NVSROOp8xMz/qwMxcDViIcLtJoR97ecKD5PsNBOuOF+u/LGBV9/yQ08rqH10RdmWMSi4MfYk/okCsCn2PRsmmTc5NBzp/n3w9cie5D6P9w/8GBoLZngSrG++o8rVmZibzTeNtp/wzUtlfD/wl8ycYhHZKh1RQpO3g2dODoX/H/OxQpdc/lDrCbQgGi++/s5Q0hz37A818XiREUQLlW8ekRY+WfQ4f4n3KAAAAAAA=",lr="data:image/webp;base64,UklGRpAFAABXRUJQVlA4WAoAAAAQAAAAYwAAYwAAQUxQSL8BAAABkGNr2xo3vx3QScfMlReQjpk2kCpVakMVxgXgKlIzk/AMhZnZzNaYLT0BzS/p1x9OERETIP7j6RHtao3K/xp4JO9qhDT/gnJSsNUy6gvlcq52YkKSEiKT8x8QeW+exowHvMn7fq6Re4a+GmjsBuhqpE5fRXJE5sjry8eP6qNPX0j/GjkV7Mz1vb52cseipLItF/3saI3W2loe2dZ7CC9trdLXW75/Y74Dd6m27kdAUHjF6JHxmrIrwJg5wPXJWTDqC+VyrnaiGvD2LY9nqjf/AZH35qXwSoru+9ybrJrxgDd53881cs9ILnwrxcATLlUr1kBjN0BXI3XJcUuOzs/sFUJ4pB82unvnCiGK5IjMkU+hcViOh6PDNUKg6NiB8cLHj+qjL7kSVgxMLqsD1iSkk9tEW2eMnk5WKcRZNcQ5cjEwOaNSzBRmt2HG6BoIlmhHrB8OHTkcdio0oorYHgRX5QrkFQqVEduG8bplykOhQjHTESs+0eIFlbiLlsT0U/DMG6lgakqI9Q60WndGvnO1JcSy0wH0lG7lnt3Wjl7/tunSQXt6uv3b5qEOHqan2+RE5v9tcmTs7Dkyttz/VAEAVlA4IKoDAADQGACdASpkAGQAPpE8mEiloyKhLRQMaLASCUAanv0Pmb7e3b5c8d/6Ae6jbUeYDzoP7l+s3ur3g30IPLc9nrAHf7h2sf4Wu47/e8GUEYp9GnSW8LUwZ/n+dF8AkPZlP3egFNWU9UMSpbGNj4GCdILar4Oe2gaVcml7RUp1DKJEepPKV7uTU5FOV9Oc/gx3DiFlIO1mrE58cf8wU9lZd56O95PQsUvhP6aNjUSBPV4fkRi5omA+yk7tAv2iHK2uef95wAC+81+3r/2RYgSB/AAA/vxc1Gpw+6dNblIBgYG9eVHgDYUN0Zj3Al+lf3wfRsSLwDFr4G3BtP5dwGKFUGe3RuAHWakXqihV7b0Ga4MDxGY3M0t5lJ97T7Z4i4EyElPFbs7HRQQmk/2Cwnq6HEcCesZVPf0hVyyLxAmNLn9XwrEgIVvM8iDYq/xu8w9Nk4q+XqxvkG9kCBQC4YeBsw2/tTX7u9B4KDsf1uQ4+YqMy0UZ03nuX8XhOOOdIDY/dF//exBAFc/f3/2I/YXluDL7OT10zX/60mWuOhbYBwFna2zt7OxD/ho48LlMINUumtkGjCzx5LdVcP1knVzxGsJs+GiH3mJndx/ARmxwb2+Wf/z13+8iwsZ8flLCcqAE/Y9uwfWvpRgE/ZWTaY4ROjSN1PjtFZgIoUaiQC8H9S/ZPo+32nZLjcnrnK9HsNmL2y/xkqSHodUKlUOA94djd2tB9WyTNo02M6xjqAsuJ5u7jJWtBDeLa/XW2CKLU8cnJhcYHSz7zQUhUZ+ao6EYqu6mfxIH4twff4bAIGO7u2G+kqivKOVtIpG8zoMbb7HYd9kjp5ti+31t+KmWyGbtze6j+0jMDPunShyctHSXcG7c5RBwfLmXaisDk5+nvc9oTrJVhV2YzTHm9bt1s4LDhCDaohI9/0hj6M+NptP5PB45hKGOGk/UESnLQUL+zf1K6k5H32Sz9ekXBUe8ebw73AlfQ5WYueq3Jme4XWPVbkBbCKp0Ue4rDcZ7cztsH0xJCGzF/mr5VJ/S/bFKdmn6sOEuyeaXa3TcgTel6E0nRfj8WgcysZ4gabZdGj0WcKp6DA5IdvZOgEcGAG7c1/en5PQeGCO9JccqSDjGEALJfGcdC0MfDR8i5FQbvmcOiQ4S2fNufhWebL8UvkZR0UwxF/4T93dOO18KaZ9Gv7w7V//7SO6TRO96w5g1Rngc43jwu5GU/Km3vjfhVvf8hooBgpDzqWgT+iVXVkAAAA==",cr="data:image/webp;base64,UklGRtIDAABXRUJQVlA4WAoAAAAQAAAAYwAAYwAAQUxQSJIAAAABYBzbdpKflXWOS4/JjhSAu0RwmIhg5LZtJPradvYZY54Ar5gQXzSKIzysXtpoSKwckjpIpBprtIRdT8XCSk3TFHP7oh2MsnkP/mrkk5Xm96BZsS9noQMXK/ruqjZs9JAHhTHv4nVv5RPyX2Dju7gwa8jWu+5bbWOvy+3odRcTkjpkSDVIdIhAOeJx1biD4Q0TAFZQOCAaAwAA8BIAnQEqZABkAD6JOpVHpSOiITOYCoigEQlnEuA8QPR5+HCB6P0bugIDJ74ryBXiquwB3CPkQ/zPpWaJ/IWqNPr01vNA26ucgHdHQfbDrOPLpL0JwBBKPj5UYLps5v1EHtAZ0EntvwNERxxIsN7X0J4tatFwedUwdxyZeGtYJDfoZCstzDr015CvlTUzz+/HobQMU85VKSoiq5jj3x/iAAD++0AjZlL8Ptq4JGaslk3LEW3PvEiVh1VF3xjHJ9hCrPQ0Zca33JsM0c1IQ/e8yjpBdUbJffLI20dLW/jL36+nhLB/4g6vtTLiCy3m0UoeatbQzfnckgzYZX8LoZlIWNbosOkt//2hD1DQKqkHrJHhHexbLIdu+GB4Y1nfs+LLvu/wfFu/5iRSZwi9ivP7epG1FjiH0crfog3w2rzbVO34tDdIKA+sP+NSGP3vfkhu3v7Kiu5HK8xZ6CQhYpgV3BJbeKNALW9E6vR4YjPBWKnUs1Zg9x2KurnBMqgEePLW210ugVSNUmHX8UhemUX2fifyjs5zoWcXtUKtecfe+DqxmPp78mQZwLNEGnFSbROFuXKu0rLTj4SLqOjPvXnPGRk2FyC4Dzaw48E9B7QczqmuofFFYWrf+Bxam+xeb6KYHBq3Ae+TdD+pmUKqwQKILnnEy5Ma91tHpf02irzrtu0G8889E4mlofI+la6XHTwW8NRjiS+92Tv48xdvyGX3paidmG+x+UvU+Fzp/PnmHzOBG85xr8w+FwCbk4c0GUBQmmQznr3vefN00X1f6RQBkMfmc6UiPecIyoFvehVnvJWvKuk/WRQFU3wNO4QACmS7rpbjE8Fwm8/nVXjYNfY9QzGAvc7hSTZ+myHe6Ym+y8xWfIHm/8m02ILSHzsuA7y7/7cfZyv5nr0TWiRUPrarwoC5IftMyM9rLtqrDYsVR7pF9+Jwqspi8wU06p+tbtgGKmiiQMFkVOOE4UV/WzpYdzF+AK3ujvtH87Tn4B18adt+NjNHjBLWmVa6MMv5EG3kzyL8UTf9Vf/GOVZNN6nS3e0K5rQwAAAAAAA=",dr="/assets/jobs-BDLp4JM9.webp",ur="/assets/shopsicon-CxJUSSkR.webp",mr="/assets/shrineicon-BvwFPa2C.webp",hr=[{id:"training",name:"Training",description:"Improve your combat stats and grow stronger",icon:nr,route:"/training",color:"bg-yellow-500 dark:bg-yellow-600"},{id:"shops",name:"Shops",description:"Buy and sell items",icon:ur,route:"/shops",color:"bg-green-500 dark:bg-green-600"},{id:"bank",name:"Bank",description:"Manage your finances and investments",icon:ir,route:"/bank",color:"bg-teal-500 dark:bg-teal-600"},{id:"shrine",name:"Shrine",description:"Donate to the shrine for blessings",icon:mr,route:"/shrine",color:"bg-amber-500 dark:bg-amber-600"},{id:"hospital",name:"Hospital",description:"Recover from injuries and restore health",icon:lr,route:"/hospital",color:"bg-pink-500 dark:bg-pink-600"},{id:"jail",name:"Jail",description:"Serve time or visit incarcerated players",icon:cr,route:"/jail",color:"bg-slate-600 dark:bg-slate-700"},{id:"casino",name:"Casino",description:"Try your luck with games of chance",icon:or,route:"/casino",color:"bg-purple-500 dark:bg-purple-600"},{id:"job",name:"Part-Time Job",description:"Work part-time to earn money and experience",icon:dr,route:"/job",color:"bg-red-500 dark:bg-red-600",gateKey:"job"},{id:"market",name:"Market",description:"Trade items in the player marketplace",icon:sr,route:"/market",color:"bg-blue-500 dark:bg-blue-600",gateKey:"market"},{id:"arcade",name:"Arcade",description:"Play mini-games",icon:rr,route:"/arcade",color:"bg-purple-500 dark:bg-purple-600",gateKey:"arcade"}],Le=()=>{const t=V(),{data:a}=Ce(),r=i=>{t(i.route)};return e.jsxs("div",{className:"mb-4 lg:mb-0",children:[e.jsxs("div",{className:"mb-3",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Common Locations"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Available in every district"})]}),e.jsx("div",{className:"grid grid-cols-5 sm:grid-cols-3 lg:grid-cols-1 gap-2 lg:gap-3",children:hr.map(i=>{const s=i.gateKey&&a?.level?bt(i.gateKey,a?.level):null,n=s?.isLocked,o=n?s.message:i.description;return e.jsx("button",{disabled:n,className:m("cursor-pointer p-2 lg:p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg transition-all duration-200 hover:shadow-md group",n?"opacity-60 cursor-not-allowed hover:bg-white dark:hover:bg-gray-800 hover:border-gray-200 dark:hover:border-gray-700":"hover:bg-gray-50 dark:hover:bg-gray-750 hover:border-gray-300 dark:hover:border-gray-600"),onClick:()=>r(i),children:e.jsxs("div",{className:"flex flex-col lg:flex-row items-center lg:items-center text-center lg:text-left space-y-1 lg:space-y-0 lg:space-x-3",children:[e.jsx("div",{className:m("w-8 h-8 lg:w-10 lg:h-10 rounded-full flex items-center justify-center text-white transition-transform duration-200 flex-shrink-0",i.color,!n&&"group-hover:scale-110"),children:e.jsx("img",{src:i.icon,alt:"",className:"hidden size-full md:block"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-xs lg:text-sm font-medium text-gray-900 dark:text-white line-clamp-1",children:i.name}),e.jsx("p",{className:m("text-xs text-gray-600 dark:text-gray-400 line-clamp-1 lg:line-clamp-2 mt-0.5 lg:mt-1 hidden sm:block lg:block",n&&"text-amber-600 dark:text-amber-400"),children:o})]})]})},i.id)})})]})},xr=()=>e.jsx("div",{className:$e({state:"error"}),children:e.jsxs("div",{className:"flex flex-col items-center space-y-4 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center",children:e.jsx(Ke,{className:"w-8 h-8 text-red-600 dark:text-red-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-red-700 dark:text-red-300",children:"Failed to Load"}),e.jsx("p",{className:"text-sm text-red-600 dark:text-red-400 mt-1",children:"Unable to fetch exploration areas"})]})]})}),gr=()=>e.jsx("div",{className:$e({state:"loading"}),children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(me,{className:"w-12 h-12 animate-spin text-blue-500"}),e.jsx("div",{className:"absolute inset-0 w-12 h-12 border-4 border-blue-200 dark:border-blue-800 rounded-full animate-pulse"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-lg font-semibold text-gray-700 dark:text-gray-200",children:"Loading District"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"Discovering new exploration areas..."})]})]})}),pr=()=>{const t=Y();return q(S.explore.processForagingOperation.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.user.getInventory.key()})}}))},ve={container:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1}}},wipe:{hidden:{width:"100%",left:0},visible:{width:0,left:"50%",transition:{ease:"easeInOut",duration:.75}}},dialogueBox:{hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.3,ease:"easeOut"}}}},Be={herbs:"https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png",mushrooms:"https://img.icons8.com/?size=100&id=43138&format=png",berries:"https://img.icons8.com/?size=100&id=bqO7Szu38UJF&format=png",flowers:"https://img.icons8.com/?size=100&id=d6K0u3dO9c4C&format=png",roots:"https://img.icons8.com/?size=100&id=C3GJQJl8Dqjp&format=png",medicinal:"https://img.icons8.com/?size=100&id=vB6PoShzdZKw&format=png",plants:"https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png"},et={easy:{color:"text-green-400",bgColor:"bg-green-500/20 border-green-400/30",icon:"🌱",label:"Easy",description:"Gentle gathering, common finds"},medium:{color:"text-yellow-400",bgColor:"bg-yellow-500/20 border-yellow-400/30",icon:"🌿",label:"Medium",description:"Careful foraging, rarer plants"},hard:{color:"text-red-400",bgColor:"bg-red-500/20 border-red-400/30",icon:"🍄",label:"Hard",description:"Dangerous terrain, exotic species"}},br=t=>Be[t]||Be.herbs,fr=()=>{const t=Y();return{invalidateQueries:async()=>{await Promise.all([t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),t.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()})])}}},yr=({isSuccess:t})=>{const a=t?"text-green-400":"text-red-400",r=t?"bg-green-500/20":"bg-red-500/20";return e.jsx("div",{className:m("w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full flex items-center justify-center",r),children:e.jsx("svg",{className:m("w-8 h-8 md:w-10 md:h-10",a),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}):e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})},vr=({item:t,quantity:a})=>e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-indigo-400/30",children:e.jsxs("div",{className:"flex items-center justify-center gap-3 md:gap-4",children:[e.jsxs("span",{className:"text-white text-lg md:text-xl font-semibold",children:[a??1,"x"]}),e.jsx(re,{item:t,height:"h-10 w-auto md:h-12 lg:h-14",className:"inline-block"}),e.jsx("span",{className:m("text-lg md:text-xl font-semibold",Se(t.rarity)),children:t.name})]})}),wr=({foragingType:t,difficulty:a,onStartForaging:r,isDisabled:i})=>{const s=et[a];return e.jsxs("button",{type:"button",disabled:i,className:m("cursor-pointer group relative overflow-hidden rounded-xl bg-gradient-to-br from-green-600 to-emerald-700","hover:from-green-500 hover:to-emerald-600 transition-all duration-300","border-2 border-green-400/30 hover:border-green-400/60","shadow-lg hover:shadow-xl transform hover:scale-[1.02]","disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none","p-6 md:p-8 w-full max-w-md mx-auto"),onClick:r,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e.jsxs("div",{className:"relative flex flex-col items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:br(t),className:"w-12 h-12 md:w-16 md:h-16 object-contain",alt:`${t} foraging`}),e.jsxs("div",{className:"text-left",children:[e.jsxs("h3",{className:"text-white font-bold text-lg md:text-xl capitalize",children:["Gather ",t]}),e.jsxs("div",{className:m("text-sm font-medium",s.color),children:[s.icon," ",s.label]})]})]}),e.jsx("p",{className:"text-gray-200 text-sm text-center",children:s.description})]})]})},jr=({foragingResult:t,onAction:a,isLoading:r,isMobile:i,actionButtonText:s})=>e.jsxs("div",{className:"flex flex-col items-center gap-6 md:gap-8 text-center",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(yr,{isSuccess:t.success}),e.jsx("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-2xl",children:t.message})]}),t.success&&t.itemReward&&e.jsx(vr,{item:t.itemReward,quantity:t.itemQuantity}),!t.success&&t.injury&&e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-red-400/30",children:e.jsx(Ae,{currentEffects:[{id:1,count:1,debuff:t.injury}],className:"justify-center"})}),e.jsx("div",{className:"w-full max-w-sm",children:e.jsx(pe,{variant:"primary",size:i?"md":"lg",isLoading:r,className:"w-full text-base md:text-lg",onClick:a,children:s})})]}),Nr=({nodeId:t,foragingType:a,difficulty:r,onClose:i})=>{const s=be(),[n,o]=p.useState(!1),[x,u]=p.useState(null),f=xe(),{invalidateQueries:c}=fr(),{mutate:d,isPending:b}=pr(),h=se("citystreet1");p.useEffect(()=>{s.start("visible")},[s]);const N=()=>{b||d({nodeId:t},{onSuccess:g=>{if(!g){u({success:!1,message:"No response received from server"});return}u({success:g.success||!1,message:g.message||"Foraging operation completed",itemReward:g.itemReward,itemQuantity:g.itemQuantity,injury:g.injury})},onError:g=>{u({success:!1,message:g?.message||"Something went wrong during foraging"})}})},C=async()=>{o(!0),await c(),i(),o(!1)},l=et[r];return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm",children:e.jsxs(M.div,{className:"relative w-full h-full max-w-6xl max-h-[90vh] overflow-hidden rounded-none md:rounded-2xl shadow-2xl",variants:ve.container,initial:"hidden",animate:s,children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("img",{className:"w-full h-full object-cover",src:h||"",alt:"Foraging location"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50"})]}),e.jsx(M.div,{className:"absolute inset-0 bg-black z-10",variants:ve.wipe,initial:"hidden",animate:"visible"}),e.jsx(M.div,{variants:ve.dialogueBox,initial:"hidden",animate:"visible",className:"relative z-20 h-full flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-4xl bg-slate-800/95 backdrop-blur-md border-4 border-green-500/80 rounded-2xl shadow-2xl",children:[e.jsxs("div",{className:"px-6 py-4 border-b border-green-500/30",children:[e.jsx("h2",{className:"text-center text-green-400 text-xl md:text-2xl lg:text-3xl font-semibold",children:"Foraging Expedition"}),e.jsx("div",{className:m("text-center mt-2 px-3 py-1 rounded-full inline-block",l.bgColor),children:e.jsxs("span",{className:m("text-sm font-medium",l.color),children:[l.icon," ",l.label," Difficulty"]})})]}),e.jsx("div",{className:"p-6 md:p-8 min-h-[400px] md:min-h-[500px] flex flex-col",children:x?e.jsx("div",{className:"flex-1 flex flex-col justify-center",children:e.jsx(jr,{foragingResult:x,isLoading:n,isMobile:f,actionButtonText:"Return to Explore",onAction:C})}):e.jsxs("div",{className:"flex-1 flex flex-col justify-center gap-6 md:gap-8",children:[e.jsx("div",{className:"text-center",children:e.jsxs("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto",children:["You've found a promising foraging site rich with"," ",e.jsx("span",{className:"text-green-400 font-semibold capitalize",children:a}),". Careful searching may yield valuable natural materials, but watch out for hazards."]})}),b?e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"inline-flex items-center gap-2 text-gray-300 text-base md:text-lg",children:[e.jsx("div",{className:"size-6 border-2 border-green-400 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Foraging in progress..."})]})}):e.jsx(wr,{foragingType:a,difficulty:r,isDisabled:b,onStartForaging:N})]})})]})})]})})},kr=()=>{const t=Y();return q(S.explore.processMiningOperation.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.user.getInventory.key()})}}))},we={container:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1}}},wipe:{hidden:{width:"100%",left:0},visible:{width:0,left:"50%",transition:{ease:"easeInOut",duration:.75}}},dialogueBox:{hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.3,ease:"easeOut"}}}},Re={coal:"https://img.icons8.com/?size=100&id=1437&format=png",copper:"https://img.icons8.com/?size=100&id=qf6FQ7o8i8DI&format=png",iron:"https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png",gold:"https://img.icons8.com/?size=100&id=43138&format=png",crystal:"https://img.icons8.com/?size=100&id=fDyZg5u1hAeF&format=png",gems:"https://img.icons8.com/?size=100&id=11234&format=png",ore:"https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png"},tt={easy:{color:"text-green-400",bgColor:"bg-green-500/20 border-green-400/30",icon:"⚡",label:"Easy",description:"Low risk, steady rewards"},medium:{color:"text-yellow-400",bgColor:"bg-yellow-500/20 border-yellow-400/30",icon:"⚠️",label:"Medium",description:"Moderate risk, better rewards"},hard:{color:"text-red-400",bgColor:"bg-red-500/20 border-red-400/30",icon:"💀",label:"Hard",description:"High risk, valuable rewards"}},Cr=t=>Re[t]||Re.ore,Ar=()=>{const t=Y();return{invalidateQueries:async()=>{await Promise.all([t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),t.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()})])}}},Ir=({isSuccess:t})=>{const a=t?"text-green-400":"text-red-400",r=t?"bg-green-500/20":"bg-red-500/20";return e.jsx("div",{className:m("w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full flex items-center justify-center",r),children:e.jsx("svg",{className:m("w-8 h-8 md:w-10 md:h-10",a),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}):e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})},Sr=({item:t,quantity:a})=>e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-indigo-400/30",children:e.jsxs("div",{className:"flex items-center justify-center gap-3 md:gap-4",children:[e.jsxs("span",{className:"text-white text-lg md:text-xl font-semibold",children:[a??1,"x"]}),e.jsx(re,{item:t,height:"h-10 w-auto md:h-12 lg:h-14",className:"inline-block"}),e.jsx("span",{className:m("text-lg md:text-xl font-semibold",Se(t.rarity)),children:t.name})]})}),Tr=({miningType:t,difficulty:a,onStartMining:r,isDisabled:i})=>{const s=tt[a];return e.jsxs("button",{type:"button",disabled:i,className:m("cursor-pointer group relative overflow-hidden rounded-xl bg-gradient-to-br from-amber-600 to-orange-700","hover:from-amber-500 hover:to-orange-600 transition-all duration-300","border-2 border-amber-400/30 hover:border-amber-400/60","shadow-lg hover:shadow-xl transform hover:scale-[1.02]","disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none","p-6 md:p-8 w-full max-w-md mx-auto"),onClick:r,children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e.jsxs("div",{className:"relative flex flex-col items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:Cr(t),className:"w-12 h-12 md:w-16 md:h-16 object-contain",alt:`${t} mining`}),e.jsxs("div",{className:"text-left",children:[e.jsxs("h3",{className:"text-white font-bold text-lg md:text-xl capitalize",children:["Mine ",t]}),e.jsxs("div",{className:m("text-sm font-medium",s.color),children:[s.icon," ",s.label]})]})]}),e.jsx("p",{className:"text-gray-200 text-sm text-center",children:s.description})]})]})},Pe=({miningResult:t,onAction:a,isLoading:r,isMobile:i,actionButtonText:s,actionButtonVariant:n="primary"})=>e.jsxs("div",{className:"flex flex-col items-center gap-6 md:gap-8 text-center",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(Ir,{isSuccess:t.success}),e.jsx("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-2xl",children:t.message})]}),t.success&&t.itemReward&&e.jsx(Sr,{item:t.itemReward,quantity:t.itemQuantity}),!t.success&&t.injury&&e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-red-400/30",children:e.jsx(Ae,{currentEffects:[{id:1,count:1,debuff:t.injury}],className:"justify-center"})}),e.jsx("div",{className:"w-full max-w-sm",children:e.jsx(pe,{variant:n,size:i?"md":"lg",isLoading:r,className:"w-full text-base md:text-lg",onClick:a,children:s})})]}),Er=({nodeId:t,miningType:a,difficulty:r,onClose:i})=>{const s=be(),[n,o]=p.useState(!1),[x,u]=p.useState(null),f=xe(),c=V(),{invalidateQueries:d}=Ar(),{mutate:b,isPending:h}=kr(),N=se("citystreet1");p.useEffect(()=>{s.start("visible")},[s]);const C=()=>{h||b({nodeId:t},{onSuccess:k=>{u({success:k.success||!1,message:k.message||"Mining operation completed",itemReward:k.itemReward,itemQuantity:k.itemQuantity,injury:k.injury})},onError:k=>{u({success:!1,message:k.message||"Something went wrong during mining"})}})},l=async()=>{o(!0),await d(),i(),o(!1)},g=async()=>{o(!0),await d(),c("/jail")},A=()=>x?x.jailed?e.jsx(Pe,{miningResult:x,isLoading:n,isMobile:f,actionButtonText:"Go to Jail",actionButtonVariant:"destructive",onAction:g}):e.jsx(Pe,{miningResult:x,isLoading:n,isMobile:f,actionButtonText:"Return to Explore",onAction:l}):null,j=tt[r];return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm",children:e.jsxs(M.div,{className:"relative w-full h-full max-w-6xl max-h-[90vh] overflow-hidden rounded-none md:rounded-2xl shadow-2xl",variants:we.container,initial:"hidden",animate:s,children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("img",{className:"w-full h-full object-cover",src:N||"",alt:"Mining location"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50"})]}),e.jsx(M.div,{className:"absolute inset-0 bg-black z-10",variants:we.wipe,initial:"hidden",animate:"visible"}),e.jsx(M.div,{variants:we.dialogueBox,initial:"hidden",animate:"visible",className:"relative z-20 h-full flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-4xl bg-slate-800/95 backdrop-blur-md border-4 border-amber-500/80 rounded-2xl shadow-2xl",children:[e.jsxs("div",{className:"px-6 py-4 border-b border-amber-500/30",children:[e.jsx("h2",{className:"text-center text-amber-400 text-xl md:text-2xl lg:text-3xl font-semibold",children:"Mining Operation"}),e.jsx("div",{className:m("text-center mt-2 px-3 py-1 rounded-full inline-block",j.bgColor),children:e.jsxs("span",{className:m("text-sm font-medium",j.color),children:[j.icon," ",j.label," Difficulty"]})})]}),e.jsx("div",{className:"p-6 md:p-8 min-h-[400px] md:min-h-[500px] flex flex-col",children:x?e.jsx("div",{className:"flex-1 flex flex-col justify-center",children:A()}):e.jsxs("div",{className:"flex-1 flex flex-col justify-center gap-6 md:gap-8",children:[e.jsx("div",{className:"text-center",children:e.jsxs("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto",children:["You've discovered a rich mining site with"," ",e.jsx("span",{className:"text-amber-400 font-semibold capitalize",children:a})," ","deposits. The mining operation will consume energy and carries risks, but may yield valuable materials."]})}),h?e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"inline-flex items-center gap-2 text-gray-300 text-base md:text-lg",children:[e.jsx("div",{className:"size-6 border-2 border-amber-400 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Mining in progress..."})]})}):e.jsx(Tr,{miningType:a,difficulty:r,isDisabled:h,onStartMining:C})]})})]})})]})})};function Or(t){const a={trash_medical:{location:"Abandoned Clinic",description:"While scavenging, you come across an abandoned clinic in a back alley. Peeking inside, you see overflowing garbage bins and medical supplies scattered around. The clinic's exterior is covered in graffiti, and its interior contains old medical equipment and documents.",choices:{trash:{action:"Scavenge through outdoor trash bins",success:"Digging through the clinic's dumpster, you uncover some usable items beneath the refuse:",failureInjury:"While lifting a heavy bag of medical waste, you feel a sharp pain in your side. You've bruised your ribs.",injury:"fracture_injury",failureJail:"A passing patrol spots you trespassing on private property. You're escorted to the local precinct for a 'chat'."},medical:{action:"Search for medical supplies inside",success:"Amidst the cluttered shelves and drawers, you find some valuable medical supplies:",failureInjury:"You trip over scattered debris, twisting your ankle badly as you fall.",injury:"trauma_injury",failureJail:"Alarms you didn't notice start blaring. Before you can escape, security arrives and detains you."}}},trash_upgrade:{location:"Discarded Electronics Store",description:"While scavenging, you find an old electronics store that has been out of business for years. Piles of outdated gadgets and their packaging litter the floor. Among the trash, you notice components and tools that can be repurposed for upgrades.",choices:{trash:{action:"Sort through piles of outdated gadgets",success:"Sifting through the electronic junk, you salvage some useful items:",failureInjury:"A jagged piece of metal slices your hand as you dig through the pile. The cut is deep and bleeding profusely.",injury:"bleeding_injury",failureJail:"The store owner catches you in the act. He calls the police, and you're taken in for attempted burglary."},upgrade:{action:"Look for repurposable components",success:"You carefully extract some promising components from the old devices:",failureInjury:"After hours of meticulous searching, you're overcome with minor fatigue, your vision blurring from exhaustion.",injury:"fatigue_injury",failureJail:"Your scavenging triggers a silent alarm. The police arrive and arrest you for trespassing and theft."}}},trash_herb:{location:"Neglected Urban Garden",description:"While scavenging, you come across a neglected urban community garden. Trash bags and litter are strewn about, but amidst the neglect, wild herbs and plants still grow.",choices:{trash:{action:"Check litter and trash bags",success:"Rummaging through the garden's debris, you find some discarded but useful items:",failureInjury:"You slip on wet leaves and bang your head against a planter. A mild headache sets in, possibly indicating a minor concussion.",injury:"concussion_injury",failureJail:"A community volunteer spots you and calls the police, reporting you for vandalism."},herb:{action:"Search among overgrown plants",success:"Carefully pushing aside the overgrowth, you discover some thriving herbs:",failureInjury:"You stumble into a thorny bush, leaving your arms covered in cuts and scrapes.",injury:"bleeding_injury",failureJail:"A local gardening club member mistakes you for a plant thief and calls the authorities."}}},trash_tech:{location:"Abandoned Internet Café",description:"While scavenging, you find a once-bustling internet café now left to decay. Broken chairs, food wrappers, and old computers are scattered throughout. Among the trash, you spot some usable tech components.",choices:{trash:{action:"Dig through debris and wrappers",success:"Sorting through layers of café detritus, you unearth some valuable items:",failureInjury:"A precariously balanced pile of old monitors topples onto you, leaving you with painful skin bruises all over.",injury:"contusion_injury",failureJail:"The café owner arrives to check on the property and catches you in the act. Off to jail you go!"},tech:{action:"Inspect old computers and equipment",success:"After a thorough examination of the outdated machines, you extract some useful components:",failureInjury:"You accidentally touch an exposed wire, receiving a shock that leaves you with minor fatigue and muscle weakness.",injury:"fatigue_injury",failureJail:"Your tampering sets off an old security system. The police arrive and arrest you for breaking and entering."}}},trash_ore:{location:"Derelict Construction Site",description:"While scavenging, you arrive at an unfinished construction site, abandoned and filled with debris. Trash piles mix with construction materials, including metal ores and other valuable resources.",choices:{trash:{action:"Rummage through debris and trash",success:"Picking through the construction site's refuse, you recover some usable items:",failureInjury:"A stack of metal pipes shifts unexpectedly, falling on your leg and causing a large skin bruise.",injury:"contusion_injury",failureJail:"A security guard on his rounds spots you trespassing. He calls the police, and you're arrested."},ore:{action:"Search construction materials",success:"Examining the abandoned construction materials, you find some valuable resources:",failureInjury:"While lifting a heavy piece of ore, you strain your back. The trauma to your muscles is immediate and painful.",injury:"trauma_injury",failureJail:"Your activities attract the attention of a passing patrol. They detain you for theft of construction materials."}}},medical_upgrade:{location:"Ransacked Pharmacy",description:"While scavenging, you enter a pharmacy that has been looted. Shelves are overturned, and supplies are scattered. Among the chaos, you find medical supplies mixed with items that can be used to enhance equipment.",choices:{medical:{action:"Look for remaining medical supplies",success:"Searching through the ransacked shelves, you manage to find some untouched medical supplies:",failureInjury:"You slip on spilled liquid medicines, twisting your ankle badly as you fall.",injury:"trauma_injury",failureJail:"The pharmacy's silent alarm is still active. Police arrive and arrest you for looting."},upgrade:{action:"Find items to enhance gear",success:"Among the scattered pharmacy goods, you discover some items perfect for gear enhancement:",failureInjury:"A shelving unit collapses as you search, striking your chest and leaving you with bruised ribs.",injury:"fracture_injury",failureJail:"A vigilant neighbor spots you and reports a break-in. The police arrive swiftly to apprehend you."}}},medical_herb:{location:"Traditional Herbalist Shop",description:"While scavenging, you discover a small herbalist shop in an old district, partially damaged but still containing various herbs and medical supplies. The air is filled with the scent of dried herbs.",choices:{medical:{action:"Search for medical supplies",success:"In the back of the shop, you uncover a cache of well-preserved medical supplies:",failureInjury:"You accidentally inhale powdered herbs, triggering a coughing fit that leaves you with minor fatigue.",injury:"fatigue_injury",failureJail:"The shop owner returns unexpectedly. Mistaking you for a burglar, he calls the police."},herb:{action:"Gather stored herbs",success:"Carefully sorting through the aromatic herbs, you collect some potent specimens:",failureInjury:"You mistake a toxic herb for a harmless one, and handling it causes cuts and scrapes on your hands.",injury:"bleeding_injury",failureJail:"A local herbalist recognizes you're not the shop owner. She alerts the authorities, leading to your arrest."}}},medical_tech:{location:"High-Tech Medical Facility",description:"While scavenging, you come across a state-of-the-art medical research facility that was hastily evacuated. Advanced medical technology and supplies are left behind, ripe for scavenging.",choices:{medical:{action:"Collect advanced medical supplies",success:"From the facility's stores, you gather some cutting-edge medical supplies:",failureInjury:"You bump your head on an open cabinet door, leaving you with a mild headache that could be a minor concussion.",injury:"concussion_injury",failureJail:"The facility's AI security system detects your presence and locks down the building until authorities arrive."},tech:{action:"Scavenge high-tech devices",success:"You manage to salvage some advanced technological devices from the facility:",failureInjury:"An experimental device misfires, zapping you and causing minor fatigue and disorientation.",injury:"fatigue_injury",failureJail:"Your tampering with the equipment triggers a biohazard alarm. A HAZMAT team arrives, followed by the police."}}},medical_ore:{location:"Research Laboratory",description:"While scavenging, you find a laboratory that focuses on medical and material sciences. It contains both medical supplies and samples of various ores used in their experiments.",choices:{medical:{action:"Gather medical research supplies",success:"From the lab's inventory, you collect some specialized medical research supplies:",failureInjury:"You accidentally knock over a heavy medical device, causing trauma to your foot as it lands on it.",injury:"trauma_injury",failureJail:"A late-working scientist catches you in the act and immediately calls security."},ore:{action:"Collect ore samples",success:"You carefully extract some rare ore samples from the laboratory's storage:",failureInjury:"While handling a sample, you cut your hand on a sharp edge. The cut is deep and bleeding.",injury:"bleeding_injury",failureJail:"Your attempts to access a secured ore storage trigger a silent alarm. Security arrives promptly."}}},upgrade_herb:{location:"Rooftop Garden Workshop",description:"While scavenging, you reach a rooftop garden that doubles as a workshop. Amidst the plants and herbs growing in containers, there are tools and materials for crafting and upgrades.",choices:{upgrade:{action:"Search workshop for tools",success:"Rifling through the workshop, you find some useful tools and materials:",failureInjury:"You lose your footing on the wet rooftop and fall, twisting your ankle badly.",injury:"trauma_injury",failureJail:"The building's security spots you on the roof. They detain you until the police arrive."},herb:{action:"Pick herbs from containers",success:"From the rooftop containers, you harvest a selection of healthy herbs:",failureInjury:"The sweltering heat on the rooftop leaves you with minor fatigue and dizziness.",injury:"fatigue_injury",failureJail:"A resident spots you from their window and reports you for trespassing and theft."}}},upgrade_tech:{location:"Innovator's Garage",description:"While scavenging, you enter a tinkerer's garage filled with half-finished projects and cutting-edge technology. Upgrade components are scattered among tech gadgets and tools.",choices:{upgrade:{action:"Find upgrade components",success:"Sifting through the innovator's projects, you salvage some valuable upgrade components:",failureInjury:"An unstable prototype explodes in your hands, leaving you with cuts and scrapes all over your arms.",injury:"bleeding_injury",failureJail:"The garage's owner returns and catches you red-handed. He immediately calls the police."},tech:{action:"Scavenge high-tech gadgets",success:"From the array of gadgets, you manage to extract some advanced technological items:",failureInjury:"A malfunctioning robotic arm swings wildly, striking your chest and leaving you with bruised ribs.",injury:"fracture_injury",failureJail:"Your tinkering activates a high-pitched alarm. Neighbors call the police, who quickly arrive."}}},upgrade_ore:{location:"Mechanic's Yard",description:"While scavenging, you explore an open yard used by a mechanic for salvaging and repurposing old machinery. Metal ores and various upgrade parts are plentiful here.",choices:{upgrade:{action:"Look for upgrade parts",success:"Searching through the mechanic's collection, you find some useful upgrade parts:",failureInjury:"A heavy machine part falls, striking your leg and causing a large skin bruise.",injury:"contusion_injury",failureJail:"The mechanic's guard dogs corner you in the yard. Their barking alerts the police."},ore:{action:"Collect metal ores",success:"From the yard's resources, you gather a variety of valuable metal ores:",failureInjury:"The strain of carrying heavy ores leaves you with minor fatigue and muscle aches.",injury:"fatigue_injury",failureJail:"A night watchman spots you loading ores into your bag. He detains you until the police arrive."}}},herb_tech:{location:"Experimental Bio-Lab",description:"While scavenging, you come across a bio-laboratory that blends nature and technology. Herb samples are grown alongside high-tech devices designed to study and enhance their properties.",choices:{herb:{action:"Gather herb samples",success:"From the bio-lab's greenhouse, you collect some rare and potent herb samples:",failureInjury:"You bump your head on a low-hanging hydroponic system, causing a mild headache that might be a minor concussion.",injury:"concussion_injury",failureJail:"The lab's containment protocols activate, trapping you inside until the authorities arrive."},tech:{action:"Scavenge research devices",success:"You manage to salvage some advanced bio-tech research devices:",failureInjury:"A device designed to analyze plant growth accidentally pricks your skin, leaving you with minor cuts and scrapes.",injury:"bleeding_injury",failureJail:"Your tampering with the lab equipment triggers a biohazard alarm. A response team arrives and detains you."}}},herb_ore:{location:"Botanical Research Center",description:"While scavenging, you find a facility dedicated to studying the natural properties of plants and minerals. Various herbs and mineral ores are stored here for research purposes.",choices:{herb:{action:"Collect research herbs",success:"From the center's vast collection, you gather some unique research-grade herbs:",failureInjury:"You slip on a wet floor, twisting your ankle painfully as you fall.",injury:"trauma_injury",failureJail:"A late-night researcher catches you in the restricted area and calls security."},ore:{action:"Gather mineral ores",success:"Searching through the facility's stores, you collect some rare mineral ore samples:",failureInjury:"While reaching for a high shelf, you strain your back. The muscle trauma is immediate and painful.",injury:"trauma_injury",failureJail:"Your presence triggers a silent alarm. Police arrive to find you surrounded by valuable research materials."}}},tech_ore:{location:"Abandoned Factory",description:"While scavenging, you come across an old factory that manufactured tech devices and components. The place is filled with discarded tech and raw ores used in production, waiting to be scavenged.",choices:{tech:{action:"Search for tech components",success:"Digging through the factory's leftovers, you salvage some valuable tech components:",failureInjury:"You accidentally activate an old industrial laser cutter, which grazes your arm. That's going to leave a scar.",injury:"bleeding_injury",failureJail:"Your rummaging triggers an old security system. Local police respond to the silent alarm and find you amidst the valuable tech."},ore:{action:"Scavenge raw ores",success:"From the factory's raw materials, you gather a selection of useful ores:",failureInjury:"A unstable pile of ore samples collapses as you approach, partially burying you and leaving you with multiple contusions.",injury:"contusion_injury",failureJail:"A security guard on a routine check spots you hauling ore from the premises. He detains you until the authorities arrive."}}}},r=[...t].sort().join("_"),i=r.split("_").reverse().join("_");return a[r]||a[i]||{location:"Unknown",description:"No description available.",choices:{}}}const Mr=()=>{const t=Y();return q(S.explore.makeScavengeChoice.mutationOptions({onSuccess:(a,r,i)=>{t.invalidateQueries({queryKey:S.user.getInventory.key()})}}))},je={container:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:1}}},wipe:{hidden:{width:"100%",left:0},visible:{width:0,left:"50%",transition:{ease:"easeInOut",duration:.75}}},dialogueBox:{hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.3,ease:"easeOut"}}}},zr={trash:"https://img.icons8.com/?size=100&id=67367&format=png",medical:"https://img.icons8.com/?size=100&id=vB6PoShzdZKw&format=png",upgrade:"https://img.icons8.com/?size=100&id=HA930KjFD9ki&format=png",herb:"https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png",tech:"https://img.icons8.com/?size=100&id=fzdr7mjSaS_w&format=png",ore:"https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png"},Dr=t=>zr[t]||ft,Lr=t=>t?"failureInjury":"failureJail",Br=()=>{const t=Y();return{invalidateQueries:async()=>{await Promise.all([t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),t.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()})])}}},Rr=({isSuccess:t})=>{const a=t?"text-green-400":"text-red-400",r=t?"bg-green-500/20":"bg-red-500/20";return e.jsx("div",{className:m("w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full flex items-center justify-center",r),children:e.jsx("svg",{className:m("w-8 h-8 md:w-10 md:h-10",a),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}):e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})},Pr=({item:t,quantity:a})=>e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-indigo-400/30",children:e.jsxs("div",{className:"flex items-center justify-center gap-3 md:gap-4",children:[e.jsxs("span",{className:"text-white text-lg md:text-xl font-semibold",children:[a??1,"x"]}),e.jsx(re,{item:t,height:"h-10 w-auto md:h-12 lg:h-14",className:"inline-block"}),e.jsx("span",{className:m("text-lg md:text-xl font-semibold",Se(t.rarity)),children:t.name})]})}),Yr=({choice:t,index:a,onSelect:r,isDisabled:i,locationDetails:s})=>{const n=s?.choices?.[t]?.action||"";return e.jsxs("button",{type:"button",disabled:i,className:m("cursor-pointer group relative overflow-hidden rounded-xl bg-gradient-to-br from-indigo-600 to-purple-700","hover:from-indigo-500 hover:to-purple-600 transition-all duration-300","border-2 border-indigo-400/30 hover:border-indigo-400/60","shadow-lg hover:shadow-xl transform hover:scale-[1.02]","disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none","p-6 md:p-8 h-24 md:h-32 lg:h-36"),onClick:()=>r(a+1),children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e.jsxs("div",{className:"relative flex items-center justify-center gap-3 md:gap-4 h-full",children:[e.jsx("img",{src:Dr(t),className:"w-8 h-8 md:w-12 md:h-12 lg:w-16 lg:h-16 object-contain",alt:`${t} icon`}),e.jsx("span",{className:"text-white font-semibold text-sm md:text-base lg:text-lg uppercase tracking-wide",children:n})]})]})},Ye=({scavengeResult:t,resultText:a,onAction:r,isLoading:i,isMobile:s,actionButtonText:n,actionButtonVariant:o="primary"})=>e.jsxs("div",{className:"flex flex-col items-center gap-6 md:gap-8 text-center",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(Rr,{isSuccess:t.success}),e.jsx("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-2xl",children:a})]}),t.success&&t.itemReward&&e.jsx(Pr,{item:t.itemReward,quantity:t.itemQuantity}),!t.success&&t.injury&&e.jsx("div",{className:"bg-slate-700/50 rounded-xl p-4 md:p-6 border border-red-400/30",children:e.jsx(Ae,{currentEffects:[{id:1,count:1,debuff:t.injury}],className:"justify-center"})}),e.jsx("div",{className:"w-full max-w-sm",children:e.jsx(pe,{variant:o,size:s?"md":"lg",isLoading:i,className:"w-full text-base md:text-lg",onClick:r,children:n})})]}),Gr=({nodeId:t,choices:a,onClose:r})=>{const i=be(),[s,n]=p.useState(!1),[o,x]=p.useState(null),u=xe(),f=V(),{invalidateQueries:c}=Br(),{mutate:d,isPending:b}=Mr(),h=Or(a),N=se("citystreet1");p.useEffect(()=>{i.start("visible")},[i]);const C=j=>{b||d({nodeId:t,choiceIndex:j},{onSuccess:k=>{x({success:k.success||!1,choice:k.choice||a[j-1],message:k.message||"Scavenging completed",itemReward:k.itemReward,itemQuantity:k.itemQuantity,jailed:k.jailed,jailDuration:k.jailDuration,injury:k.injury})},onError:k=>{x({success:!1,choice:a[j-1],message:k.message||"Something went wrong during scavenging"})}})},l=async()=>{n(!0),await c(),r(),n(!1)},g=async()=>{n(!0),await c(),f("/jail")},A=()=>{if(!o)return null;const j=Lr(!!o.injury),k=o.success?h?.choices?.[o.choice]?.success||"":h?.choices?.[o.choice]?.[j]||"";return o.success?e.jsx(Ye,{scavengeResult:o,resultText:k,isLoading:s,isMobile:u,actionButtonText:"Return to Explore",onAction:l}):e.jsx(Ye,{scavengeResult:o,resultText:k,isLoading:s,isMobile:u,actionButtonText:o.jailed?"Go to Jail":"Continue Exploring",actionButtonVariant:o.jailed?"destructive":"primary",onAction:o.jailed?g:l})};return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm",children:e.jsxs(M.div,{className:"relative w-full h-full max-w-6xl max-h-[90vh] overflow-hidden rounded-none md:rounded-2xl shadow-2xl",variants:je.container,initial:"hidden",animate:i,children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("img",{className:"w-full h-full object-cover",src:N||"",alt:"Scavenging location"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50"})]}),e.jsx(M.div,{className:"absolute inset-0 bg-black z-10",variants:je.wipe,initial:"hidden",animate:"visible"}),e.jsx(M.div,{variants:je.dialogueBox,initial:"hidden",animate:"visible",className:"relative z-20 h-full flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-4xl bg-slate-800/95 backdrop-blur-md border-4 border-indigo-500/80 rounded-2xl shadow-2xl",children:[e.jsx("div",{className:"px-6 py-4 border-b border-indigo-500/30",children:e.jsx("h2",{className:"text-center text-indigo-400 text-xl md:text-2xl lg:text-3xl font-semibold",children:h?.location})}),e.jsx("div",{className:"p-6 md:p-8 min-h-[400px] md:min-h-[500px] flex flex-col",children:o?e.jsx("div",{className:"flex-1 flex flex-col justify-center",children:A()}):e.jsxs("div",{className:"flex-1 flex flex-col justify-center gap-6 md:gap-8",children:[e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto",children:h?.description})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 max-w-4xl mx-auto w-full",children:b?e.jsx("div",{className:"text-center",children:e.jsx("div",{className:"inline-flex items-center gap-2 text-gray-300 text-sm md:text-base",children:e.jsx("div",{className:"size-6 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"})})}):a.map((j,k)=>e.jsx(Yr,{choice:j,index:k,isDisabled:b,locationDetails:h,onSelect:C},j))})]})})]})})]})})},W=[{id:"shibuya",name:"Shibuya",cost:100,description:"A bustling commercial and entertainment district known for its shopping and nightlife.",position:{x:45,y:55},background:"bg-gradient-to-br from-pink-900/30 to-purple-900/30",atmosphere:{primaryColor:"from-pink-600 to-purple-700",accentColor:"border-pink-500",glowColor:"shadow-pink-500/20",theme:"neon-commercial"},features:["Shopping Districts","Youth Culture","Busy Crossings"],travelTimes:{walk:15,bus:4}},{id:"shinjuku",name:"Shinjuku",cost:150,description:"A major commercial and administrative center with the world's busiest railway station.",position:{x:35,y:30},background:"bg-gradient-to-br from-red-900/30 to-orange-900/30",atmosphere:{primaryColor:"from-red-600 to-orange-700",accentColor:"border-red-500",glowColor:"shadow-red-500/20",theme:"urban-chaos"},features:["Business District","Government Buildings","Entertainment"],travelTimes:{walk:20,bus:5}},{id:"bunkyo",name:"Bunkyo",cost:200,description:"A district known for its traditional architecture and cultural heritage.",position:{x:65,y:35},background:"bg-gradient-to-br from-blue-900/30 to-cyan-900/30",atmosphere:{primaryColor:"from-blue-600 to-cyan-700",accentColor:"border-blue-500",glowColor:"shadow-blue-500/20",theme:"tech-paradise"},features:["Traditional Architecture","Cultural Heritage","Historical Sites"],travelTimes:{walk:25,bus:7}},{id:"chiyoda",name:"Chiyoda",cost:250,description:"The political center of Tokyo, containing the Imperial Palace and government buildings.",position:{x:55,y:20},background:"bg-gradient-to-br from-emerald-900/30 to-green-900/30",atmosphere:{primaryColor:"from-emerald-600 to-green-700",accentColor:"border-emerald-500",glowColor:"shadow-emerald-500/20",theme:"imperial-formal"},features:["Imperial Palace","Government District","Historic Sites"],travelTimes:{walk:30,bus:8}},{id:"minato",name:"Minato",cost:300,description:"An upscale district with many embassies, corporate headquarters, and Tokyo Tower.",position:{x:70,y:65},background:"bg-gradient-to-br from-indigo-900/30 to-violet-900/30",atmosphere:{primaryColor:"from-indigo-600 to-violet-700",accentColor:"border-indigo-500",glowColor:"shadow-indigo-500/20",theme:"luxury-corporate"},features:["Luxury Shopping","Embassies","Tokyo Tower"],travelTimes:{walk:35,bus:9}}],Qr={tokyo:"https://media.sketchfab.com/models/a05e11c729f84bc4bc3e7d791c461947/thumbnails/445b23029bda4c4297e23c6404909275/9a84ce44cd024d99b00f59a5c8864326.jpeg",shibuya:"https://i.ibb.co/gF71hMMk/shibuya-20000-sdb-7c434f-preview-1.png",shinjuku:"https://i.imgur.com/BLkvr0i.png",bunkyo:"https://i.imgur.com/FqJ1RoJ.png",chiyoda:"https://i.imgur.com/BeEDDBw.png",minato:"https://i.imgur.com/HnOe3Tv.png"},at=({currentView:t,className:a})=>{const r=t?W.find(i=>i.id===t):null;return e.jsxs("div",{className:`absolute inset-0 w-full h-full ${a||""}`,children:[e.jsx("img",{src:Qr[t],alt:"Map Background",className:m("w-full h-full object-cover",t==="tokyo"&&"scale-150")}),r&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"absolute inset-0 pointer-events-none mix-blend-multiply opacity-30",style:{background:`linear-gradient(to br, ${Ja(r.atmosphere.primaryColor)})`}}),r.atmosphere.theme==="neon-commercial"&&e.jsx("div",{className:"absolute inset-0 pointer-events-none",children:e.jsx("div",{className:"absolute w-full h-full opacity-20 animate-pulse",style:{background:`
                                        radial-gradient(ellipse at 20% 30%, rgba(255, 20, 147, 0.3) 0%, transparent 40%),
                                        radial-gradient(ellipse at 80% 70%, rgba(138, 43, 226, 0.3) 0%, transparent 40%)
                                    `}})}),r.atmosphere.theme==="urban-chaos"&&e.jsx("div",{className:"absolute inset-0 pointer-events-none",children:e.jsx("div",{className:"absolute w-full h-full opacity-25",style:{background:`
                                        radial-gradient(circle at 30% 20%, rgba(220, 38, 38, 0.3) 0%, transparent 30%),
                                        radial-gradient(circle at 70% 80%, rgba(251, 146, 60, 0.3) 0%, transparent 30%)
                                    `}})}),r.atmosphere.theme==="tech-paradise"&&e.jsx("div",{className:"absolute inset-0 pointer-events-none",children:e.jsx("div",{className:"absolute w-full h-full opacity-20",style:{background:`
                                        linear-gradient(45deg, rgba(37, 99, 235, 0.2) 0%, transparent 50%),
                                        linear-gradient(-45deg, rgba(6, 182, 212, 0.2) 0%, transparent 50%)
                                    `}})})]}),e.jsx("div",{className:"absolute inset-0 pointer-events-none opacity-10",style:{backgroundImage:`
                        linear-gradient(to right, rgba(128, 90, 213, 0.2) 1px, transparent 1px),
                        linear-gradient(to bottom, rgba(128, 90, 213, 0.2) 1px, transparent 1px)
                    `,backgroundSize:"50px 50px"}})]})};function Fr({className:t,...a}){return e.jsx("div",{"data-slot":"card",className:m("bg-white text-zinc-950 flex flex-col gap-6 rounded-xl border border-zinc-200 py-6 shadow-sm dark:bg-zinc-950 dark:text-zinc-50 dark:border-zinc-800",t),...a})}function Hr({className:t,...a}){return e.jsx("div",{"data-slot":"card-content",className:m("px-6",t),...a})}const Ur=Fe("inline-flex items-center justify-center rounded-md border border-zinc-200 px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-zinc-950 focus-visible:ring-zinc-950/50 focus-visible:ring-[3px] aria-invalid:ring-red-500/20 dark:aria-invalid:ring-red-500/40 aria-invalid:border-red-500 transition-[color,box-shadow] overflow-hidden dark:border-zinc-800 dark:focus-visible:border-zinc-300 dark:focus-visible:ring-zinc-300/50 dark:aria-invalid:ring-red-900/20 dark:dark:aria-invalid:ring-red-900/40 dark:aria-invalid:border-red-900",{variants:{variant:{default:"border-transparent bg-zinc-900 text-zinc-50 [a&]:hover:bg-zinc-900/90 dark:bg-zinc-50 dark:text-zinc-900 dark:[a&]:hover:bg-zinc-50/90",secondary:"border-transparent bg-zinc-100 text-zinc-900 [a&]:hover:bg-zinc-100/90 dark:bg-zinc-800 dark:text-zinc-50 dark:[a&]:hover:bg-zinc-800/90",destructive:"border-transparent bg-red-500 text-white [a&]:hover:bg-red-500/90 focus-visible:ring-red-500/20 dark:focus-visible:ring-red-500/40 dark:bg-red-500/60 dark:bg-red-900 dark:[a&]:hover:bg-red-900/90 dark:focus-visible:ring-red-900/20 dark:dark:focus-visible:ring-red-900/40 dark:dark:bg-red-900/60",outline:"text-zinc-950 [a&]:hover:bg-zinc-100 [a&]:hover:text-zinc-900 dark:text-zinc-50 dark:[a&]:hover:bg-zinc-800 dark:[a&]:hover:text-zinc-50"}},defaultVariants:{variant:"default"}});function Ge({className:t,variant:a,asChild:r=!1,...i}){const s=r?yt:"span";return e.jsx(s,{"data-slot":"badge",className:m(Ur({variant:a}),t),...i})}const Wr=()=>{const t=Y();return q(S.story.completeEpisode.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.quests.getAvailable.key()}),t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})}}))},Jr=({episodeData:t,onClose:a})=>{const r=V(),i=Wr(),[s,n]=p.useState(0),o=t,x=o?.content,u=x?.scenes||[],f=u[s],c=async()=>{try{if(!o?.id){console.error("No episode ID available");return}await i.mutateAsync({episodeId:o.id,choices:{}}),a()}catch(C){console.error("Failed to complete episode:",C)}},d=()=>{s<u.length-1?n(s+1):c()},b=()=>{s>0&&n(s-1)},h=()=>{n(u.length-1)};if(!o||!x)return e.jsx("div",{className:"flex items-center justify-center h-screen",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Episode Not Found"}),e.jsxs(te,{onClick:()=>r("/explore"),children:[e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Back to Explore"]})]})});const N=(s+1)/u.length*100;return e.jsxs("div",{className:"min-h-screen bg-gradient-to-b from-slate-900 to-slate-800 text-white",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-slate-900/90 backdrop-blur-sm border-b border-slate-700",children:e.jsxs("div",{className:"container mx-auto px-4 py-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(te,{variant:"ghost",size:"sm",className:"text-white hover:bg-slate-700",onClick:()=>r("/explore"),children:[e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg font-semibold",children:o.name}),e.jsx(Ge,{variant:"secondary",className:"text-xs",children:o.episodeType})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(te,{variant:"ghost",size:"sm",className:"text-white hover:bg-slate-700",onClick:h,children:e.jsx(aa,{className:"h-4 w-4"})})})]}),e.jsxs("div",{className:"mt-3",children:[e.jsxs("div",{className:"flex justify-between text-sm text-slate-400 mb-1",children:[e.jsxs("span",{children:["Scene ",s+1," of ",u.length]}),e.jsxs("span",{children:[Math.round(N),"%"]})]}),e.jsx(Lt,{value:N,className:"h-2"})]})]})}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:f&&e.jsx(Fr,{className:"bg-slate-800/50 border-slate-600 backdrop-blur-sm",children:e.jsx(Hr,{className:"p-8",children:e.jsxs("div",{className:"space-y-6",children:[f.speaker&&e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center",children:e.jsx("span",{className:"text-lg font-bold",children:f.speaker.charAt(0)})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-lg",children:f.speaker}),f.character&&e.jsx("p",{className:"text-sm text-slate-400",children:f.character})]})]}),e.jsx("div",{className:"prose prose-invert max-w-none",children:e.jsx("p",{className:"text-lg leading-relaxed",children:f.text})}),f.effects&&f.effects.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2",children:f.effects.map((C,l)=>e.jsx(Ge,{variant:"outline",className:"text-xs",children:C},l))}),f.type!=="choice"&&e.jsxs("div",{className:"flex justify-between items-center mt-8 pt-6 border-t border-slate-600",children:[e.jsxs(te,{variant:"ghost",disabled:s===0,className:"text-white hover:bg-slate-700",onClick:b,children:[e.jsx(ue,{className:"h-4 w-4 mr-2"}),"Previous"]}),e.jsx(te,{className:"bg-primary hover:bg-primary/90",disabled:i.isPending,onClick:d,children:s===u.length-1?e.jsx(e.Fragment,{children:i.isPending?e.jsxs(e.Fragment,{children:["Completing...",e.jsx(Bt,{className:"h-4 w-4 ml-2 animate-pulse"})]}):e.jsxs(e.Fragment,{children:["Complete Episode",e.jsx(Ie,{className:"h-4 w-4 ml-2"})]})}):e.jsxs(e.Fragment,{children:["Next",e.jsx(vt,{className:"h-4 w-4 ml-2"})]})})]})]})})})})})]})},Vr=({mapData:t,isLoading:a,error:r,className:i,viewType:s,currentView:n})=>{const o=p.useRef(null),[x,u]=p.useState(null),[f,c]=p.useState(!1),{mutate:d,isPending:b}=Qa({onSelectedNodeChange:u}),{setJustJailed:h}=We(),N=V(),C=Y(),l=p.useCallback(y=>{u(y)},[]),g=t?.find(y=>y.status==="current"),A=p.useMemo(()=>t?$a(t):[],[t]),j=p.useCallback((y,E)=>{const T=t?.find(Q=>Q.id===y);if(!T||T.status!=="available")return;u(y);const L=T.isStatic;if(T.nodeType==="SHOP"){N(`/shops/${E||T.shopId||y}`);return}d({nodeId:y,isStatic:L})},[t,d,N]),k=async()=>{C.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()}),h(!1),await C.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})},P=async()=>{await C.invalidateQueries({queryKey:S.explore.getMapByLocation.key()})};if(g&&g.nodeType==="CHARACTER_ENCOUNTER"&&g.metadata?.dialogue)return e.jsx(ar,{dialogue:g.metadata.dialogue,healed:g.metadata.healed,nodeId:g.id,onClose:k});if(g&&g.nodeType==="STORY"&&g.metadata?.episodeData)return e.jsx(Jr,{episodeData:g.metadata.episodeData,onClose:P});if(g&&g.nodeType==="SCAVENGE_NODE"&&g.metadata?.choices)return e.jsx(Gr,{nodeId:g.id,location:g.location,choices:g.metadata.choices,onClose:k});if(g&&g.nodeType==="MINING_NODE"&&g.status==="current"){const y=g.metadata?.miningType||"ore",E=g.metadata?.difficulty||"easy";return e.jsx(Er,{nodeId:g.id,location:g.location,miningType:y,difficulty:E,onClose:k})}if(g&&g.nodeType==="FORAGING_NODE"&&g.status==="current"){const y=g.metadata?.foragingType||"herbs",E=g.metadata?.difficulty||"easy";return e.jsx(Nr,{nodeId:g.id,location:g.location,foragingType:y,difficulty:E,onClose:k})}return a?e.jsx(gr,{}):r?e.jsx(xr,{}):s==="map"?e.jsxs("div",{className:`relative ${i||""}`,children:[b&&e.jsx("div",{className:"absolute inset-0 bg-black/20 z-50 flex items-center justify-center rounded-lg",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg flex items-center space-x-2",children:[e.jsx(me,{className:"w-5 h-5 animate-spin text-blue-500"}),e.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"Interacting..."})]})}),e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4 lg:gap-6",children:[e.jsxs("div",{ref:o,className:"flex-1 min-w-0 relative min-h-[400px] lg:min-h-[600px]",children:[e.jsx("div",{className:"absolute inset-0 overflow-hidden rounded-lg",children:e.jsx(at,{currentView:n||"shibuya",className:"w-full h-full"})}),!1,e.jsx("div",{className:"relative z-10 w-full h-full",children:e.jsx(Ya,{showTooltips:!0,showAnimations:!0,showGridOverlay:f,nodes:t||[],connections:[],mapType:"isolated",interactive:!1,className:m(Ha({background:"default"}),"bg-transparent border-0 w-full h-full"),onNodeClick:l,onAccessNode:j})})]}),e.jsx("div",{className:"lg:w-80 lg:flex-shrink-0",children:e.jsx(Le,{})})]})]}):e.jsxs("div",{className:m("space-y-6 grid grid-cols-1 lg:grid-cols-2 gap-12",i),children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-1 h-6 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"}),e.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"District Locations"}),e.jsx(Ee,{className:"w-4 h-4 text-purple-500 animate-pulse"})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"Explore what this district has to offer"})]}),e.jsx("div",{className:"space-y-3",children:A.map((y,E)=>{const T=Xa(y.status),L=Ka(y.nodeType),Q=Za(y.nodeType),B=y.status==="available",K=y.status==="locked",D=B&&!b&&x!==y.id;return e.jsx("div",{className:"relative p-1 -m-1",style:{animationDelay:`${E*50}ms`},children:e.jsxs("button",{disabled:!B||b||x===y.id,className:m(Fa({status:y.status,interactive:D,nodeType:y.nodeType}),Ua({nodeType:y.nodeType}),D&&Q.hoverBorder,"w-full relative min-h-[80px] overflow-hidden",D&&"transform transition-all duration-300 hover:scale-[1.02]"),onClick:()=>B&&j(y.id,y.shopId||null),children:[e.jsx("div",{className:m("absolute inset-0 opacity-5 dark:opacity-10 bg-cover bg-center bg-no-repeat transition-opacity duration-300",D&&"group-hover:opacity-10 dark:group-hover:opacity-20"),style:{backgroundImage:(()=>{const F=_a(y.nodeType);if(!F)return"none";const J=F.match(/bg-\[url\('([^']+)'\)\]/);return J?`url("${J[1]}")`:"none"})()}}),B&&e.jsx("div",{className:m("absolute inset-0 opacity-0 transition-opacity duration-300 pointer-events-none",D&&"group-hover:opacity-100",Q.shadowColor,"shadow-2xl rounded-xl")}),B&&y.nodeType==="STORY"&&e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-400/20 rounded-xl animate-pulse pointer-events-none"}),e.jsxs("div",{className:"relative z-10 flex items-center gap-3 p-0",children:[e.jsx("div",{className:"relative flex-shrink-0",children:e.jsxs("div",{className:m("w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 text-white shadow-md bg-gradient-to-br relative transition-all duration-300",K&&"grayscale opacity-60",D&&"group-hover:shadow-xl",qa(y.nodeType)),children:[e.jsx("div",{className:"w-4 h-4",children:Va(y.nodeType)}),K&&e.jsx("div",{className:"absolute -top-0.5 -right-0.5 w-4 h-4 rounded-full bg-gray-900 dark:bg-gray-100 border border-gray-700 dark:border-gray-300 flex items-center justify-center",children:e.jsx(he,{className:"w-2 h-2 text-gray-400 dark:text-gray-600"})}),B&&y.nodeType==="STORY"&&e.jsx("div",{className:"absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full bg-amber-500 animate-pulse shadow-md shadow-amber-500/50"}),B&&y.nodeType!=="STORY"&&e.jsx("div",{className:"absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full bg-green-500 animate-pulse shadow-md shadow-green-500/50"})]})}),e.jsxs("div",{className:"flex-1 min-w-0 text-left",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsxs("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[e.jsx("h3",{className:"text-base font-bold text-gray-900 dark:text-white line-clamp-1",children:y.title}),y.nodeType==="STORY"&&e.jsx(Ee,{className:"w-4 h-4 text-amber-500 animate-pulse flex-shrink-0"})]}),e.jsxs("div",{className:"flex items-center gap-1 flex-shrink-0 ml-2",children:[e.jsx("span",{className:m("px-2 py-0.5 rounded text-xs font-bold",L.color,y.nodeType==="STORY"&&"ring-2 ring-amber-300 dark:ring-amber-600"),children:L.text}),!y.isStatic&&y.status!=="available"&&y.status!=="current"&&e.jsx("span",{className:m("px-1.5 py-0.5 rounded text-xs font-medium",T.color),children:T.text})]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 line-clamp-1 leading-tight",children:y.description}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 italic",children:y.nodeType==="STORY"?"Story episode - advances main quest progress":Q.description})]}),y.expiresAt&&!y.isStatic&&e.jsx("div",{className:"mt-2",children:e.jsxs("div",{className:m("flex items-center gap-1 text-xs px-2 py-1 rounded w-fit",ae(y.expiresAt)?"bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300":"bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"),children:[e.jsx(G,{className:"w-3 h-3"}),e.jsx("span",{className:"font-medium",children:He(y.expiresAt)})]})})]})]})]})},y.id)})})]}),e.jsx(Le,{})]})},qr=()=>{const t=Y();return q(S.explore.changeMapLocation.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:S.explore.getMapByLocation.key()}),t.invalidateQueries({queryKey:S.user.getCurrentUserInfo.key()})}}))},Kr=({isOpen:t,onClose:a,onSelectMethod:r,destination:i,userCash:s,isLoading:n=!1})=>{const o=[{method:"walk",name:"Walk",description:"Free travel, but takes longer",cost:0,time:i.travelTimes.walk,icon:"👟"},{method:"bus",name:"Bus",description:"Faster travel, but costs money",cost:i.cost,time:i.travelTimes.bus,icon:"🚌"}],x=u=>{r(u),a()};return e.jsx(wt,{open:t,onOpenChange:a,children:e.jsxs(jt,{className:"sm:max-w-md bg-gray-800/95 border-purple-900/30 text-white",children:[e.jsxs(Nt,{className:"space-y-3",children:[e.jsxs(kt,{className:"flex items-center gap-3 text-xl font-semibold",children:[e.jsx("div",{className:"size-8 rounded-full bg-blue-500/20 flex items-center justify-center",children:e.jsx($,{className:"size-4 text-blue-400"})}),"Travel to ",i.name]}),e.jsxs(Ct,{className:"text-gray-400",children:["Choose your preferred method of travel to ",i.name,"."]})]}),e.jsx("div",{className:"space-y-3 mt-4",children:o.map(u=>{const f=s>=u.cost,c=!f||n,d=u.method==="walk";return e.jsx("div",{className:m("bg-gray-900/50 rounded-lg p-4 border transition-all duration-200 relative",f?"border-gray-700 hover:border-purple-500 hover:bg-gray-800/70":"border-red-800/50 bg-red-900/10"),children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:m("size-12 rounded-full flex items-center justify-center text-xl flex-shrink-0",d?"bg-green-500/20 border border-green-500/30":"bg-blue-500/20 border border-blue-500/30"),children:u.icon}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h3",{className:"font-semibold text-white text-lg",children:u.name}),!f&&u.cost>0&&e.jsx("span",{className:"text-xs text-red-400 bg-red-900/30 px-2 py-1 rounded-full",children:"Cannot Afford"})]}),e.jsx("p",{className:"text-sm text-gray-400 mb-3",children:u.description}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"size-6 rounded-full bg-gray-700 flex items-center justify-center",children:e.jsx(G,{className:"size-3 text-blue-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-400",children:"Time"}),e.jsxs("p",{className:"text-sm text-white font-medium",children:[u.time," min"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"size-6 rounded-full bg-gray-700 flex items-center justify-center",children:e.jsx(Ve,{className:"size-3 text-yellow-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-400",children:"Cost"}),e.jsx("p",{className:"text-sm text-white font-medium",children:u.cost===0?"Free":U(u.cost)})]})]})]}),!f&&u.cost>0&&e.jsxs("div",{className:"mb-3 text-xs text-red-400 bg-red-900/20 p-2 rounded border border-red-800/30",children:["Insufficient funds (need ",U(u.cost-s)," more)"]}),e.jsx(pe,{disabled:c,size:"sm",className:m("w-full transition-all duration-200",!f&&"!bg-gray-700 hover:!bg-gray-700 !text-gray-400 !border-gray-600"),onClick:()=>x(u.method),children:n?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"size-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),e.jsx("span",{children:"Starting Travel..."})]}):e.jsxs("div",{className:"flex items-center gap-2 -ml-5",children:[d?e.jsx(At,{className:"size-4"}):e.jsx(Ne,{className:"size-4"}),e.jsx("span",{children:f?`${u.name}`:"Cannot Afford"})]})})]})]})},u.method)})}),e.jsx("div",{className:"mt-4 bg-gray-800/50 rounded-lg p-3 border border-purple-900/30",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"size-8 rounded-full bg-yellow-500/20 flex items-center justify-center",children:e.jsx(Dt,{className:"size-4 text-yellow-400"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-400",children:"Your Cash"}),e.jsx("p",{className:"text-sm font-medium text-white",children:U(s)})]})]})})]})})},Xr=({className:t,travelStatus:a})=>{const{data:r}=Ce(),{mutate:i,isPending:s}=qr(),{setExplorePageSetting:n}=qe(),[o,x]=p.useState(null),[u,f]=p.useState(!1),[c,d]=p.useState(null),b=r?.currentMapLocation,h=a?.isTravel||!1,N=l=>{if(!(!W.find(A=>A.id===l)||!r)){if(l===b){_.error("You are already in this location!");return}if(h){_.error("You are already traveling!");return}d(l),f(!0)}},C=l=>{const g=W.find(j=>j.id===c);if(!g||!r||!c)return;const A=l==="walk"?0:g.cost;if(r.cash<A){_.error(`You need ${U(A)} to travel by ${l}!`);return}i({location:c,method:l},{onSuccess:()=>{_.success(`Started traveling to ${g.name} by ${l}!`),n("map")},onError:j=>{_.error(j?.message||"Failed to start travel")}})};return r?e.jsxs("div",{className:m("space-y-6",t),children:[e.jsxs("div",{className:"grid lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-2 bg-gray-900/70 rounded-lg border border-purple-900/30 overflow-hidden",children:e.jsxs("div",{className:"bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-lg shadow-2xl shadow-gray-900/10 dark:shadow-gray-900/30 border border-gray-200/50 dark:border-gray-700/50 overflow-hidden",children:[e.jsxs("div",{className:"p-4 border-b border-gray-200/50 dark:border-gray-700/50 hidden lg:block",children:[e.jsxs("div",{className:"flex items-center space-x-3 absolute z-10 bg-black/10 top-3 left-2 px-2 rounded-lg",children:[e.jsx(Xe,{className:"w-5 h-5 text-purple-600 dark:text-purple-400"}),e.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"Tokyo District Map"})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Click on any district to view details and travel options"})]}),e.jsxs("div",{className:"relative h-80 lg:h-96",children:[e.jsx(at,{currentView:"tokyo"}),W.map(l=>e.jsxs("button",{disabled:h,className:m("absolute w-16 h-16 transform -translate-x-1/2 -translate-y-1/2 z-10 transition-all duration-300",o===l.id?"scale-110":"hover:scale-105",h&&"opacity-50 cursor-not-allowed"),style:{left:`${l.position.x}%`,top:`${l.position.y}%`},onClick:()=>!h&&x(l.id),children:[e.jsx("div",{className:`absolute inset-0 rounded-lg blur-md opacity-60 bg-gradient-to-br ${l.atmosphere.primaryColor}`,style:{transform:o===l.id?"scale(1.2)":"scale(1)"}}),e.jsxs("div",{className:m("relative w-16 h-16 rounded-lg flex flex-col items-center justify-center p-1",l.background,`border-2 ${l.atmosphere.accentColor}`,o===l.id&&`${l.atmosphere.glowColor} shadow-lg`),children:[e.jsx($,{className:"w-6 h-6 text-white mb-1"}),e.jsx("span",{className:"text-white text-xs font-medium text-center leading-tight",children:l.name})]}),e.jsxs("div",{className:m("absolute -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap px-2 py-1 rounded-md text-xs font-medium",o===l.id?`bg-gradient-to-r ${l.atmosphere.primaryColor} text-white shadow-lg`:"bg-gray-900/80 text-gray-300"),children:[l.name,o===l.id&&e.jsx("div",{className:"absolute inset-0 rounded-md bg-white/20 animate-pulse"})]})]},l.id))]})]})}),e.jsx("div",{className:"space-y-4",children:o?(()=>{const l=W.find(j=>j.id===o);if(!l)return null;const g=l.id===b,A=r.cash>=l.cost;return e.jsxs("div",{className:m("rounded-lg p-3 border-2",l.atmosphere.accentColor,l.background),children:[e.jsx("div",{className:"flex items-center justify-between mb-3",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:m("w-12 h-12 rounded-lg flex items-center justify-center relative overflow-hidden",l.background,`border-2 ${l.atmosphere.accentColor}`),children:[e.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${l.atmosphere.primaryColor} opacity-60 animate-pulse`}),e.jsx($,{className:"w-6 h-6 text-white relative z-10"})]}),e.jsx("div",{children:e.jsx("h3",{className:"text-white font-semibold text-lg",children:l.name})})]})}),e.jsx("p",{className:"text-gray-300 text-sm mb-3 leading-relaxed",children:l.description}),e.jsxs("div",{className:"mb-3",children:[e.jsx("h4",{className:"text-white font-medium text-sm mb-2",children:"Key Features"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:l.features.map((j,k)=>e.jsx("span",{className:"px-2 py-1 bg-gray-800/60 text-gray-300 text-xs rounded-full border border-gray-600",children:j},k))})]}),e.jsxs("div",{className:"flex items-center space-x-1 bg-yellow-50 dark:bg-yellow-900/20 mb-4 px-3 py-2 rounded-lg border border-yellow-200 dark:border-yellow-800",children:[e.jsx("p",{className:"text-sm font-semibold text-gray-900 dark:text-white mr-4",children:"Cost:"}),e.jsx(Ve,{className:"w-4 h-4 text-yellow-600 dark:text-yellow-400"}),e.jsx("span",{className:"font-semibold text-yellow-700 dark:text-yellow-300 text-sm",children:U(l.cost)})]}),g?e.jsx("div",{className:"flex items-center justify-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800",children:e.jsxs("div",{className:"flex items-center space-x-2 text-purple-700 dark:text-purple-300",children:[e.jsx($,{className:"w-4 h-4"}),e.jsx("span",{className:"font-semibold text-sm",children:"You are here!"})]})}):e.jsx("button",{disabled:h||s,className:m("w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold transition-all duration-200 text-sm",!h&&!s?"bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-purple-700 hover:to-indigo-800 text-white shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-500/30 transform hover:scale-105 active:scale-95":"bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed","disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"),onClick:()=>N(l.id),children:s?e.jsxs(e.Fragment,{children:[e.jsx(me,{className:"w-4 h-4 animate-spin"}),e.jsx("span",{children:"Starting Travel..."})]}):h?e.jsxs(e.Fragment,{children:[e.jsx(Ne,{className:"w-4 h-4"}),e.jsx("span",{children:"Currently Traveling"})]}):e.jsxs(e.Fragment,{children:[e.jsx(Ne,{className:"w-4 h-4"}),e.jsx("span",{children:"Choose Travel Method"})]})}),!A&&!g&&e.jsxs("div",{className:"mt-2 flex items-center space-x-2 text-red-600 dark:text-red-400 text-xs",children:[e.jsx(Ke,{className:"w-3 h-3"}),e.jsxs("span",{children:["You need ",U(l.cost-r.cash)," more"]})]})]})})():e.jsx("div",{className:"bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-lg lg:rounded-2xl shadow-2xl shadow-gray-900/10 dark:shadow-gray-900/30 border border-gray-200/50 dark:border-gray-700/50 overflow-hidden p-3 lg:p-6",children:e.jsxs("div",{className:"lg:text-center flex lg:flex-col items-center justify-center gap-x-2",children:[e.jsx(Qt,{className:"lg:w-12 w-8 h-auto text-gray-400 lg:mx-auto lg:mb-3"}),e.jsx("h3",{className:"lg:text-lg font-semibold text-gray-900 dark:text-white lg:mb-2",children:"Select a Tokyo District"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm hidden lg:block",children:"Click on any district marker on the map to view details and travel options."})]})})})]}),c&&e.jsx(Kr,{isOpen:u,destination:W.find(l=>l.id===c),userCash:r?.cash||0,isLoading:s,onSelectMethod:C,onClose:()=>{f(!1),d(null)}})]}):e.jsx("div",{className:"flex items-center justify-center h-96 bg-gradient-to-br from-indigo-50 via-white to-cyan-50 dark:from-gray-950 dark:via-gray-900 dark:to-indigo-950/50 rounded-lg",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(me,{className:"w-8 h-8 animate-spin text-blue-500"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-300",children:"Loading user data..."})]})})},_r=({travelStatus:t,className:a})=>{const[r,i]=p.useState(t.remainingTime||0),[s,n]=p.useState(0),[o,x]=p.useState(!1),[u,f]=p.useState(0),c=p.useRef(0);p.useEffect(()=>{if(!t.isTravel||!t.travelStartTime||!t.travelEndTime)return;const C=()=>{const g=new Date().getTime(),A=new Date(t.travelStartTime).getTime(),j=new Date(t.travelEndTime).getTime();if(isNaN(A)||isNaN(j)||A>=j){console.error("Invalid travel dates detected");return}const k=j-A,P=g-A,y=Math.max(0,j-g);i(y);const E=Math.min(100,P/k*100);if(Math.abs(E-c.current)>=5&&f(T=>T+1),c.current=E,n(E),y<=0&&(n(100),!o)){const T=W.find(L=>L.id===t.travelingTo);_.success(`Successfully arrived at ${T?.name||"your destination"}!`),x(!0)}};C();const l=setInterval(C,1e3);return()=>clearInterval(l)},[t,o]);const d=C=>{const l=Math.ceil(C/1e3),g=Math.floor(l/60),A=l%60;return`${g}:${A.toString().padStart(2,"0")}`},b=r<=0,h=W.find(C=>C.id===t.travelingTo),N=t.travelMethod==="walk";return e.jsx("div",{className:m("h-full overflow-y-auto p-4 max-w-4xl mx-auto",a),children:e.jsxs(M.div,{className:"space-y-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:[e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-6 border border-purple-900/30",children:[e.jsx(It,{mode:"wait",children:b?e.jsxs(M.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"text-center mb-6",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-2",children:[e.jsx("div",{className:"size-10 rounded-full bg-green-500/20 flex items-center justify-center",children:e.jsx(St,{className:"size-6 text-green-400"})}),e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Journey Complete!"})]}),e.jsxs("p",{className:"text-gray-400",children:["Welcome to ",h?.name||"your destination"]})]},"complete"):e.jsx(M.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"text-center mb-6",children:e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-2 -ml-5",children:[e.jsx("div",{className:"size-10 rounded-full bg-purple-500/20 flex items-center justify-center",children:e.jsx(Zt,{className:"size-6 text-purple-400 animate-pulse"})}),e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Traveling"})]})},"traveling")}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-white font-semibold mb-3 flex items-center gap-2",children:[e.jsx(Tt,{className:"size-4 text-purple-400"}),"Travel Method"]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(M.div,{className:m("size-16 rounded-full flex items-center justify-center text-2xl relative",N?"bg-green-500/20 border border-green-500/30":"bg-blue-500/20 border border-blue-500/30"),animate:b?{}:{scale:[1,1.05,1]},transition:{duration:2,repeat:b?0:1/0,ease:"easeInOut"},children:[e.jsx("div",{className:m("size-8"),children:t.travelMethod==="walk"?"👟":"🚌"}),!b&&e.jsx("div",{className:"absolute inset-0 overflow-hidden rounded-full",children:[...Array(3)].map((C,l)=>e.jsx(M.div,{className:m("absolute w-1 h-6 rounded-full opacity-30",N?"bg-green-400":"bg-blue-400"),style:{right:`${8+l*6}px`,top:`${16+l*3}px`},animate:{x:[-15,15],opacity:[0,.6,0]},transition:{duration:1.5,repeat:1/0,delay:l*.2,ease:"easeInOut"}},l))})]}),e.jsx("div",{children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{className:"text-base text-gray-200 capitalize",children:t.travelMethod})})})]})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-white font-semibold mb-3 flex items-center gap-2",children:[e.jsx($,{className:"size-4 text-blue-400"}),"Destination"]}),e.jsxs("div",{className:"bg-gray-900/50 rounded-lg p-3 border border-gray-700",children:[e.jsx("h4",{className:"text-lg font-semibold text-white",children:h?.name||"Unknown Destination"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Tokyo Ward"})]})]})]})]}),e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-purple-900/30",children:[e.jsxs("h3",{className:"text-white font-semibold mb-4 flex items-center gap-2",children:[e.jsx(Rt,{className:"size-4 text-purple-400"}),"Journey Progress"]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400",children:"Progress"}),e.jsxs(M.span,{className:"text-lg font-bold text-white",animate:{scale:[1,1.1,1]},transition:{duration:.3},children:[Math.round(s),"%"]},u)]}),e.jsx("div",{className:"w-full h-3 bg-gray-700 rounded-full overflow-hidden",children:e.jsx(M.div,{initial:{width:0},animate:{width:`${Math.min(s,100)}%`},transition:{duration:.8,ease:"easeOut"},className:m("h-full rounded-full transition-all duration-300",N?"bg-green-500":"bg-purple-500"),children:e.jsx(M.div,{className:"h-full bg-gradient-to-r from-transparent via-white/30 to-transparent",animate:{x:["-100%","100%"]},transition:{duration:2,repeat:b?0:1/0,ease:"easeInOut"}})})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-gray-900/50 rounded-lg p-3 border border-gray-700",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"size-6 rounded-full bg-gray-700 flex items-center justify-center",children:e.jsx(G,{className:"size-3 text-blue-400"})}),e.jsx("span",{className:"text-sm text-gray-400",children:b?"Arrival Time":"Time Remaining"})]}),e.jsx(M.div,{className:"text-xl font-bold text-white",animate:b?{}:{color:["#ffffff","#a855f7","#ffffff"]},transition:{duration:3,repeat:b?0:1/0,ease:"easeInOut"},children:b?"Arrived!":d(r)}),e.jsx("p",{className:"text-xs text-gray-400",children:b?"Ready to explore!":"Until arrival"})]}),e.jsxs("div",{className:"bg-gray-900/50 rounded-lg p-3 border border-gray-700",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"size-6 rounded-full bg-gray-700 flex items-center justify-center",children:e.jsx(Je,{className:"size-3 text-yellow-400"})}),e.jsx("span",{className:"text-sm text-gray-400",children:"Travel Speed"})]}),e.jsx("div",{className:m("text-xl font-bold",N?"text-green-400":"text-blue-400"),children:t.travelMethod==="walk"?"Steady":"Fast"}),e.jsx("p",{className:"text-xs text-gray-400",children:t.travelMethod==="walk"?"Walking pace":"Bus transport"})]})]})]})]})})},Zr=(t={})=>Et(S.explore.getMapByLocation.queryOptions({staleTime:6e4,...t}));function ds(){const{data:t}=Ce(),{explorePageSetting:a,setExplorePageSetting:r}=qe(),{data:i,isLoading:s,error:n}=Zr(),o=se("highstreet2"),x=Ot(t?.currentMapLocation||"Unknown"),u=i?.travelStatus,f=i?.nodes||[],c=u?.isTravel||!1,d=p.useMemo(()=>{if(!f.length)return!1;const g=f.find(A=>A.status==="current");return g?["CHARACTER_ENCOUNTER","STORY","SCAVENGE_NODE","MINING_NODE","FORAGING_NODE"].includes(g.nodeType):!1},[i]),b=()=>a==="travel"?e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 blur-3xl opacity-60",children:e.jsx("h1",{className:"text-3xl sm:text-5xl lg:text-6xl font-black text-purple-400 tracking-tight",children:"Explore Tokyo"})}),e.jsx("div",{className:"absolute inset-0 blur-xl opacity-40",children:e.jsx("h1",{className:"text-3xl sm:text-5xl lg:text-6xl font-black text-pink-300 tracking-tight",children:"Explore Tokyo"})}),e.jsx("h1",{className:"relative text-3xl sm:text-5xl lg:text-6xl font-black text-white tracking-tight",style:{textShadow:`
                                0 0 20px rgba(168, 85, 247, 0.8),
                                0 0 40px rgba(236, 72, 153, 0.6),
                                0 0 60px rgba(34, 211, 238, 0.4),
                                2px 2px 4px rgba(0, 0, 0, 0.8)
                            `},children:"Explore Tokyo"})]}):x,h=()=>a==="travel"?"Choose your next destination":"Discover what this district has to offer",N=()=>a==="travel"?e.jsxs("div",{className:"flex items-center space-x-2 sm:space-x-3 mt-3",children:[e.jsx($,{className:"w-4 h-4 sm:w-5 sm:h-5 text-purple-300 animate-bounce"}),e.jsxs("span",{className:"text-sm sm:text-base text-purple-200 font-semibold",children:[e.jsx("span",{className:"inline",children:"Currently in: "}),e.jsx("span",{className:"text-white font-bold bg-purple-600/20 px-2 sm:px-3 py-1 rounded-full backdrop-blur-sm border border-purple-400/30",children:x})]})]}):null,C=()=>e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur-lg opacity-60 animate-pulse"}),e.jsx("div",{className:"relative flex rounded-2xl bg-gray-900/80 backdrop-blur-md shadow-2xl border border-white/20 hover:border-white/40 transition-all duration-300",children:a==="travel"?e.jsxs("button",{className:"flex items-center space-x-2 sm:space-x-3 px-4 sm:px-6 py-3 sm:py-4 text-sm font-bold text-white hover:bg-white/10 transition-all duration-300 rounded-2xl group",onClick:()=>r("map"),children:[e.jsx(ue,{className:"w-4 h-4 sm:w-5 sm:h-5 group-hover:-translate-x-1 transition-transform"}),e.jsx("span",{className:"hidden xs:inline sm:inline",children:"Back"})]}):e.jsxs("button",{disabled:c||d,className:m("flex items-center space-x-2 sm:space-x-3 px-4 sm:px-6 py-3 sm:py-4 text-sm font-bold transition-all duration-300 rounded-2xl group",c||d?"text-gray-400 cursor-not-allowed opacity-60":"text-white hover:bg-white/10"),onClick:()=>!(c||d)&&r("travel"),children:[e.jsx(Kt,{className:m("w-4 h-4 sm:w-5 sm:h-5 transition-transform",!c&&"group-hover:rotate-12")}),e.jsx("span",{className:"inline",children:c?"Traveling...":"Travel"})]})})]}),l=()=>{if(a==="travel")return{primary:"rgba(168, 85, 247, 0.8)",secondary:"rgba(236, 72, 153, 0.6)",tertiary:"rgba(34, 211, 238, 0.4)",textShadow:`
                    0 0 20px rgba(168, 85, 247, 0.8),
                    0 0 40px rgba(236, 72, 153, 0.6),
                    0 0 60px rgba(34, 211, 238, 0.4),
                    2px 2px 4px rgba(0, 0, 0, 0.8)
                `}};return e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900 -m-2 md:-m-4",children:[e.jsx(Mt,{backgroundImage:o||"",title:b(),subtitle:h(),additionalInfo:N(),actionButton:C(),titleTheme:l(),useNegativeZIndex:!1}),e.jsxs("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 -mt-10",children:[a!=="travel"&&e.jsxs("div",{className:"flex items-center bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl w-fit mb-6 shadow-xl border border-gray-200/50 dark:border-gray-700/50",children:[e.jsxs("button",{disabled:c||d,className:m("px-5 py-2.5 rounded-xl transition-all duration-300 flex items-center gap-3 text-sm font-semibold",(c||d)&&"opacity-50 cursor-not-allowed",a==="map"?"bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg shadow-purple-500/25 scale-105":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700/50"),onClick:()=>!(c||d)&&r("map"),children:[e.jsx(Xe,{className:"w-4 h-4"}),e.jsx("span",{children:"Map View"})]}),e.jsxs("button",{disabled:c||d,className:m("px-5 py-2.5 rounded-xl transition-all duration-300 flex items-center gap-3 text-sm font-semibold",(c||d)&&"opacity-50 cursor-not-allowed",a==="list"?"bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg shadow-purple-500/25 scale-105":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700/50"),onClick:()=>!(c||d)&&r("list"),children:[e.jsx(Wt,{className:"w-4 h-4"}),e.jsx("span",{children:"List View"})]})]}),c&&u?e.jsx("div",{className:"flex items-center justify-center min-h-[400px] lg:min-h-[600px] w-full",children:e.jsx(_r,{travelStatus:u})}):e.jsx(e.Fragment,{children:a==="travel"?e.jsx(Xr,{className:"w-full",travelStatus:u}):e.jsx(Vr,{mapData:f,isLoading:s,error:n,viewType:a,className:"w-full",currentView:t?.currentMapLocation})})]})]})}export{ds as default};
