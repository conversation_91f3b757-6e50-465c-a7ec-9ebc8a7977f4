import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBeginBattle = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.battle.begin.mutationOptions({
            onSuccess: () => {
                // Fetch latest battle state and refresh user info
                queryClient.invalidateQueries({ queryKey: api.battle.getStatus.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
            },
        })
    );
};

export default useBeginBattle;
