import { describe, expect, it } from "vitest";
import nFormatter from "../nFormatter";

describe("nFormatter", () => {
    describe("basic number formatting", () => {
        it("should format numbers less than 1000 without suffix", () => {
            expect(nFormatter(0, 1)).toBe("0");
            expect(nFormatter(1, 1)).toBe("1");
            expect(nFormatter(999, 1)).toBe("999");
            expect(nFormatter(500, 2)).toBe("500");
        });

        it("should format thousands with 'k' suffix", () => {
            expect(nFormatter(1000, 1)).toBe("1k");
            expect(nFormatter(1500, 1)).toBe("1.5k");
            expect(nFormatter(12000, 1)).toBe("12k");
            expect(nFormatter(999999, 1)).toBe("1000k");
        });

        it("should format millions with 'M' suffix", () => {
            expect(nFormatter(1000000, 1)).toBe("1M");
            expect(nFormatter(1500000, 1)).toBe("1.5M");
            expect(nFormatter(12000000, 1)).toBe("12M");
            expect(nFormatter(999999999, 1)).toBe("1000M");
        });

        it("should format billions with 'G' suffix", () => {
            expect(nFormatter(1000000000, 1)).toBe("1G");
            expect(nFormatter(1500000000, 1)).toBe("1.5G");
            expect(nFormatter(12000000000, 1)).toBe("12G");
        });

        it("should format trillions with 'T' suffix", () => {
            expect(nFormatter(1000000000000, 1)).toBe("1T");
            expect(nFormatter(1500000000000, 1)).toBe("1.5T");
        });

        it("should format petabytes with 'P' suffix", () => {
            expect(nFormatter(1000000000000000, 1)).toBe("1P");
            expect(nFormatter(1500000000000000, 1)).toBe("1.5P");
        });

        it("should format exabytes with 'E' suffix", () => {
            expect(nFormatter(1000000000000000000, 1)).toBe("1E");
            expect(nFormatter(1500000000000000000, 1)).toBe("1.5E");
        });
    });

    describe("decimal precision", () => {
        it("should respect the digits parameter", () => {
            expect(nFormatter(1234, 0)).toBe("1k");
            expect(nFormatter(1234, 1)).toBe("1.2k");
            expect(nFormatter(1234, 2)).toBe("1.23k");
            expect(nFormatter(1234, 3)).toBe("1.234k");
        });

        it("should remove trailing zeros", () => {
            expect(nFormatter(1000, 2)).toBe("1k");
            expect(nFormatter(1100, 2)).toBe("1.1k");
            expect(nFormatter(1010, 2)).toBe("1.01k");
        });

        it("should handle edge cases with precision", () => {
            expect(nFormatter(1999, 1)).toBe("2k");
            expect(nFormatter(1999, 0)).toBe("2k");
            expect(nFormatter(1001, 2)).toBe("1k");
        });
    });

    describe("negative numbers", () => {
        it("should handle negative numbers by converting to 0", () => {
            // The implementation uses +num.toFixed(0) which makes negative numbers become 0
            expect(nFormatter(-1000, 1)).toBe("0");
            expect(nFormatter(-1500, 1)).toBe("0");
            expect(nFormatter(-1000000, 1)).toBe("0");
        });

        it("should handle negative numbers with precision", () => {
            expect(nFormatter(-1234, 2)).toBe("0");
            expect(nFormatter(-999, 1)).toBe("0");
        });
    });

    describe("decimal input numbers", () => {
        it("should handle decimal inputs by rounding", () => {
            expect(nFormatter(1000.7, 1)).toBe("1k");
            expect(nFormatter(1500.9, 1)).toBe("1.5k");
            expect(nFormatter(999.9, 1)).toBe("1k"); // 999.9 rounds to 1000, which becomes 1k
        });

        it("should round decimal inputs before processing", () => {
            expect(nFormatter(1234.6, 1)).toBe("1.2k");
            expect(nFormatter(1999.4, 1)).toBe("2k");
        });
    });

    describe("edge cases", () => {
        it("should handle very large numbers", () => {
            const veryLarge = 1e20;
            const result = nFormatter(veryLarge, 1);
            expect(result).toBe("100E");
        });

        it("should handle zero with different precision", () => {
            expect(nFormatter(0, 0)).toBe("0");
            expect(nFormatter(0, 2)).toBe("0");
            expect(nFormatter(0, 5)).toBe("0");
        });

        it("should handle numbers just below thresholds", () => {
            expect(nFormatter(999, 1)).toBe("999");
            expect(nFormatter(999999, 1)).toBe("1000k");
            expect(nFormatter(999999999, 1)).toBe("1000M");
        });

        it("should handle numbers just above thresholds", () => {
            expect(nFormatter(1001, 1)).toBe("1k");
            expect(nFormatter(1000001, 1)).toBe("1M");
            expect(nFormatter(1000000001, 1)).toBe("1G");
        });
    });
});
