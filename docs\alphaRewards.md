// 15+ - Keep your Alpha Tester Discord role and retain exclusive access to the Alpha Tester Discord channel
[
{ "id": 11, "username": "🅱️IG FLOPPA 🦁", "level": 27, "email": "<EMAIL>" },
{ "id": 12, "username": "<PERSON>", "level": 40, "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@hotmail.com" },
{ "id": 15, "username": "<PERSON><PERSON><PERSON>", "level": 40, "email": "<EMAIL>" },
{ "id": 16, "username": "Punching Bag", "level": 15, "email": "<EMAIL>" },
{ "id": 17, "username": "kirth", "level": 27, "email": "<EMAIL>" },
{ "id": 19, "username": "<PERSON>", "level": 35, "email": "<EMAIL>" },
{ "id": 38, "username": "<PERSON><PERSON>", "level": 15, "email": "christiang<PERSON><PERSON><PERSON>@gmail.com" },
{ "id": 39, "username": "<PERSON><PERSON><PERSON>", "level": 40, "email": "<EMAIL>" },
{ "id": 42, "username": "<PERSON> Abnomar", "level": 17, "email": "<EMAIL>" },
{ "id": 48, "username": "Pandora", "level": 40, "email": "<EMAIL>" },
{ "id": 49, "username": "Elite", "level": 40, "email": "<EMAIL>" },
{ "id": 51, "username": "Roman", "level": 35, "email": "<EMAIL>" },
{ "id": 56, "username": "Eclipse", "level": 40, "email": "<EMAIL>" },
{ "id": 57, "username": "Chrono", "level": 40, "email": "<EMAIL>" },
{ "id": 60, "username": "Toiste", "level": 40, "email": "<EMAIL>" },
{ "id": 62, "username": "David", "level": 31, "email": "<EMAIL>" },
{ "id": 69, "username": "Kamon", "level": 28, "email": "<EMAIL>" },
{ "id": 77, "username": "Nick", "level": 35, "email": "<EMAIL>" },
{ "id": 87, "username": "Walter Malone", "level": 37, "email": "<EMAIL>" },
{ "id": 88, "username": "Broda", "level": 15, "email": "<EMAIL>" },
{ "id": 89, "username": "Yukisa Nezu", "level": 35, "email": "<EMAIL>" },
{ "id": 91, "username": "Manus", "level": 20, "email": "<EMAIL>" },
{ "id": 92, "username": "zellking", "level": 15, "email": "<EMAIL>" },
{ "id": 103, "username": "Bibul", "level": 15, "email": "<EMAIL>" },
{ "id": 105, "username": "Jbwebb", "level": 22, "email": "<EMAIL>" },
{ "id": 143, "username": "Crazy Fi", "level": 19, "email": "<EMAIL>" },
{ "id": 156, "username": "DarkTea", "level": 35, "email": "<EMAIL>" },
{ "id": 169, "username": "Edwin", "level": 24, "email": "<EMAIL>" },
{ "id": 170, "username": "Kirito", "level": 23, "email": "<EMAIL>" },
{ "id": 181, "username": "\t n1ghtm4re5", "level": 20, "email": "<EMAIL>" }
]

// 20+ - Guaranteed Beta access
[
{ "id": 11, "username": "🅱️IG FLOPPA 🦁", "level": 27, "email": "<EMAIL>" },
{ "id": 12, "username": "Angel", "level": 40, "email": "<EMAIL>" },
{ "id": 15, "username": "Sereneace", "level": 40, "email": "<EMAIL>" },
{ "id": 17, "username": "kirth", "level": 27, "email": "<EMAIL>" },
{ "id": 19, "username": "Benny", "level": 35, "email": "<EMAIL>" },
{ "id": 39, "username": "Contatore", "level": 40, "email": "<EMAIL>" },
{ "id": 48, "username": "Pandora", "level": 40, "email": "<EMAIL>" },
{ "id": 49, "username": "Elite", "level": 40, "email": "<EMAIL>" },
{ "id": 51, "username": "Roman", "level": 35, "email": "<EMAIL>" },
{ "id": 56, "username": "Eclipse", "level": 40, "email": "<EMAIL>" },
{ "id": 57, "username": "Chrono", "level": 40, "email": "<EMAIL>" },
{ "id": 60, "username": "Toiste", "level": 40, "email": "<EMAIL>" },
{ "id": 62, "username": "David", "level": 31, "email": "<EMAIL>" },
{ "id": 69, "username": "Kamon", "level": 28, "email": "<EMAIL>" },
{ "id": 77, "username": "Nick", "level": 35, "email": "<EMAIL>" },
{ "id": 87, "username": "Walter Malone", "level": 37, "email": "<EMAIL>" },
{ "id": 89, "username": "Yukisa Nezu", "level": 35, "email": "<EMAIL>" },
{ "id": 91, "username": "Manus", "level": 20, "email": "<EMAIL>" },
{ "id": 105, "username": "Jbwebb", "level": 22, "email": "<EMAIL>" },
{ "id": 156, "username": "DarkTea", "level": 35, "email": "<EMAIL>" },
{ "id": 169, "username": "Edwin", "level": 24, "email": "<EMAIL>" },
{ "id": 170, "username": "Kirito", "level": 23, "email": "<EMAIL>" },
{ "id": 181, "username": "\t n1ghtm4re5", "level": 20, "email": "<EMAIL>" }
]

// 25+ - An exclusive Alpha Tester Profile badge + Achievement (once implemented)
[
{ "id": 11, "username": "🅱️IG FLOPPA 🦁", "level": 27, "email": "<EMAIL>" },
{ "id": 12, "username": "Angel", "level": 40, "email": "<EMAIL>" },
{ "id": 15, "username": "Sereneace", "level": 40, "email": "<EMAIL>" },
{ "id": 17, "username": "kirth", "level": 27, "email": "<EMAIL>" },
{ "id": 19, "username": "Benny", "level": 35, "email": "<EMAIL>" },
{ "id": 39, "username": "Contatore", "level": 40, "email": "<EMAIL>" },
{ "id": 48, "username": "Pandora", "level": 40, "email": "<EMAIL>" },
{ "id": 49, "username": "Elite", "level": 40, "email": "<EMAIL>" },
{ "id": 51, "username": "Roman", "level": 35, "email": "<EMAIL>" },
{ "id": 56, "username": "Eclipse", "level": 40, "email": "<EMAIL>" },
{ "id": 57, "username": "Chrono", "level": 40, "email": "<EMAIL>" },
{ "id": 60, "username": "Toiste", "level": 40, "email": "<EMAIL>" },
{ "id": 62, "username": "David", "level": 31, "email": "<EMAIL>" },
{ "id": 69, "username": "Kamon", "level": 28, "email": "<EMAIL>" },
{ "id": 77, "username": "Nick", "level": 35, "email": "<EMAIL>" },
{ "id": 87, "username": "Walter Malone", "level": 37, "email": "<EMAIL>" },
{ "id": 89, "username": "Yukisa Nezu", "level": 35, "email": "<EMAIL>" },
{ "id": 156, "username": "DarkTea", "level": 35, "email": "<EMAIL>" },
]

// 30+ - Able to reserve your desired username for the Beta and Release
// 30+ - 1 day early access to the Beta test
[
{ "id": 12, "username": "Angel", "level": 40, "email": "<EMAIL>" },
{ "id": 15, "username": "Sereneace", "level": 40, "email": "<EMAIL>" },
{ "id": 17, "username": "kirth", "level": 27, "email": "<EMAIL>" },
{ "id": 19, "username": "Benny", "level": 35, "email": "<EMAIL>" },
{ "id": 39, "username": "Contatore", "level": 40, "email": "<EMAIL>" },
{ "id": 48, "username": "Pandora", "level": 40, "email": "<EMAIL>" },
{ "id": 49, "username": "Elite", "level": 40, "email": "<EMAIL>" },
{ "id": 51, "username": "Roman", "level": 35, "email": "<EMAIL>" },
{ "id": 56, "username": "Eclipse", "level": 40, "email": "<EMAIL>" },
{ "id": 57, "username": "Chrono", "level": 40, "email": "<EMAIL>" },
{ "id": 60, "username": "Toiste", "level": 40, "email": "<EMAIL>" },
{ "id": 62, "username": "David", "level": 31, "email": "<EMAIL>" },
{ "id": 77, "username": "Nick", "level": 35, "email": "<EMAIL>" },
{ "id": 87, "username": "Walter Malone", "level": 37, "email": "<EMAIL>" },
{ "id": 89, "username": "Yukisa Nezu", "level": 35, "email": "<EMAIL>" },
{ "id": 156, "username": "DarkTea", "level": 35, "email": "<EMAIL>" },
]

// 1st Place leaderboard (Gear Changer Item)
// Allows you to set a custom image for one of your gear pieces
Sereneace x6
Angel x5
David x1
Toiste x1
Roman x2
Contatore x2
Walter Malone x1
Pandora x1
Nick x1

// 2nd Place leaderboard (Gear Renamer Item)
// Allows you to set a custom name for one of your gear pieces
Angel x2
Eclipse x1
Chrono x3
Sereneace x4
Pandora x1
Elite x1
Contatore x1
David x1
