import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { sceneManager, getTimeOfDay } from "../sceneManager";

// Get the actual CDN URL from environment for testing
const actualCDN = import.meta.env.VITE_IMAGE_CDN_URL;

describe("sceneManager", () => {
    beforeEach(() => {
        // Reset to a default time before each test
        vi.setSystemTime(new Date("2024-01-15T12:00:00Z"));
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    const mockDate = (hour: number) => {
        const testDate = new Date("2024-01-15T12:00:00Z");
        testDate.setHours(hour, 0, 0, 0);
        vi.setSystemTime(testDate);
    };

    describe("getTimeOfDay function", () => {
        it("should return 'sunrise' for hours 6-9", () => {
            const date6 = new Date();
            date6.setHours(6, 0, 0, 0);
            expect(getTimeOfDay(date6)).toBe("sunrise");

            const date9 = new Date();
            date9.setHours(9, 0, 0, 0);
            expect(getTimeOfDay(date9)).toBe("sunrise");
        });

        it("should return 'day' for hours 10-17", () => {
            const date10 = new Date();
            date10.setHours(10, 0, 0, 0);
            expect(getTimeOfDay(date10)).toBe("day");

            const date17 = new Date();
            date17.setHours(17, 0, 0, 0);
            expect(getTimeOfDay(date17)).toBe("day");
        });

        it("should return 'sunset' for hours 18-21", () => {
            const date18 = new Date();
            date18.setHours(18, 0, 0, 0);
            expect(getTimeOfDay(date18)).toBe("sunset");

            const date21 = new Date();
            date21.setHours(21, 0, 0, 0);
            expect(getTimeOfDay(date21)).toBe("sunset");
        });

        it("should return 'night' for hours 22-5", () => {
            const date22 = new Date();
            date22.setHours(22, 0, 0, 0);
            expect(getTimeOfDay(date22)).toBe("night");

            const date5 = new Date();
            date5.setHours(5, 0, 0, 0);
            expect(getTimeOfDay(date5)).toBe("night");

            const date0 = new Date();
            date0.setHours(0, 0, 0, 0);
            expect(getTimeOfDay(date0)).toBe("night");
        });

        it("should default to 'day' for any unexpected case", () => {
            // Mock a scenario where none of the conditions match by returning an hour that breaks the logic
            const mockInvalidDate = {
                getHours: () => -1, // This will still match night condition (hour < 6)
            } as Date;
            // Actually -1 should match night, so let's test that
            expect(getTimeOfDay(mockInvalidDate)).toBe("night");
        });
    });

    describe("time of day detection", () => {
        it("should detect sunrise (6-9 hours)", () => {
            mockDate(7);
            const result = sceneManager("smallclassroom1"); // Use a scene with time variants
            expect(result).toContain("Sunrise.webp");
        });

        it("should detect day (10-17 hours)", () => {
            mockDate(14);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Day.webp");
        });

        it("should detect sunset (18-21 hours)", () => {
            mockDate(19);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Sunset.webp");
        });

        it("should detect night (22-5 hours)", () => {
            mockDate(23);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Night.webp");
        });

        it("should detect night for early morning hours", () => {
            mockDate(3);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Night.webp");
        });
    });

    describe("edge cases for time boundaries", () => {
        it("should detect sunrise at hour 6", () => {
            mockDate(6);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Sunrise.webp");
        });

        it("should detect sunrise at hour 9", () => {
            mockDate(9);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Sunrise.webp");
        });

        it("should detect day at hour 10", () => {
            mockDate(10);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Day.webp");
        });

        it("should detect day at hour 17", () => {
            mockDate(17);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Day.webp");
        });

        it("should detect sunset at hour 18", () => {
            mockDate(18);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Sunset.webp");
        });

        it("should detect sunset at hour 21", () => {
            mockDate(21);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Sunset.webp");
        });

        it("should detect night at hour 22", () => {
            mockDate(22);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Night.webp");
        });

        it("should detect night at hour 5", () => {
            mockDate(5);
            const result = sceneManager("smallclassroom1");
            expect(result).toContain("Night.webp");
        });
    });

    describe("scene URL generation", () => {
        beforeEach(() => {
            mockDate(14); // Set to day time
        });

        it("should generate correct URL for schoolField1", () => {
            const result = sceneManager("schoolField1");
            expect(result).toBe(`${actualCDN}/static/backgrounds/exterior/school/schoolfield1Day.webp`);
        });

        it("should generate correct URL for mall1", () => {
            const result = sceneManager("mall1");
            expect(result).toBe(`${actualCDN}/static/backgrounds/interior/shop/mall1Day.webp`);
        });

        it("should generate correct URL for shrine", () => {
            const result = sceneManager("shrine");
            expect(result).toBe(`${actualCDN}/static/backgrounds/exterior/town/shrineDay.webp`);
        });

        it("should generate correct URL for livingroom1Alternative", () => {
            const result = sceneManager("livingroom1Alternative");
            expect(result).toBe(`${actualCDN}/static/backgrounds/interior/house/livingroom1Alternative.webp`);
        });
    });

    describe("special scene configurations", () => {
        it("should handle scenes with same image for all times (river)", () => {
            mockDate(7); // sunrise
            let result = sceneManager("river");
            expect(result).toContain("Day.webp");

            mockDate(14); // day
            result = sceneManager("river");
            expect(result).toContain("Day.webp");

            mockDate(19); // sunset
            result = sceneManager("river");
            expect(result).toContain("Day.webp");

            mockDate(23); // night
            result = sceneManager("river");
            expect(result).toContain("Day.webp");
        });

        it("should handle scenes with alternative configurations", () => {
            mockDate(14); // day
            const result = sceneManager("livingroom1Alternative");
            expect(result).toContain("Alternative.webp");
        });

        it("should handle housingExt1 with Day for sunrise", () => {
            mockDate(7); // sunrise
            const result = sceneManager("housingExt1");
            expect(result).toContain("Day.webp");
        });
    });

    describe("error handling", () => {
        it("should use fallback scene for empty scene name", () => {
            const result = sceneManager("");
            // Should fallback to schoolField1 (default fallback scene)
            expect(result).toContain("schoolfield1Day.webp");
        });

        it("should use fallback scene for undefined scene name", () => {
            const result = sceneManager(undefined as any);
            // Should fallback to schoolField1 (default fallback scene)
            expect(result).toContain("schoolfield1Day.webp");
        });

        it("should use fallback scene for null scene name", () => {
            const result = sceneManager(null as any);
            // Should fallback to schoolField1 (default fallback scene)
            expect(result).toContain("schoolfield1Day.webp");
        });

        it("should use fallback scene for non-existent scene names", () => {
            mockDate(14);
            const result = sceneManager("nonExistentScene");
            // Should fallback to schoolField1 (default fallback scene)
            expect(result).toContain("schoolfield1Day.webp");
        });

        it("should return null when fallback is disabled", () => {
            const result = sceneManager("nonExistentScene", { fallbackScene: undefined });
            expect(result).toBeNull();
        });
    });

    describe("URL structure validation", () => {
        beforeEach(() => {
            mockDate(14); // day time
        });

        it("should always include the CDN URL", () => {
            const result = sceneManager("schoolField1");
            expect(result?.startsWith(`${actualCDN}/static/backgrounds`)).toBe(true);
        });

        it("should always end with .webp extension", () => {
            const result = sceneManager("schoolField1");
            expect(result?.endsWith(".webp")).toBe(true);
        });

        it("should include the correct path structure", () => {
            const result = sceneManager("schoolField1");
            expect(result).toMatch(/\/exterior\/school\/schoolfield1Day\.webp$/);
        });
    });

    describe("all scenes basic functionality", () => {
        beforeEach(() => {
            mockDate(14); // day time
        });

        const sceneNames = [
            "schoolField1",
            "smallclassroom1",
            "housingExt1",
            "housingExt2",
            "mall1",
            "street1",
            "market",
            "river",
            "shrine",
            "livingroom1",
            "livingroom1Alternative",
            "bathroom1",
            "citystreet1",
            "highstreet1",
            "highstreet2",
            "hallway1",
            "hallway2",
            "sportsroom1",
            "shoppingstreet2",
            "schoolroof1",
        ];

        sceneNames.forEach((sceneName) => {
            it(`should generate valid URL for ${sceneName}`, () => {
                const result = sceneManager(sceneName);
                expect(result).toBeTruthy();
                expect(result?.startsWith(`${actualCDN}/static/backgrounds`)).toBe(true);
                expect(result?.endsWith(".webp")).toBe(true);
            });
        });
    });
});
