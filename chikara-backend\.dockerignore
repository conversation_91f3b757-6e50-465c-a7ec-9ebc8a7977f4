# Git
.git
.gitignore
.github

# Dependencies
node_modules
npm-debug.log
yarn-error.log

# Build artifacts
dist
coverage
.early.coverage
.nyc_output

# IDE files
.idea
.vscode
*.swp
*.swo
.DS_Store
.cursor
.repomix

# Logs
logs
*.log

# Miscellaneous
*.md
!README.md
Dockerfile*
docker-compose*
.dockerignore
.editorconfig

# Large files that aren't needed for production
repomix-output.txt
fullcode.txt

# Test files
**/*.spec.ts
**/*.test.ts
test
tests 