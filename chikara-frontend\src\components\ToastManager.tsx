import React, { useEffect } from "react";
import { api } from "@/helpers/api";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { useQueryClient } from "@tanstack/react-query";
import { ToastBar, Toaster, toast } from "react-hot-toast";
import { useLocation, useNavigate } from "react-router-dom";
import { useNormalStore, useSessionStore, useSocketStore } from "../app/store/stores";
import { ToastHandler } from "./ToastHandler";

// Define types for socket notification message
interface NotificationMessage {
    type: string;
    details: string | Record<string, unknown>;
}

const ToastManager = () => {
    const { socket } = useSocketStore();
    const { setJustJailed } = useNormalStore();
    const { setLevelupValue } = useSessionStore();
    const queryClient = useQueryClient();
    const location = useLocation();
    const navigate = useNavigate();
    const isMobile = useCheckMobileScreen();

    useEffect(() => {
        if (socket) {
            socket.on("notification", (msg: NotificationMessage) => {
                if (msg.type === "jail") {
                    setJustJailed(true);
                }
                if (msg.type === "message") {
                    if (location.pathname.includes("/inbox")) {
                        queryClient.invalidateQueries({
                            queryKey: api.messaging.getChatHistory.key(),
                        });
                        return;
                    }
                }

                queryClient.invalidateQueries({
                    queryKey: api.notifications.getList.key(),
                });
                const details = msg.details;
                ToastHandler(msg.type, details, navigate, queryClient, setLevelupValue);
            });
            return () => {
                socket.off("notification");
            };
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [socket]);

    let containerStyle: React.CSSProperties = {};
    if (isMobile || !socket) {
        containerStyle = {
            top: 65,
        };
    } else {
        containerStyle = {
            top: 90,
            right: "26rem",
        };
    }

    let toastStyle: React.CSSProperties = {};
    if (isMobile) {
        toastStyle = {
            background: "#363636",
            color: "#fff",
            padding: "5px 15px",
            fontSize: "0.9rem",
            border: "2px solid grey",
        };
    } else {
        toastStyle = {
            background: "#363636",
            color: "#fff",
            padding: "10px 15px",
            fontSize: "1rem",
            border: "2px solid grey",
        };
    }

    return (
        <Toaster
            position={isMobile ? "top-center" : "top-right"}
            containerStyle={containerStyle}
            toastOptions={{
                style: toastStyle,
            }}
        >
            {(t) => (
                <ToastBar toast={t}>
                    {({ icon, message }) => (
                        <div className="flex" onClick={() => toast.dismiss(t.id)}>
                            {icon}
                            {message}
                        </div>
                    )}
                </ToastBar>
            )}
        </Toaster>
    );
};

export default ToastManager;
