import validate from "../validate.js";
import { stringify } from "../../utils/jsonHelper.js";
import { LogErrorStack } from "../../utils/log.js";
import { NextFunction } from "express";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { z } from "zod";

// Mocking necessary dependencies
vi.mock("../../utils/jsonHelper", () => ({
    stringify: vi.fn((obj) => JSON.stringify(obj)),
}));

// Mock interfaces
class MockRequest {
    public method = "GET";
    public query: any = {};
    public body: any = {};
}

class MockResponse {
    public status = vi.fn().mockReturnThis();
    public send = vi.fn().mockReturnThis();
}

interface MockNextFunction extends NextFunction {
    (): void;
}

describe("validate() validate method", () => {
    let mockRequest: MockRequest;
    let mockResponse: MockResponse;
    let mockNextFunction: MockNextFunction;

    beforeEach(() => {
        mockRequest = new MockRequest();
        mockResponse = new MockResponse();
        mockNextFunction = vi.fn() as any;
    });

    // Happy path: Valid data for GET request
    it("should call next function for valid GET request data", async () => {
        const schema = z.object({ name: z.string() });
        mockRequest.query = { name: "John" };

        const middleware = validate(schema);
        await middleware(mockRequest as any, mockResponse as any, mockNextFunction);

        expect(mockNextFunction).toHaveBeenCalled();
        expect(mockResponse.status).not.toHaveBeenCalled();
        expect(mockResponse.send).not.toHaveBeenCalled();
    });

    // Happy path: Valid data for POST request
    it("should call next function for valid POST request data", async () => {
        const schema = z.object({ age: z.number() });
        mockRequest.method = "POST";
        mockRequest.body = { age: 30 };

        const middleware = validate(schema);
        await middleware(mockRequest as any, mockResponse as any, mockNextFunction);

        expect(mockNextFunction).toHaveBeenCalled();
        expect(mockResponse.status).not.toHaveBeenCalled();
        expect(mockResponse.send).not.toHaveBeenCalled();
    });

    // Edge case: Invalid data for GET request
    it("should return 400 status for invalid GET request data", async () => {
        const schema = z.object({ name: z.string() });
        mockRequest.query = { name: 123 };

        const middleware = validate(schema);
        await middleware(mockRequest as any, mockResponse as any, mockNextFunction);

        expect(mockResponse.status).toHaveBeenCalledWith(400);
        expect(mockResponse.send).toHaveBeenCalledWith(
            stringify({
                success: false,
                data: null,
                error: "Invalid data",
                validationErrors: [{ message: "name is Invalid input: expected string, received number" }],
            })
        );
        expect(mockNextFunction).not.toHaveBeenCalled();
    });

    // Edge case: Invalid data for POST request
    it("should return 400 status for invalid POST request data", async () => {
        const schema = z.object({ age: z.number() });
        mockRequest.method = "POST";
        mockRequest.body = { age: "thirty" };

        const middleware = validate(schema);
        await middleware(mockRequest as any, mockResponse as any, mockNextFunction);

        expect(mockResponse.status).toHaveBeenCalledWith(400);
        expect(mockResponse.send).toHaveBeenCalledWith(
            stringify({
                success: false,
                data: null,
                error: "Invalid data",
                validationErrors: [{ message: "age is Invalid input: expected number, received string" }],
            })
        );
        expect(mockNextFunction).not.toHaveBeenCalled();
    });

    // Edge case: Unexpected error
    it("should return 500 status for unexpected errors", async () => {
        const schema = z.object({ name: z.string() });
        mockRequest.query = { name: "John" };

        vi.spyOn(schema, "parse").mockImplementation(() => {
            throw new Error("Unexpected error");
        });

        const middleware = validate(schema);
        await middleware(mockRequest as any, mockResponse as any, mockNextFunction);

        expect(mockResponse.status).toHaveBeenCalledWith(500);
        expect(mockResponse.send).toHaveBeenCalledWith(
            stringify({
                success: false,
                data: null,
                error: "Unexpected server error",
            })
        );
        expect(LogErrorStack).toHaveBeenCalled();
        expect(mockNextFunction).not.toHaveBeenCalled();
    });
});
