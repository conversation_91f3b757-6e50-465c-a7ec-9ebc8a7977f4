/**
 * Backend notification error handling and monitoring
 */

import { logger } from "./log.js";
import type { FirebaseError, FirebaseErrorCode, NotificationResult } from "../types/firebase.js";

// Error severity levels
export enum ErrorSeverity {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

// Error categories
export enum ErrorCategory {
    TOKEN = 'token',
    NETWORK = 'network',
    CONFIGURATION = 'configuration',
    RATE_LIMIT = 'rate_limit',
    AUTHENTICATION = 'authentication',
    UNKNOWN = 'unknown'
}

// Structured error interface
export interface NotificationError {
    code: string;
    message: string;
    category: ErrorCategory;
    severity: ErrorSeverity;
    timestamp: Date;
    userId?: number;
    token?: string;
    context?: Record<string, any>;
    stackTrace?: string;
}

// Error mapping for Firebase errors
const FIREBASE_ERROR_MAP: Record<string, { category: ErrorCategory; severity: ErrorSeverity; action: string }> = {
    'messaging/invalid-registration-token': {
        category: ErrorCategory.TOKEN,
        severity: ErrorSeverity.MEDIUM,
        action: 'remove_token'
    },
    'messaging/registration-token-not-registered': {
        category: ErrorCategory.TOKEN,
        severity: ErrorSeverity.MEDIUM,
        action: 'remove_token'
    },
    'messaging/internal-error': {
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.HIGH,
        action: 'retry'
    },
    'messaging/server-unavailable': {
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.HIGH,
        action: 'retry'
    },
    'messaging/quota-exceeded': {
        category: ErrorCategory.RATE_LIMIT,
        severity: ErrorSeverity.MEDIUM,
        action: 'backoff'
    },
    'messaging/invalid-argument': {
        category: ErrorCategory.CONFIGURATION,
        severity: ErrorSeverity.HIGH,
        action: 'alert'
    },
    'messaging/third-party-auth-error': {
        category: ErrorCategory.AUTHENTICATION,
        severity: ErrorSeverity.HIGH,
        action: 'alert'
    }
};

// Create structured error from Firebase error
export const createNotificationError = (
    error: any,
    userId?: number,
    token?: string,
    context?: Record<string, any>
): NotificationError => {
    const timestamp = new Date();
    const stackTrace = error?.stack;

    // Handle Firebase errors
    if (error?.code && FIREBASE_ERROR_MAP[error.code]) {
        const mapping = FIREBASE_ERROR_MAP[error.code];
        return {
            code: error.code,
            message: error.message || `Firebase error: ${error.code}`,
            category: mapping.category,
            severity: mapping.severity,
            timestamp,
            userId,
            token: token ? `${token.substring(0, 10)}...` : undefined, // Truncate for privacy
            context: {
                ...context,
                action: mapping.action
            },
            stackTrace
        };
    }

    // Handle generic errors
    const message = error?.message || error?.toString() || 'Unknown notification error';
    return {
        code: 'unknown',
        message,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        timestamp,
        userId,
        token: token ? `${token.substring(0, 10)}...` : undefined,
        context,
        stackTrace
    };
};

// Log error with appropriate level
export const logNotificationError = (error: NotificationError): void => {
    const logData = {
        timestamp: error.timestamp.toISOString(),
        code: error.code,
        message: error.message,
        category: error.category,
        severity: error.severity,
        userId: error.userId,
        token: error.token,
        context: error.context
    };

    switch (error.severity) {
        case ErrorSeverity.CRITICAL:
            logger.error('Critical notification error', logData);
            break;
        case ErrorSeverity.HIGH:
            logger.error('High severity notification error', logData);
            break;
        case ErrorSeverity.MEDIUM:
            logger.warn('Medium severity notification error', logData);
            break;
        case ErrorSeverity.LOW:
        default:
            logger.info('Low severity notification error', logData);
            break;
    }
};

// Send error to monitoring service
export const reportNotificationError = async (error: NotificationError): Promise<void> => {
    // Log to application logger
    logNotificationError(error);
    
    // TODO: Implement error reporting to monitoring service
    // Example implementations:
    
    // Send to Discord webhook for critical errors
    if (error.severity === ErrorSeverity.CRITICAL && process.env.DISCORD_WEBHOOK_URL) {
        try {
            // Implementation would go here
        } catch (webhookError) {
            logger.error('Failed to send error to Discord webhook', { error: webhookError });
        }
    }
    
    // Send to external monitoring service (e.g., Sentry, DataDog)
    // if (process.env.SENTRY_DSN) {
    //     // Sentry implementation
    // }
};

// Metrics tracking for notification errors
interface ErrorMetrics {
    totalErrors: number;
    errorsByCategory: Record<ErrorCategory, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    errorsByCode: Record<string, number>;
    lastReset: Date;
}

class NotificationErrorTracker {
    private metrics: ErrorMetrics = {
        totalErrors: 0,
        errorsByCategory: {} as Record<ErrorCategory, number>,
        errorsBySeverity: {} as Record<ErrorSeverity, number>,
        errorsByCode: {},
        lastReset: new Date()
    };

    trackError(error: NotificationError): void {
        this.metrics.totalErrors++;
        this.metrics.errorsByCategory[error.category] = (this.metrics.errorsByCategory[error.category] || 0) + 1;
        this.metrics.errorsBySeverity[error.severity] = (this.metrics.errorsBySeverity[error.severity] || 0) + 1;
        this.metrics.errorsByCode[error.code] = (this.metrics.errorsByCode[error.code] || 0) + 1;
    }

    getMetrics(): ErrorMetrics {
        return { ...this.metrics };
    }

    resetMetrics(): void {
        this.metrics = {
            totalErrors: 0,
            errorsByCategory: {} as Record<ErrorCategory, number>,
            errorsBySeverity: {} as Record<ErrorSeverity, number>,
            errorsByCode: {},
            lastReset: new Date()
        };
    }

    shouldAlert(): boolean {
        const now = new Date();
        const timeSinceReset = now.getTime() - this.metrics.lastReset.getTime();
        const hoursElapsed = timeSinceReset / (1000 * 60 * 60);
        
        // Alert if more than 100 errors in the last hour
        if (hoursElapsed <= 1 && this.metrics.totalErrors > 100) {
            return true;
        }
        
        // Alert if more than 10 critical errors
        if ((this.metrics.errorsBySeverity[ErrorSeverity.CRITICAL] || 0) > 10) {
            return true;
        }
        
        return false;
    }
}

export const errorTracker = new NotificationErrorTracker();

// Enhanced error handler for notification operations
export const handleNotificationError = async (
    error: any,
    userId?: number,
    token?: string,
    context?: Record<string, any>
): Promise<NotificationError> => {
    const notificationError = createNotificationError(error, userId, token, context);
    
    // Track error metrics
    errorTracker.trackError(notificationError);
    
    // Report error
    await reportNotificationError(notificationError);
    
    // Check if we should send alerts
    if (errorTracker.shouldAlert()) {
        logger.error('Notification error threshold exceeded', {
            metrics: errorTracker.getMetrics()
        });
        // TODO: Send alert to monitoring system
    }
    
    return notificationError;
};

// Check if error indicates token should be removed
export const shouldRemoveToken = (error: NotificationError): boolean => {
    return error.context?.action === 'remove_token' || 
           error.code === 'messaging/invalid-registration-token' ||
           error.code === 'messaging/registration-token-not-registered';
};

// Check if operation should be retried
export const shouldRetryOperation = (error: NotificationError, attemptNumber: number): boolean => {
    const maxRetries = 3;
    
    if (attemptNumber >= maxRetries) return false;
    
    return error.context?.action === 'retry' ||
           error.category === ErrorCategory.NETWORK ||
           error.code === 'messaging/internal-error' ||
           error.code === 'messaging/server-unavailable';
};

// Get retry delay for operations
export const getRetryDelay = (attemptNumber: number): number => {
    // Exponential backoff: 1s, 2s, 4s
    return Math.pow(2, attemptNumber) * 1000;
};

// Error handler wrapper for async notification functions
export const withNotificationErrorHandling = <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    context?: Record<string, any>
) => {
    return async (...args: T): Promise<R | null> => {
        try {
            return await fn(...args);
        } catch (error) {
            await handleNotificationError(error, undefined, undefined, context);
            return null;
        }
    };
};
