/**
 * Notification queue system for optimized delivery
 */

import { logger } from "../utils/log.js";
import { sendPushNotifications } from "../config/firebase.js";
import { handleNotificationError } from "../utils/notificationErrorHandler.js";
import type { NotificationPayload, BatchNotificationResult } from "../types/firebase.js";
import { NotificationTypes } from "../types/notification.js";

// Queue item interface
export interface QueuedNotification {
    id: string;
    userId: number;
    token: string;
    payload: NotificationPayload;
    priority: NotificationPriority;
    scheduledAt: Date;
    maxRetries: number;
    retryCount: number;
    createdAt: Date;
}

// Notification priority levels
export enum NotificationPriority {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    URGENT = 3
}

// Batch configuration
interface BatchConfig {
    maxSize: number;
    maxWaitTime: number; // milliseconds
    priorityThreshold: NotificationPriority;
}

// Default batch configurations by priority
const BATCH_CONFIGS: Record<NotificationPriority, BatchConfig> = {
    [NotificationPriority.URGENT]: {
        maxSize: 10,
        maxWaitTime: 1000, // 1 second
        priorityThreshold: NotificationPriority.URGENT
    },
    [NotificationPriority.HIGH]: {
        maxSize: 50,
        maxWaitTime: 5000, // 5 seconds
        priorityThreshold: NotificationPriority.HIGH
    },
    [NotificationPriority.NORMAL]: {
        maxSize: 100,
        maxWaitTime: 30000, // 30 seconds
        priorityThreshold: NotificationPriority.NORMAL
    },
    [NotificationPriority.LOW]: {
        maxSize: 200,
        maxWaitTime: 60000, // 1 minute
        priorityThreshold: NotificationPriority.LOW
    }
};

// Map notification types to priorities
const NOTIFICATION_PRIORITY_MAP: Record<NotificationTypes, NotificationPriority> = {
    [NotificationTypes.hospitalised]: NotificationPriority.HIGH,
    [NotificationTypes.jail]: NotificationPriority.HIGH,
    [NotificationTypes.message]: NotificationPriority.NORMAL,
    [NotificationTypes.bailed_out]: NotificationPriority.NORMAL,
    [NotificationTypes.fight_win]: NotificationPriority.NORMAL,
    [NotificationTypes.fight_win_attacked]: NotificationPriority.HIGH,
    [NotificationTypes.fight_timeout]: NotificationPriority.LOW,
    [NotificationTypes.profile_comment]: NotificationPriority.LOW,
    [NotificationTypes.levelup]: NotificationPriority.NORMAL,
    [NotificationTypes.crafting_completion]: NotificationPriority.LOW,
    [NotificationTypes.job_paid]: NotificationPriority.LOW,
    [NotificationTypes.transfer_sent]: NotificationPriority.NORMAL,
    [NotificationTypes.transfer_received]: NotificationPriority.NORMAL,
    [NotificationTypes.quest_complete]: NotificationPriority.NORMAL,
    [NotificationTypes.quest_step_complete]: NotificationPriority.LOW,
    [NotificationTypes.bounty_placed]: NotificationPriority.HIGH,
    [NotificationTypes.bounty_claimed]: NotificationPriority.NORMAL,
    [NotificationTypes.new_patch_notes]: NotificationPriority.LOW,
    [NotificationTypes.banned]: NotificationPriority.URGENT,
    [NotificationTypes.death_noted]: NotificationPriority.HIGH,
    [NotificationTypes.life_noted]: NotificationPriority.HIGH,
    [NotificationTypes.kompromat_jailed]: NotificationPriority.HIGH,
    [NotificationTypes.course_complete]: NotificationPriority.NORMAL,
    [NotificationTypes.admin_action]: NotificationPriority.URGENT,
    [NotificationTypes.auction_item_sold]: NotificationPriority.NORMAL,
    [NotificationTypes.auction_item_expired]: NotificationPriority.LOW,
    [NotificationTypes.suggestion_comment]: NotificationPriority.LOW,
    [NotificationTypes.mission_completed]: NotificationPriority.NORMAL,
    [NotificationTypes.gang_invite]: NotificationPriority.NORMAL,
    [NotificationTypes.gang_invite_request]: NotificationPriority.NORMAL,
    [NotificationTypes.gang_rank_change]: NotificationPriority.NORMAL,
    [NotificationTypes.gang_kicked]: NotificationPriority.HIGH,
    [NotificationTypes.temporary_notification]: NotificationPriority.NORMAL,
    [NotificationTypes.suggestion_status_change]: NotificationPriority.LOW,
    [NotificationTypes.lottery_won]: NotificationPriority.HIGH,
    [NotificationTypes.injured]: NotificationPriority.HIGH,
    [NotificationTypes.friend_request]: NotificationPriority.LOW,
    [NotificationTypes.friend_request_accepted]: NotificationPriority.LOW,
    [NotificationTypes.referral_reward]: NotificationPriority.NORMAL
};

class NotificationQueue {
    private queues: Map<NotificationPriority, QueuedNotification[]> = new Map();
    private timers: Map<NotificationPriority, NodeJS.Timeout> = new Map();
    private isProcessing: Map<NotificationPriority, boolean> = new Map();

    constructor() {
        // Initialize queues for each priority level
        Object.values(NotificationPriority).forEach(priority => {
            if (typeof priority === 'number') {
                this.queues.set(priority, []);
                this.isProcessing.set(priority, false);
            }
        });
    }

    // Add notification to queue
    async enqueue(
        userId: number,
        token: string,
        payload: NotificationPayload,
        notificationType?: NotificationTypes,
        customPriority?: NotificationPriority
    ): Promise<string> {
        const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const priority = customPriority ?? (notificationType ? NOTIFICATION_PRIORITY_MAP[notificationType] : NotificationPriority.NORMAL);
        
        const queuedNotification: QueuedNotification = {
            id,
            userId,
            token,
            payload,
            priority,
            scheduledAt: new Date(),
            maxRetries: 3,
            retryCount: 0,
            createdAt: new Date()
        };

        const queue = this.queues.get(priority)!;
        queue.push(queuedNotification);

        logger.debug(`Notification queued: ${id} (priority: ${priority}, queue size: ${queue.length})`);

        // Check if we should process immediately
        await this.checkAndProcessQueue(priority);

        return id;
    }

    // Check if queue should be processed
    private async checkAndProcessQueue(priority: NotificationPriority): Promise<void> {
        const queue = this.queues.get(priority)!;
        const config = BATCH_CONFIGS[priority];
        const isProcessing = this.isProcessing.get(priority)!;

        if (isProcessing) return;

        // Process immediately if queue is full or has urgent notifications
        if (queue.length >= config.maxSize || priority === NotificationPriority.URGENT) {
            await this.processQueue(priority);
            return;
        }

        // Set timer for batch processing if not already set
        if (!this.timers.has(priority)) {
            const timer = setTimeout(async () => {
                await this.processQueue(priority);
            }, config.maxWaitTime);
            
            this.timers.set(priority, timer);
        }
    }

    // Process queue for specific priority
    private async processQueue(priority: NotificationPriority): Promise<void> {
        const queue = this.queues.get(priority)!;
        
        if (queue.length === 0) return;

        this.isProcessing.set(priority, true);

        // Clear timer if exists
        const timer = this.timers.get(priority);
        if (timer) {
            clearTimeout(timer);
            this.timers.delete(priority);
        }

        const config = BATCH_CONFIGS[priority];
        const batch = queue.splice(0, config.maxSize);

        logger.info(`Processing notification batch: ${batch.length} notifications (priority: ${priority})`);

        try {
            // Convert to format expected by sendPushNotifications
            const tokens = batch.map(notification => ({
                userId: notification.userId,
                token: notification.token
            }));

            // Group by payload to optimize sending
            const payloadGroups = new Map<string, { payload: NotificationPayload; tokens: typeof tokens }>();
            
            batch.forEach((notification, index) => {
                const payloadKey = JSON.stringify(notification.payload);
                if (!payloadGroups.has(payloadKey)) {
                    payloadGroups.set(payloadKey, { payload: notification.payload, tokens: [] });
                }
                payloadGroups.get(payloadKey)!.tokens.push(tokens[index]);
            });

            // Send notifications for each unique payload
            const results: BatchNotificationResult[] = [];
            for (const [, group] of payloadGroups) {
                const result = await sendPushNotifications(group.tokens, group.payload);
                results.push(result);

                // Handle failed notifications
                if (result.invalidTokens.length > 0) {
                    logger.warn(`Found ${result.invalidTokens.length} invalid tokens in batch`);
                    // TODO: Clean up invalid tokens from database
                }
            }

            // Log batch results
            const totalSent = results.reduce((sum, result) => sum + result.totalSent, 0);
            const totalFailed = results.reduce((sum, result) => sum + result.totalFailed, 0);
            
            logger.info(`Batch processing complete: ${totalSent} sent, ${totalFailed} failed (priority: ${priority})`);

        } catch (error) {
            logger.error(`Error processing notification batch (priority: ${priority}):`, error);
            
            // Re-queue failed notifications with increased retry count
            const retriableNotifications = batch.filter(notification => 
                notification.retryCount < notification.maxRetries
            );

            for (const notification of retriableNotifications) {
                notification.retryCount++;
                notification.scheduledAt = new Date(Date.now() + Math.pow(2, notification.retryCount) * 1000);
                queue.push(notification);
            }

            await handleNotificationError(error, undefined, undefined, {
                operation: 'processQueue',
                priority,
                batchSize: batch.length
            });
        } finally {
            this.isProcessing.set(priority, false);
            
            // Check if there are more notifications to process
            if (queue.length > 0) {
                await this.checkAndProcessQueue(priority);
            }
        }
    }

    // Get queue statistics
    getStats(): Record<string, any> {
        const stats: Record<string, any> = {};
        
        this.queues.forEach((queue, priority) => {
            stats[`priority_${priority}`] = {
                queueSize: queue.length,
                isProcessing: this.isProcessing.get(priority),
                hasTimer: this.timers.has(priority)
            };
        });

        return stats;
    }

    // Force process all queues (for shutdown)
    async processAllQueues(): Promise<void> {
        const priorities = Array.from(this.queues.keys()).sort((a, b) => b - a); // Process high priority first
        
        for (const priority of priorities) {
            await this.processQueue(priority);
        }
    }
}

// Singleton instance
export const notificationQueue = new NotificationQueue();

// Helper function to get priority for notification type
export const getNotificationPriority = (notificationType: NotificationTypes): NotificationPriority => {
    return NOTIFICATION_PRIORITY_MAP[notificationType] ?? NotificationPriority.NORMAL;
};
