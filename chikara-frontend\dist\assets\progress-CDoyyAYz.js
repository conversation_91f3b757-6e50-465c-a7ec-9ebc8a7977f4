import{r as p,a8 as l,aS as y,aU as g,ae as d}from"./index-hZ2cjOe1.js";var v="Progress",m=100,[w,A]=y(v),[I,E]=w(v),P=p.forwardRef((e,a)=>{const{__scopeProgress:n,value:s=null,max:r,getValueLabel:i=R,...u}=e;(r||r===0)&&!f(r)&&console.error(z(`${r}`,"Progress"));const t=f(r)?r:m;s!==null&&!x(s,t)&&console.error(_(`${s}`,"Progress"));const o=x(s,t)?s:null,j=c(o)?i(o,t):void 0;return l.jsx(I,{scope:n,value:o,max:t,children:l.jsx(g.div,{"aria-valuemax":t,"aria-valuemin":0,"aria-valuenow":c(o)?o:void 0,"aria-valuetext":j,role:"progressbar","data-state":h(o,t),"data-value":o??void 0,"data-max":t,...u,ref:a})})});P.displayName=v;var N="ProgressIndicator",b=p.forwardRef((e,a)=>{const{__scopeProgress:n,...s}=e,r=E(N,n);return l.jsx(g.div,{"data-state":h(r.value,r.max),"data-value":r.value??void 0,"data-max":r.max,...s,ref:a})});b.displayName=N;function R(e,a){return`${Math.round(e/a*100)}%`}function h(e,a){return e==null?"indeterminate":e===a?"complete":"loading"}function c(e){return typeof e=="number"}function f(e){return c(e)&&!isNaN(e)&&e>0}function x(e,a){return c(e)&&!isNaN(e)&&e<=a&&e>=0}function z(e,a){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${a}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${m}\`.`}function _(e,a){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${a}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${m} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var $=P,M=b;const S=p.forwardRef(({className:e,barClassName:a,value:n,showPercentage:s,displayText:r,frontGradient:i,backGradient:u,...t},o)=>l.jsxs($,{ref:o,className:d("relative h-4 w-full overflow-hidden rounded-xs bg-zinc-900/20 dark:bg-zinc-50/20",e),...t,children:[s&&l.jsxs("p",{className:"-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 z-10",children:[n,"%"]}),r&&l.jsx("p",{className:"-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2 z-10 tracking-wide text-gray-200",children:r}),u&&l.jsx("div",{className:d(u,"absolute top-0 z-5 h-2 w-full")}),l.jsx(M,{className:d(a,"size-full flex-1 transition-all"),style:{transform:`translateX(-${100-(n||0)}%)`}}),i&&l.jsx("div",{style:{transform:`translateX(-${100-(n||0)}%)`},className:d(i,"absolute top-0 z-5 h-2 w-full rounded-b-md")})]}));S.displayName=$.displayName;export{S as P};
