import { useNormalStore } from "@/app/store/stores";
import bountyIcon from "@/assets/icons/bounty.webp";
import classroomIcon from "@/assets/icons/classroom.webp";
import leaderboardIcon from "@/assets/icons/leaderboard.webp";
import shoelockerIcon from "@/assets/icons/shoelockericon.webp";
import storyIcon from "@/assets/icons/story.webp";
import studentcouncilIcon from "@/assets/icons/studentcouncil.webp";
import facultyListIcon from "@/assets/icons/studentlisticon.webp";
import workshopIcon from "@/assets/icons/workshop.webp";
import { sceneManager } from "@/helpers/sceneManager";

// Background images
import classroomBg from "@/assets/images/classroom1Day.webp";
import hallwayBg from "@/assets/images/hallway1Day.webp";
import purplePattern from "@/assets/images/UI/BackgroundImages/purple2.jpg";
import bluePattern from "@/assets/images/UI/BackgroundImages/blue3.jpg";
import greenPattern from "@/assets/images/UI/BackgroundImages/green1.jpg";

import { checkLevelGate } from "@/helpers/levelGates";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";
import type { User } from "@/types/user";

interface SchoolPage {
    name: string;
    icon: string;
    link: string;
    description: string;
    backgroundImage: string;
    gridSize: string;
    priority: number;
    levelGate?: string;
}

export default function School() {
    const { craftCollectReady } = useNormalStore();
    const { data: currentUser } = useFetchCurrentUser();

    const isPageDisabled = (page: SchoolPage): string | false => {
        const { displayName, isLocked, requiredLevel } = checkLevelGate(page?.levelGate, currentUser?.level);
        if (isLocked) return `${displayName} - Requires level ${requiredLevel}`;
        return false;
    };

    const pages: SchoolPage[] = [
        {
            name: "Faculty List",
            icon: facultyListIcon,
            link: "/facultylist",
            description: "Browse all academy faculty members and their specialties",
            backgroundImage: hallwayBg,
            gridSize: "md:col-span-1 md:row-span-1",
            priority: 1,
        },
        {
            name: "Mission Board",
            icon: classroomIcon,
            link: "/missions",
            description: "Accept challenging missions to earn rewards and experience",
            backgroundImage: sceneManager("street1"),
            gridSize: "md:col-span-2 md:row-span-1",
            priority: 2,
        },
        {
            name: "Workshop",
            icon: workshopIcon,
            link: "/workshop",
            levelGate: "crafting",
            description: "Craft powerful items and equipment using collected materials",
            backgroundImage: greenPattern,
            gridSize: "md:col-span-1 md:row-span-2",
            priority: 3,
        },
        {
            name: "Courses",
            icon: storyIcon,
            link: "/courses",
            levelGate: "courses",
            description: "Enroll in academy courses to develop your skills and knowledge",
            backgroundImage: classroomBg,
            gridSize: "md:col-span-2 md:row-span-1",
            priority: 4,
        },
        {
            name: "Bounty Board",
            icon: bountyIcon,
            link: "/bountyboard",
            description: "Hunt dangerous targets for substantial rewards",
            backgroundImage: sceneManager("smallclassroom1"),
            gridSize: "md:col-span-1 md:row-span-1",
            priority: 5,
        },
        {
            name: "Leaderboard",
            icon: leaderboardIcon,
            link: "/leaderboard",
            description: "View academy rankings and compete with other students",
            backgroundImage: sceneManager("schoolField1"),
            gridSize: "md:col-span-1 md:row-span-1",
            priority: 6,
        },
        {
            name: "Rooftop Battles",
            icon: classroomIcon,
            link: "/rooftop",
            description: "Unique Boss Encounters",
            backgroundImage: sceneManager("schoolroof1"),
            gridSize: "md:col-span-2 md:row-span-1",
            priority: 7,
        },
        // {
        //     name: "Classroom",
        //     icon: classroomIcon,
        //     link: "/classroom",
        //     description: "Take exams and tests to prove your academic prowess",
        //     backgroundImage: classroomBg,
        //     gridSize: "md:col-span-2 md:row-span-1",
        //     priority: 7,
        // },
        // {
        //     name: "Shoe Locker",
        //     icon: shoelockerIcon,
        //     link: "/shoelocker",
        //     description: "Store and organize your equipment and personal belongings",
        //     backgroundImage: pinkPattern,
        //     gridSize: "md:col-span-1 md:row-span-1",
        //     priority: 8,
        // },
        // {
        //     name: "Student Council",
        //     icon: studentcouncilIcon,
        //     link: "/studentcouncil",
        //     description: "Participate in academy governance and student affairs",
        //     backgroundImage: bluePattern,
        //     gridSize: "md:col-span-1 md:row-span-1",
        //     priority: 9,
        // },
        {
            name: "Suggestions",
            icon: studentcouncilIcon,
            link: "/suggestions",
            description: "Submit ideas and feedback to improve the academy experience",
            backgroundImage: sceneManager("hallway2"),
            gridSize: "md:col-span-1 md:row-span-1",
            priority: 10,
        },
    ];

    return (
        <div className="mx-auto max-w-7xl p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 auto-rows-fr">
                {pages.map((page) => {
                    const disabled = isPageDisabled(page);

                    return (
                        <div key={page.link} className={cn("group", page.gridSize)}>
                            <Link
                                to={page.link}
                                className={cn(
                                    "block h-full rounded-xl overflow-hidden shadow-lg transition-all duration-300",
                                    "hover:shadow-2xl hover:scale-[1.02] transform hover:z-10 relative",
                                    disabled && "pointer-events-none opacity-60"
                                )}
                            >
                                <div
                                    className="absolute inset-0 bg-cover bg-center transition-transform duration-500 ease-in-out group-hover:scale-110"
                                    style={{ backgroundImage: `url(${page.backgroundImage})` }}
                                />
                                <div
                                    className="absolute inset-0"
                                    style={{
                                        background: "linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.4))",
                                    }}
                                />
                                <div className="relative h-full min-h-[200px] flex flex-col justify-between p-6 text-white">
                                    {/* Top section with notifications only */}
                                    <div className="flex items-start justify-end">
                                        <div className="flex flex-col items-end space-y-2">
                                            {/* Workshop notification */}
                                            {page.name === "Workshop" && craftCollectReady && (
                                                <div className="size-3 rounded-full bg-red-500 animate-pulse shadow-lg"></div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Bottom section with icon, title and description in a row */}
                                    <div className="flex items-start space-x-4">
                                        <div className="flex-shrink-0">
                                            <img src={page.icon} alt={page.name} className="size-12 drop-shadow-lg" />
                                        </div>

                                        <div className="flex-1 space-y-2">
                                            <h3
                                                className={cn(
                                                    "text-xl font-bold leading-tight",
                                                    disabled && "text-gray-400"
                                                )}
                                            >
                                                {disabled ? disabled : page.name}
                                            </h3>

                                            {!disabled && (
                                                <p className="text-sm text-gray-200 leading-relaxed opacity-90 group-hover:opacity-100 transition-opacity">
                                                    {page.description}
                                                </p>
                                            )}
                                        </div>
                                    </div>

                                    {/* Gradient overlay for better text readability */}
                                    {/* <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-40 pointer-events-none"></div> */}
                                </div>
                            </Link>
                        </div>
                    );
                })}
            </div>
        </div>
    );
}
