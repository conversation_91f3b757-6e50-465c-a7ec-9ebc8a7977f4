import { useQueryClient } from "@tanstack/react-query";
import { motion, useAnimation, type Variants } from "framer-motion";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import missingIcon from "@/assets/images/missingIcon.png";
import Button from "@/components/Buttons/Button";
import { DisplayItem } from "@/components/DisplayItem";
import StatusEffects from "@/components/StatusEffects";
import { rarityColours } from "@/helpers/rarityColours";
import { getScavengeLocation } from "@/helpers/scavengeLocations";
import { sceneManager } from "@/helpers/sceneManager";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import type { Item } from "@/types/item";
import useExploreScavengeChoice from "../api/useExploreScavengeChoice";
import type { ScavengeLocation, ScavengeResult, ExploreNodeLocation } from "../types/explore.types";

interface ScavengeViewProps {
    nodeId: number;
    location: ExploreNodeLocation;
    choices: string[];
    onClose: () => void;
}

// Constants
const ANIMATION_VARIANTS = {
    container: {
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 1 } },
    } as Variants,
    wipe: {
        hidden: { width: "100%", left: 0 },
        visible: {
            width: 0,
            left: "50%",
            transition: { ease: "easeInOut", duration: 0.75 },
        },
    } as Variants,
    dialogueBox: {
        hidden: { opacity: 0, scale: 0.9 },
        visible: {
            opacity: 1,
            scale: 1,
            transition: { duration: 0.3, ease: "easeOut" },
        },
    } as Variants,
};

const SCAVENGE_ICONS: Record<string, string> = {
    trash: "https://img.icons8.com/?size=100&id=67367&format=png",
    medical: "https://img.icons8.com/?size=100&id=vB6PoShzdZKw&format=png",
    upgrade: "https://img.icons8.com/?size=100&id=HA930KjFD9ki&format=png",
    herb: "https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png",
    tech: "https://img.icons8.com/?size=100&id=fzdr7mjSaS_w&format=png",
    ore: "https://img.icons8.com/?size=100&id=Iqzd6gV1vpm5&format=png",
};

// Utility functions
const getScavengeIcon = (scavengeType: string): string => SCAVENGE_ICONS[scavengeType] || missingIcon;

const getFailureType = (hasInjury: boolean): "failureInjury" | "failureJail" =>
    hasInjury ? "failureInjury" : "failureJail";

// Custom hooks
const useScavengeQueries = () => {
    const queryClient = useQueryClient();

    const invalidateQueries = async () => {
        await Promise.all([
            queryClient.invalidateQueries({ queryKey: api.explore.getMapByLocation.key() }),
            queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() }),
        ]);
    };

    return { invalidateQueries };
};

// Status Icon Component
const StatusIcon = ({ isSuccess }: { isSuccess: boolean }) => {
    const iconClass = isSuccess ? "text-green-400" : "text-red-400";
    const bgClass = isSuccess ? "bg-green-500/20" : "bg-red-500/20";

    return (
        <div className={cn("w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full flex items-center justify-center", bgClass)}>
            <svg
                className={cn("w-8 h-8 md:w-10 md:h-10", iconClass)}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
            >
                {isSuccess ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                )}
            </svg>
        </div>
    );
};

// Item Reward Component
const ItemReward = ({ item, quantity }: { item: Item; quantity?: number }) => (
    <div className="bg-slate-700/50 rounded-xl p-4 md:p-6 border border-indigo-400/30">
        <div className="flex items-center justify-center gap-3 md:gap-4">
            <span className="text-white text-lg md:text-xl font-semibold">{quantity ?? 1}x</span>
            <DisplayItem item={item} height="h-10 w-auto md:h-12 lg:h-14" className="inline-block" />
            <span className={cn("text-lg md:text-xl font-semibold", rarityColours(item.rarity))}>{item.name}</span>
        </div>
    </div>
);

// Choice Button Component
const ChoiceButton = ({
    choice,
    index,
    onSelect,
    isDisabled,
    locationDetails,
}: {
    choice: string;
    index: number;
    onSelect: (index: number) => void;
    isDisabled: boolean;
    locationDetails: ScavengeLocation;
}) => {
    const actionText = locationDetails?.choices?.[choice]?.action || "";

    return (
        <button
            type="button"
            disabled={isDisabled}
            className={cn(
                "cursor-pointer group relative overflow-hidden rounded-xl bg-gradient-to-br from-indigo-600 to-purple-700",
                "hover:from-indigo-500 hover:to-purple-600 transition-all duration-300",
                "border-2 border-indigo-400/30 hover:border-indigo-400/60",
                "shadow-lg hover:shadow-xl transform hover:scale-[1.02]",
                "disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",
                "p-6 md:p-8 h-24 md:h-32 lg:h-36"
            )}
            onClick={() => onSelect(index + 1)}
        >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative flex items-center justify-center gap-3 md:gap-4 h-full">
                <img
                    src={getScavengeIcon(choice)}
                    className="w-8 h-8 md:w-12 md:h-12 lg:w-16 lg:h-16 object-contain"
                    alt={`${choice} icon`}
                />
                <span className="text-white font-semibold text-sm md:text-base lg:text-lg uppercase tracking-wide">
                    {actionText}
                </span>
            </div>
        </button>
    );
};

// Result Panel Component
const ResultPanel = ({
    scavengeResult,
    resultText,
    onAction,
    isLoading,
    isMobile,
    actionButtonText,
    actionButtonVariant = "primary",
}: {
    scavengeResult: ScavengeResult;
    resultText: string;
    onAction: () => void;
    isLoading: boolean;
    isMobile: number | boolean;
    actionButtonText: string;
    actionButtonVariant?: "primary" | "destructive";
}) => (
    <div className="flex flex-col items-center gap-6 md:gap-8 text-center">
        <div className="space-y-4">
            <StatusIcon isSuccess={scavengeResult.success} />
            <p className="text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-2xl">{resultText}</p>
        </div>

        {/* Item Reward for Success */}
        {scavengeResult.success && scavengeResult.itemReward && (
            <ItemReward item={scavengeResult.itemReward} quantity={scavengeResult.itemQuantity} />
        )}

        {/* Status Effects for Failure */}
        {!scavengeResult.success && scavengeResult.injury && (
            <div className="bg-slate-700/50 rounded-xl p-4 md:p-6 border border-red-400/30">
                <StatusEffects
                    currentEffects={[{ id: 1, count: 1, debuff: scavengeResult.injury }]}
                    className="justify-center"
                />
            </div>
        )}

        <div className="w-full max-w-sm">
            <Button
                variant={actionButtonVariant}
                size={isMobile ? "md" : "lg"}
                isLoading={isLoading}
                className="w-full text-base md:text-lg"
                onClick={onAction}
            >
                {actionButtonText}
            </Button>
        </div>
    </div>
);

export const ScavengeView = ({ nodeId, choices, onClose }: ScavengeViewProps) => {
    const controls = useAnimation();
    const [isLoading, setIsLoading] = useState(false);
    const [scavengeResult, setScavengeResult] = useState<ScavengeResult | null>(null);
    const isMobile = useCheckMobileScreen();
    const navigate = useNavigate();
    const { invalidateQueries } = useScavengeQueries();

    const { mutate: makeScavengeChoice, isPending: isProcessingChoice } = useExploreScavengeChoice();
    const locationDetails = getScavengeLocation(choices);
    const sceneImage = sceneManager("citystreet1");

    useEffect(() => {
        controls.start("visible");
    }, [controls]);

    const handleChoiceSelect = (choiceIndex: number) => {
        if (isProcessingChoice) return;

        makeScavengeChoice(
            { nodeId, choiceIndex },
            {
                onSuccess: (response) => {
                    setScavengeResult({
                        success: response.success || false,
                        choice: response.choice || choices[choiceIndex - 1],
                        message: response.message || "Scavenging completed",
                        itemReward: response.itemReward as Item,
                        itemQuantity: response.itemQuantity,
                        jailed: response.jailed,
                        jailDuration: response.jailDuration,
                        injury: response.injury,
                    });
                },
                onError: (error) => {
                    setScavengeResult({
                        success: false,
                        choice: choices[choiceIndex - 1],
                        message: error.message || "Something went wrong during scavenging",
                    });
                },
            }
        );
    };

    const handleClose = async () => {
        setIsLoading(true);
        await invalidateQueries();
        onClose();
        setIsLoading(false);
    };

    const handleJailNavigation = async () => {
        setIsLoading(true);
        await invalidateQueries();
        navigate("/jail");
    };

    const getResultContent = () => {
        if (!scavengeResult) return null;

        const failureType = getFailureType(!!scavengeResult.injury);
        const resultText = scavengeResult.success
            ? locationDetails?.choices?.[scavengeResult.choice]?.success || ""
            : locationDetails?.choices?.[scavengeResult.choice]?.[failureType] || "";

        if (scavengeResult.success) {
            return (
                <ResultPanel
                    scavengeResult={scavengeResult}
                    resultText={resultText}
                    isLoading={isLoading}
                    isMobile={isMobile}
                    actionButtonText="Return to Explore"
                    onAction={handleClose}
                />
            );
        }

        return (
            <ResultPanel
                scavengeResult={scavengeResult}
                resultText={resultText}
                isLoading={isLoading}
                isMobile={isMobile}
                actionButtonText={scavengeResult.jailed ? "Go to Jail" : "Continue Exploring"}
                actionButtonVariant={scavengeResult.jailed ? "destructive" : "primary"}
                onAction={scavengeResult.jailed ? handleJailNavigation : handleClose}
            />
        );
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
            <motion.div
                className="relative w-full h-full max-w-6xl max-h-[90vh] overflow-hidden rounded-none md:rounded-2xl shadow-2xl"
                variants={ANIMATION_VARIANTS.container}
                initial="hidden"
                animate={controls}
            >
                {/* Background Image */}
                <div className="absolute inset-0">
                    <img className="w-full h-full object-cover" src={sceneImage || ""} alt="Scavenging location" />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/50" />
                </div>

                {/* Animated Wipe Effect */}
                <motion.div
                    className="absolute inset-0 bg-black z-10"
                    variants={ANIMATION_VARIANTS.wipe}
                    initial="hidden"
                    animate="visible"
                />

                {/* Content Container */}
                <motion.div
                    variants={ANIMATION_VARIANTS.dialogueBox}
                    initial="hidden"
                    animate="visible"
                    className="relative z-20 h-full flex items-center justify-center p-4"
                >
                    <div className="w-full max-w-4xl bg-slate-800/95 backdrop-blur-md border-4 border-indigo-500/80 rounded-2xl shadow-2xl">
                        {/* Header */}
                        <div className="px-6 py-4 border-b border-indigo-500/30">
                            <h2 className="text-center text-indigo-400 text-xl md:text-2xl lg:text-3xl font-semibold">
                                {locationDetails?.location}
                            </h2>
                        </div>

                        {/* Main Content */}
                        <div className="p-6 md:p-8 min-h-[400px] md:min-h-[500px] flex flex-col">
                            {scavengeResult ? (
                                <div className="flex-1 flex flex-col justify-center">{getResultContent()}</div>
                            ) : (
                                <div className="flex-1 flex flex-col justify-center gap-6 md:gap-8">
                                    <div className="text-center">
                                        <p className="text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto">
                                            {locationDetails?.description}
                                        </p>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 max-w-4xl mx-auto w-full">
                                        {isProcessingChoice ? (
                                            <div className="text-center">
                                                <div className="inline-flex items-center gap-2 text-gray-300 text-sm md:text-base">
                                                    <div className="size-6 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                                                </div>
                                            </div>
                                        ) : (
                                            choices.map((choice, index) => (
                                                <ChoiceButton
                                                    key={choice}
                                                    choice={choice}
                                                    index={index}
                                                    isDisabled={isProcessingChoice}
                                                    locationDetails={locationDetails}
                                                    onSelect={handleChoiceSelect}
                                                />
                                            ))
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </motion.div>
            </motion.div>
        </div>
    );
};
