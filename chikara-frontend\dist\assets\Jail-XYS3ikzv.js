import{a6 as l,a7 as m,at as u,a8 as r}from"./index-hZ2cjOe1.js";import{U as d}from"./UsersTable-ewU9L9xl.js";function x(){const{isLoading:e,error:a,data:s}=l(m.jail.jailList.queryOptions({select:i=>i.sort((o,n)=>o.jailedUntil>n.jailedUntil?1:-1)})),{data:t}=u();return a?"An error has occurred: "+a.message:r.jsx("div",{className:"mb-8 md:mx-auto md:mb-0 md:max-w-6xl",children:r.jsx(d,{data:s,isLoading:e,type:"jail",currentUser:t})})}export{x as default};
