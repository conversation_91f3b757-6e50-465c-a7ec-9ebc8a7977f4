# Chikara Academy Talents Content Creation Guide

## Overview

This guide provides comprehensive instructions for creating talents content in Chikara Academy, a persistent browser-based game (PBBG) featuring a sophisticated talent system with multiple skill trees and combat abilities.

## Database Schema Overview

### Core Talent Model (`talent` table)

The talent system is built around the following database structure:

```sql
model talent {
  id                   Int                       @id @default(autoincrement())
  name                 String                    @db.VarChar(255)
  displayName          String                    @db.VarChar(255)
  tree                 TalentTree
  talentType           TalentType                @default(passive)
  description          String?                   @db.Text
  skillLevelRequired   Int                       @db.UnsignedInt
  pointsInTreeRequired Int                       @db.UnsignedInt
  pointsCost           Int                       @default(1) @db.UnsignedInt
  maxPoints            Int                       @db.UnsignedInt
  staminaCost          Int?                      @db.UnsignedInt
  tier1Modifier        Float?                    @db.Float
  tier2Modifier        Float?                    @db.Float
  tier3Modifier        Float?                    @db.Float
  secondaryModifier    Float?                    @db.Float
  // Relations...
}
```

### Supporting Models

- **`user_talent`**: Links users to their acquired talents with levels
- **`user_equipped_abilities`**: Tracks equipped combat abilities (up to 4 slots)
- **`user_skill`**: Manages skill progression and talent points per skill tree

## Talent System Structure

### 1. Talent Trees (Skill Categories)

The game features the following talent trees:

**Combat Stats:**
- `strength` - Physical damage, melee combat
- `dexterity` - Accuracy, ranged combat, speed
- `defence` - Damage reduction, shields, armor
- `intelligence` - Abilities, crafting, magic-like effects
- `endurance` - Health, stamina, regeneration
- `vitality` - Overall survivability and utility

**Crafting Skills:**
- `mining` - Resource extraction
- `scavenging` - Finding materials and items
- `foraging` - Gathering consumables and reagents
- `fabrication` - Creating weapons and heavy equipment
- `outfitting` - Crafting armor and protective gear
- `chemistry` - Consumables, buffs, debuffs
- `electronics` - Advanced technology and mods

### 2. Talent Types

**Passive Talents (`passive`):**
- Always active once learned
- Modify base stats or provide permanent bonuses
- Examples: increased damage, better regeneration, crafting bonuses

**Active Abilities (`ability`):**
- Combat abilities that can be equipped and used
- Consume stamina when activated
- Limited to 4 equipped abilities at once
- Examples: healing spells, damage abilities, crowd control

### 3. Talent Progression

**Skill Level Requirements:**
- Talents unlock at specific skill levels (5, 10, 20, 40, etc.)
- Higher tiers require more skill investment

**Tree Points Requirements:**
- Some talents require a minimum number of points spent in the tree
- Creates natural progression gates

**Talent Levels:**
- Most talents have 1-3 levels
- Each level increases effectiveness via tier modifiers

## Content Creation Requirements

### 1. Talent Identification

**Name Field:**
- Use snake_case format (e.g., `melee_damage_increase`)
- Must be unique across all talents
- Should be descriptive of the talent's function

**Display Name:**
- Human-readable name for UI display
- Use Title Case (e.g., "Melee Damage Increase")

### 2. Talent Tree Assignment

Choose the most appropriate tree based on the talent's primary function:

**Combat Stat Trees:**
- **Strength**: Physical damage, melee weapons, intimidation
- **Dexterity**: Ranged combat, speed, agility, stealth
- **Defence**: Damage reduction, shields, armor, blocking
- **Intelligence**: Abilities, crafting efficiency, tactical skills
- **Endurance**: Health, stamina, regeneration, survival
- **Vitality**: Overall utility, versatility, adaptability

**Crafting Trees:**
- **Mining**: Resource extraction, ore processing
- **Scavenging**: Finding items, salvaging materials
- **Foraging**: Gathering consumables, botanical knowledge
- **Fabrication**: Heavy equipment, weapons, tools
- **Outfitting**: Armor, protective gear, utility items
- **Chemistry**: Consumables, medical items, chemical reactions
- **Electronics**: Technology, gadgets, advanced equipment

### 3. Talent Progression Design

**Skill Level Requirements:**
- Tier 0: Level 1 (starting talents)
- Tier 1: Level 5 (basic advancement)
- Tier 2: Level 10 (intermediate skills)
- Tier 3: Level 20 (advanced abilities)
- Tier 4: Level 40 (master-level talents)

**Tree Points Requirements:**
- Early talents: 0-5 points
- Mid-tier talents: 5-15 points
- Advanced talents: 15-25 points
- Master talents: 25+ points

**Point Cost:**
- Most talents cost 1 point per level
- Powerful or broadly applicable talents may cost 2-3 points
- Ultimate abilities might cost 5+ points

**Max Points:**
- Passive talents: Usually 1-3 levels
- Active abilities: Usually 1-3 levels
- Utility talents: 1-5 levels for scalable effects

### 4. Modifier System

**Tier Modifiers (tier1Modifier, tier2Modifier, tier3Modifier):**
- Define the talent's effectiveness at each level
- Use appropriate scaling (linear, exponential, or custom)
- Examples:
  - Damage increase: 1.1, 1.25, 1.5 (10%, 25%, 50% increase)
  - Flat bonuses: 5, 10, 20 (additive increases)
  - Percentages: 0.1, 0.15, 0.25 (10%, 15%, 25%)

**Secondary Modifier:**
- Optional additional effect or scaling factor
- Used for talents with multiple effects
- Examples: critical hit chance, duration, range

**Stamina Cost (for abilities only):**
- Balance active abilities with resource cost
- Typical range: 5-25 stamina
- More powerful abilities cost more stamina

### 5. Description Guidelines

**Format:**
- Clear, concise description of the talent's effect
- Include specific numbers when possible
- Use consistent terminology across similar talents

**Examples:**
- "Increases melee weapon damage by X%"
- "Reduces crafting time by X%"
- "Grants X% chance to find additional resources"
- "Heals X health over Y seconds"

### 6. Balance Considerations

**Passive Talents:**
- Moderate bonuses (5-25% typical range)
- Avoid making any single talent mandatory
- Consider interaction with other talents

**Active Abilities:**
- Balance power with stamina cost
- Ensure counterplay options exist
- Consider cooldowns if implementing them

**Crafting Talents:**
- Focus on efficiency and quality improvements
- Don't trivialize the crafting process
- Maintain resource economy balance

## Implementation Examples

### Example 1: Passive Strength Talent

```sql
INSERT INTO talent (
  name, displayName, tree, talentType, description,
  skillLevelRequired, pointsInTreeRequired, pointsCost, maxPoints,
  tier1Modifier, tier2Modifier, tier3Modifier
) VALUES (
  'melee_damage_increase', 'Melee Damage Increase', 'strength', 'passive',
  'Increases damage dealt with melee weapons by X%',
  1, 0, 1, 3,
  1.1, 1.2, 1.35
);
```

### Example 2: Active Intelligence Ability

```sql
INSERT INTO talent (
  name, displayName, tree, talentType, description,
  skillLevelRequired, pointsInTreeRequired, pointsCost, maxPoints,
  staminaCost, tier1Modifier, tier2Modifier, tier3Modifier
) VALUES (
  'heal', 'Heal', 'intelligence', 'ability',
  'Instantly restores X health to yourself or an ally',
  5, 3, 1, 3,
  15, 50, 75, 100
);
```

### Example 3: Crafting Talent

```sql
INSERT INTO talent (
  name, displayName, tree, talentType, description,
  skillLevelRequired, pointsInTreeRequired, pointsCost, maxPoints,
  tier1Modifier, tier2Modifier, tier3Modifier
) VALUES (
  'speed_crafter', 'Speed Crafter', 'fabrication', 'passive',
  'Reduces crafting time by X%',
  5, 2, 1, 3,
  0.1, 0.2, 0.3
);
```

## Content Creation Workflow

### 1. Concept Development
- Define the talent's purpose and theme
- Determine which tree it belongs to
- Consider how it fits into the overall progression

### 2. Mechanical Design
- Set appropriate skill level and tree point requirements
- Design modifier values for balanced progression
- Consider interactions with existing content

### 3. Implementation
- Create database entries with proper values
- Add talent name constants to the codebase
- Implement any special logic if required

### 4. Testing Considerations
- Verify talent unlocks at correct levels
- Test modifier calculations
- Ensure proper UI display
- Check for balance issues

### 5. Documentation
- Update talent lists and guides
- Document any special interactions
- Provide context for future content creators

## Special Considerations

### Talent Interactions
- Some talents may modify how other talents work
- Consider synergies between talents in the same tree
- Avoid creating overpowered combinations

### UI Integration
- Talents need visual representation in the talent tree
- Position values determine grid placement
- Icons and descriptions must be user-friendly

### Combat Integration
- Active abilities must integrate with the battle system
- Consider turn-based combat implications
- Ensure proper stamina management

### Progression Balance
- Maintain meaningful choices at each level
- Avoid creating "must-have" talents
- Ensure multiple viable build paths

## Common Talent Archetypes

### Damage Dealers
- Direct damage increases
- Critical hit improvements
- Armor penetration
- Weapon specialization

### Defenders
- Damage reduction
- Shield effectiveness
- Health increases
- Regeneration bonuses

### Utility
- Crafting improvements
- Resource gathering bonuses
- Movement enhancements
- Social interaction bonuses

### Active Abilities
- Direct damage spells
- Healing abilities
- Crowd control effects
- Buff/debuff applications

## Quality Assurance Checklist

- [ ] Talent name follows snake_case convention
- [ ] Display name is user-friendly
- [ ] Tree assignment is logical
- [ ] Skill level requirement is appropriate
- [ ] Tree points requirement creates proper gating
- [ ] Modifier values are balanced
- [ ] Description is clear and specific
- [ ] Talent type is correct (passive/ability)
- [ ] Stamina cost is set for abilities
- [ ] No conflicts with existing talents
- [ ] Proper database constraints are met

## Maintenance and Updates

### Version Control
- Track all talent changes
- Document balance adjustments
- Maintain backward compatibility when possible

### Player Feedback
- Monitor talent usage statistics
- Gather player feedback on balance
- Adjust as needed for game health

### Content Expansion
- Plan talent trees for new content
- Consider how new talents interact with existing ones
- Maintain consistent design philosophy

This guide provides the foundation for creating engaging, balanced, and well-integrated talents for Chikara Academy's progression system. Always consider the player experience and maintain consistency with the game's overall design philosophy.
