import { logger } from "../utils/log.js";
import {
    handleNotificationError,
    shouldRemoveToken,
    shouldRetryOperation,
    getRetryDelay,
} from "../utils/notificationErrorHandler.js";
/* eslint-disable unicorn/prefer-export-from */
import * as admin from "firebase-admin";
import { App, cert, initializeApp } from "firebase-admin/app";
import { getMessaging, type Message } from "firebase-admin/messaging";

// Configuration interface for better type safety
interface FirebaseConfig {
    projectId: string;
    clientEmail: string;
    privateKey: string;
    appBaseUrl: string;
    enabled: boolean;
}

// Import and re-export shared types
import type {
    FirebaseNotificationPayload,
    NotificationResult,
    BatchNotificationResult,
    FirebaseError,
    FirebaseErrorCode,
} from "../types/firebase.js";

// Re-export types for convenience
export type NotificationPayload = FirebaseNotificationPayload;
export type { NotificationResult, BatchNotificationResult, FirebaseError, FirebaseErrorCode };

// Get configuration from environment variables
const getFirebaseConfig = (): FirebaseConfig => {
    const config = {
        projectId: process.env.FIREBASE_PROJECT_ID || "chikara-academy",
        clientEmail:
            process.env.FIREBASE_CLIENT_EMAIL || "<EMAIL>",
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n") || "",
        appBaseUrl: process.env.APP_BASE_URL || "https://api.battleacademy.io",
        enabled: process.env.FIREBASE_ENABLED === "true",
    };

    // Validate required configuration
    if (config.enabled) {
        const requiredFields = ["projectId", "clientEmail", "privateKey"];
        const missingFields = requiredFields.filter((field) => !config[field as keyof FirebaseConfig]);

        if (missingFields.length > 0) {
            throw new Error(`Firebase configuration incomplete. Missing: ${missingFields.join(", ")}`);
        }
    }

    return config;
};

// Keep track of app initialization status
let firebaseApp: App | null = null;
let firebaseConfig: FirebaseConfig;

// Initialize Firebase conditionally
export const initializeFirebase = (): Promise<void> => {
    return new Promise<void>((resolve, reject) => {
        try {
            firebaseConfig = getFirebaseConfig();

            if (firebaseConfig.enabled) {
                // Initialize the app if it hasn't been initialized already
                if (!firebaseApp) {
                    firebaseApp = initializeApp({
                        credential: cert({
                            projectId: firebaseConfig.projectId,
                            clientEmail: firebaseConfig.clientEmail,
                            privateKey: firebaseConfig.privateKey,
                        }),
                    });
                }
                logger.info("Firebase Notifications Service initialized successfully");
            } else {
                logger.info("Firebase Notifications Service disabled");
            }
            resolve();
        } catch (error) {
            logger.error(`Failed to initialize Firebase Notifications Service: ${String(error)}`);
            reject(error);
        }
    });
};

// Check if Firebase is available
export const isFirebaseAvailable = (): boolean => {
    return firebaseApp !== null && firebaseConfig?.enabled === true;
};

// Enhanced function to send push notification to a specific token
export async function sendPushNotification(
    userId: number,
    token: string,
    payload: NotificationPayload,
    retryCount = 0
): Promise<NotificationResult> {
    const maxRetries = 3;
    const result: NotificationResult = { success: false, token };

    if (!isFirebaseAvailable()) {
        result.error = "Firebase not initialized or disabled";
        return result;
    }

    if (!token || token.length < 10) {
        result.error = "Invalid token provided";
        return result;
    }

    const message: Message = {
        token: token,
        notification: {
            title: payload.title,
            body: payload.body,
        },
        webpush: {
            headers: {
                image: payload.image || "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
            },
            fcmOptions: {
                link: payload.clickAction || firebaseConfig.appBaseUrl,
            },
        },
        android: {
            notification: {
                imageUrl: payload.image || "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
                icon: payload.icon,
                clickAction: payload.clickAction,
            },
        },
        apns: {
            payload: {
                aps: {
                    badge: 1,
                    sound: "default",
                },
            },
        },
        data: payload.data || {},
    };

    try {
        const messaging = getMessaging();
        const response = await messaging.send(message);

        if (response) {
            logger.info(`Push notification sent successfully - userId: ${userId}, messageId: ${response}`);
            result.success = true;
            result.messageId = response;
            return result;
        } else {
            result.error = "No response from Firebase messaging service";
        }
    } catch (error: any) {
        // Use enhanced error handling
        const notificationError = await handleNotificationError(error, userId, token, {
            operation: "sendPushNotification",
            attempt: retryCount + 1,
            maxRetries,
            payload: {
                title: payload.title,
                hasData: !!payload.data,
            },
        });

        result.error = notificationError.message;

        // Check if token should be removed
        if (shouldRemoveToken(notificationError)) {
            result.error = "Invalid or expired token";
            // Don't retry for invalid tokens
            return result;
        }

        // Retry for recoverable errors
        if (retryCount < maxRetries && shouldRetryOperation(notificationError, retryCount)) {
            const delay = getRetryDelay(retryCount);
            logger.info(`Retrying push notification in ${delay}ms... (attempt ${retryCount + 2}/${maxRetries + 1})`);

            await new Promise((resolve) => setTimeout(resolve, delay));
            return sendPushNotification(userId, token, payload, retryCount + 1);
        }
    }

    return result;
}

// Backward compatibility function
export async function sendPushNotificationLegacy(
    userId: number,
    token: string,
    message: string
): Promise<string | null> {
    const payload: NotificationPayload = {
        title: "Chikara Academy MMO",
        body: message,
    };

    const result = await sendPushNotification(userId, token, payload);
    return result.success ? result.messageId || "sent" : null;
}

// Enhanced batch notification function
export async function sendPushNotifications(
    tokens: { userId: number | null; token: string }[],
    payload: NotificationPayload
): Promise<BatchNotificationResult> {
    const result: BatchNotificationResult = {
        totalSent: 0,
        totalFailed: 0,
        results: [],
        invalidTokens: [],
    };

    if (!isFirebaseAvailable()) {
        logger.warn("Firebase not available for batch notifications");
        return result;
    }

    if (tokens.length === 0) {
        logger.warn("No tokens provided for batch notifications");
        return result;
    }

    logger.info(`Sending batch notifications to ${tokens.length} tokens`);

    // Process notifications in batches to avoid overwhelming the service
    const batchSize = 100;
    const batches = [];

    for (let i = 0; i < tokens.length; i += batchSize) {
        batches.push(tokens.slice(i, i + batchSize));
    }

    for (const batch of batches) {
        const promises = batch.map(async (tokenRecord) => {
            if (!tokenRecord.userId || !tokenRecord.token) {
                return { success: false, error: "Invalid token record", token: tokenRecord.token };
            }

            return sendPushNotification(tokenRecord.userId, tokenRecord.token, payload);
        });

        const batchResults = await Promise.allSettled(promises);

        for (const promiseResult of batchResults) {
            if (promiseResult.status === "fulfilled") {
                const notificationResult = promiseResult.value;
                result.results.push(notificationResult);

                if (notificationResult.success) {
                    result.totalSent++;
                } else {
                    result.totalFailed++;

                    // Track invalid tokens for cleanup
                    if (notificationResult.error?.includes("Invalid or expired token") && notificationResult.token) {
                        result.invalidTokens.push(notificationResult.token);
                    }
                }
            } else {
                result.totalFailed++;
                result.results.push({
                    success: false,
                    error: promiseResult.reason?.message || "Promise rejected",
                });
            }
        }

        // Add a small delay between batches to be respectful to the service
        if (batches.length > 1) {
            await new Promise((resolve) => setTimeout(resolve, 100));
        }
    }

    logger.info(
        `Batch notification complete: ${result.totalSent} sent, ${result.totalFailed} failed, ${result.invalidTokens.length} invalid tokens`
    );
    return result;
}

// Legacy function for backward compatibility
export async function sendPushNotificationsLegacy(
    tokens: { userId: number | null; token: string }[],
    message: string
): Promise<void> {
    const payload: NotificationPayload = {
        title: "Chikara Academy MMO",
        body: message,
    };

    await sendPushNotifications(tokens, payload);
}

// Export Firebase admin for direct access if needed
export { admin };
