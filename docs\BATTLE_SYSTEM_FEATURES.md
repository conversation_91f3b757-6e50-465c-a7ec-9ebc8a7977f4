# Chikara Academy Battle System - Feature Summary

## Overview

The Chikara Academy battle system is a comprehensive turn-based combat system supporting multiple battle types, AI opponents, complex skill mechanics, and strategic decision-making.

## Battle Types

### 1. Player vs Player (PvP)

- **Cost**: 2 Action Points
- **Features**:
    - Real-time battles between human players
    - Post-battle actions (mug, cripple, leave)
    - Anti-bullying protection (daily attack limits)
    - Anonymous attacks (with special items)
    - Jail chance for aggressive actions
    - Bounty system integration

### 2. Player vs Environment (PvE) - Roguelike

- **Cost**: No Action Points
- **Features**:
    - Procedurally generated NPC encounters
    - Boss battles with increased rewards
    - Roguelike progression system
    - Map-based buffs (strength, defense, dexterity)
    - Item and crate drops
    - XP scaling based on level difference

### 3. Rooftop Battles (PvE-Rooftop)

- **Cost**: 1 Action Point
- **Features**:
    - Unique boss NPC encounters on rooftops
    - One-time defeats (can't fight same NPC twice)
    - Special item rewards
    - Level gate requirement (minimum level)

## Combat Mechanics

### Turn-Based System

- **Initiative**: Random determination of who attacks first (affected by dexterity stat)
- **Round Structure**: Both players act in each round
- **Turn Counter**: Tracks turns for escalating damage after turn 10
- **Status Effect Processing**: Applied at end of each turn

### Damage Calculation

```
Base Damage = Combat Stat + Weapon Damage × 1.25
Effective Mitigation = (Defense + Armor) × 0.8
Net Damage = Base Damage - Effective Mitigation + 20
Final Damage = Max(1, Net Damage × Random Modifier)
```

### Combat Stats

- **Strength**: Affects melee damage
- **Dexterity**: Affects ranged damage
- **Defense**: Reduces incoming damage
- **Intelligence**: Affects skill damage
- **Stamina**: Affects maximum combat stamina

## AI System

### Decision Making

- **Action Scoring**: AI evaluates all available actions and assigns scores
- **Priority Multipliers**:
    - Killing Blow: 10.0x
    - Low Health Healing: 8.0x
    - High Damage Skills: 3.0x
    - Poison Effects: 2.5x

### Health Thresholds

- **Critical**: Below 30% health
- **Low**: Below 50% health
- **Medium**: Below 70% health

### AI Behavior Patterns

- Prioritizes killing blows
- Uses healing when health is low
- Chooses appropriate attack types based on stats
- Manages ammo and reload timing
- Applies status effects strategically

## Skills and Abilities

### Damage Skills

1. **Headbutt**: Percentage-based max health damage
2. **Shield Bash**: Scales with defense stat
3. **Spray**: Multi-ammo ranged attack with scaling damage
4. **Giant Killing Slingshot**: High damage vs full-health enemies
5. **Toxic Dart**: Poison damage over time

### Healing Skills

1. **Heal**: Immediate health restoration
2. **Heal Over Time**: 15% max health over 3 turns
3. **Max HP Heal**: Large immediate heal (75% max health)

### Buff Skills

1. **Self Harm**: Damage boost at cost of health
2. **High Guard**: Damage reduction
3. **Rage**: Melee damage increase (60% boost)

### Debuff Skills

1. **Cripple**: Reduces enemy defense
2. **Stun**: Prevents enemy actions for multiple turns
3. **Shockwave**: Knockback with stun effect
4. **Sleep**: Puts enemy to sleep (broken by damage)
5. **Disarm**: Removes weapon effectiveness
6. **Exhaust**: Reduces enemy strength

### Utility Skills

1. **Reload**: Restores ammo to maximum
2. **Various consumable skills**: Based on equipped items

## Equipment System

### Weapon Types

- **Melee Weapons**: Scale with Strength
- **Ranged Weapons**: Scale with Dexterity, require ammo
- **Dual Wielding**: Offhand weapons add damage
- **Shields**: Provide defense, enable shield bash

### Equipment Effects

- **Lifesteal**: Heal based on damage dealt
- **Armor**: Reduces incoming damage
- **Special Effects**: Various item-specific bonuses
- **Upgrade Levels**: Enhance item effectiveness

### Ammo System

- **Base Ammo**: Determined by ranged weapon
- **Quiver Talent**: Increases maximum ammo
- **Reload Skill**: Restores ammo during battle
- **Spray Skill**: Consumes all remaining ammo

## Status Effects

### Buffs

- **Damage Buff**: Increases outgoing damage
- **Defense Buff**: Increases damage resistance
- **Stamina Buff**: Increases maximum stamina
- **Stat Buffs**: Temporary stat increases

### Debuffs

- **Skill Lock**: Prevents skill usage
- **Accuracy Debuff**: Causes attacks to miss
- **Bleed**: Damage over time
- **Skill Damage Debuff**: Reduces skill effectiveness

### Special Effects

- **Stunned**: Skip turns completely
- **Asleep**: Skip turns (broken by damage)
- **Disarmed**: Reduced weapon damage
- **Poisoned**: Continuous health loss

## Consumables System

### Refillable Consumables

- **Battle Usage**: Limited uses per battle
- **Effect Types**:
    - Healing items
    - Damage items
    - Stamina restoration
- **Equipment Slots**: 3 consumable slots available

## Talent Integration

### Combat Talents

- **Melee Damage Increase**: Boost melee attacks
- **Ranger**: Enhance ranged combat
- **Berserker**: Extra damage vs low-health enemies
- **Active Defense**: Improved defensive capabilities
- **Shadowstep**: Dodge chance that decreases over time
- **Deflect Damage**: Chance to negate ranged attacks

### Special Talents

- **Skill Efficiency**: Reduced stamina cost for repeated skills
- **Combat Regeneration**: Health restoration during combat
- **Recovery**: Enhanced stamina regeneration
- **Healthy Caster**: Skill damage boost at high health
- **Rejuvenation**: Health restoration after victories

## Victory Actions (PvP Only)

### Mug

- **Effect**: Steal percentage of opponent's cash
- **XP Reward**: Moderate XP gain
- **Jail Risk**: High chance of jail time
- **Modifiers**: Mugger talent increases cash stolen

### Cripple

- **Effect**: Apply severe injury to opponent
- **XP Reward**: High XP gain
- **Jail Risk**: High chance of jail time
- **Bounty**: Can collect bounties with this action

### Leave

- **Effect**: No additional penalties to opponent
- **XP Reward**: Low XP gain
- **Jail Risk**: No jail risk
- **Ethics**: "Honorable" choice

## Fleeing System

### Flee Mechanics

- **Base Chance**: 25% success rate
- **Modifiers**:
    - Run items: +20% success chance
    - Coward talent: Additional bonus
- **Success**: Battle ends, player escapes
- **Failure**: Enemy gets free attack, potential death

## Special Features

### Level Scaling

- **XP Penalties**: Fighting lower-level opponents gives less XP
- **XP Bonuses**: Fighting higher-level opponents gives more XP
- **Cash Scaling**: Mugging lower-level players yields less cash
- **Protection**: Very low-level players protected from attacks

### Anti-Abuse Systems

- **Daily Limits**: Maximum attacks per target per day
- **Jail System**: Penalties for aggressive actions
- **Anonymous Mode**: Hide identity during attacks
- **Escape Talents**: Reduce jail time and flee penalties

### Shrine Buffs

- **Daily Buffs**: Server-wide bonuses affecting all battles
- **Damage Shrine**: Increases all damage dealt
- **Armor Shrine**: Increases armor effectiveness
- **Jail Shrine**: Affects jail duration

### Battle Logging

- **Combat Log**: Detailed turn-by-turn action recording
- **Timestamp Tracking**: Precise timing of all actions
- **Damage Tracking**: Record all damage sources and amounts
- **Effect Tracking**: Monitor status effect applications and durations

## Error Handling & Validation

### Pre-Battle Validation

- User availability checks
- Resource requirement verification
- Target validity confirmation
- Equipment/skill validation

### Battle State Management

- Redis-based state persistence
- Timeout handling for abandoned battles
- State cleanup for completed battles
- Error recovery for invalid states

This battle system provides a rich, strategic combat experience with multiple layers of complexity while maintaining fairness and preventing abuse through various protective mechanisms.

## Battle Flow

### Pre-Battle

1. **Validation**: Check user status, availability, resources
2. **State Creation**: Initialize battle state in Redis
3. **Player Setup**: Create battle player objects with equipment/stats
4. **Initiative Roll**: Determine attack order

### During Battle

1. **Action Selection**: Player chooses action, AI evaluates options
2. **Action Processing**: Calculate damage, apply effects
3. **Status Updates**: Process ongoing effects, regeneration
4. **Victory Check**: Determine if battle should end
5. **Round Increment**: Move to next round

### Post-Battle

1. **Cleanup**: Remove battle state from Redis
2. **Rewards**: Distribute XP, items, currency
3. **Penalties**: Apply injuries, jail time, hospitalization
4. **Notifications**: Inform players of results
5. **Record Keeping**: Log battle outcomes
