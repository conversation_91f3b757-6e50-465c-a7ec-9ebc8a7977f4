# Item Content Creation Guide

---

#### Overview

Items in Chikara Academy are integral to gameplay, impacting combat, crafting, and player progression. This guide provides the necessary structure and details required to create items that are consistent with game mechanics.

---

#### Item Structure

Each item is defined with the following attributes:

- **ID**: Unique identifier for the item.
- **Name**: Display name of the item.
- **ItemType**: Categorizes the item (e.g., weapon, armour, consumable).
- **Rarity**: Defines the item's rarity level (e.g., novice, legendary).
- **Level**: Indicates level requirement or effectiveness.
- **Description**: Brief text about the item, its lore, or functionality.

---

#### Attributes and Stats

- **CashValue**: The in-game currency value of the item.
- **Image**: Path to the visual representation of the item.
- **Damage/Armour/Health**: Stats that affect gameplay (applicable to weapons and armour).
- **Energy**: Usage of the item affects player energy.
- **ActionPoints**: Number of action points the item provides or requires.
- **BaseAmmo**: Default ammunition for ranged items.

---

#### Effects

- **ItemEffects**: Serialized JSON of any effects the item may have on usage (e.g., buffs or debuffs).

---

#### Relations and Dependencies

The item model's relationships with other entities include:

- **Auction Items**: Items listed for trade or auction.
- **Crafting Recipes**: Dependencies on crafting recipes if applicable.
- **User Inventory**: Tracks player ownership and modifications.

---

#### Creation Guidelines

1. **Consistency**: Ensure consistency with existing game items regarding balance and naming conventions.
2. **Uniqueness**: Each item should offer unique value or mechanics to prevent redundancy.
3. **Lore**: Consider the item's narrative context within the game world.
4. **Balancing**: Reflect on the balance implications the item may have on gameplay.

---

#### Examples

```json
{
  "id": 101,
  "name": "Mystic Sword",
  "itemType": "weapon",
  "rarity": "legendary",
  "level": 15,
  "about": "A sword infused with mystical energy, cherished by ancient warriors.",
  "cashValue": 500,
  "image": "items/mystic_sword.png",
  "damage": 25,
  "armour": 0,
  "energy": 0,
  "actionPoints": 5,
  "baseAmmo": 0,
  "itemEffects": "{\"buffs\": [{\"type\": \"strength\", \"value\": 5}]}"
}
```

---

This markdown file will serve as a reference for developers and designers working on in-game item content.

