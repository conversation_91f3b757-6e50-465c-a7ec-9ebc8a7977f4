import { client } from "@/lib/orpc";
import { initializeApp } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import { persistStore } from "./store/persistStore";
import {
    createNotificationError,
    reportNotificationError,
    getUserFriendlyErrorMessage,
    withErrorHandling,
    isRecoverableError,
    getRetryDelay,
} from "@/utils/notificationErrorHandler";
import type {
    FirebaseClientConfig,
    NotificationPermissionResult,
    TokenServerResponse,
    ForegroundMessageHandler,
    NotificationSupport,
    TokenState,
} from "@/types/firebase";

// Firebase configuration from environment variables
const firebaseConfig: Omit<FirebaseClientConfig, "vapidKey"> = {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyCQ-QeWMrBZw8v9idybo_cFwAIlDO6j-z0",
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "chikara-academy.firebaseapp.com",
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "chikara-academy",
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "chikara-academy.appspot.com",
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "75175802639",
    appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:75175802639:web:a1cc5e6073f185ec46707a",
};

const VAPID_KEY =
    import.meta.env.VITE_FIREBASE_VAPID_KEY ||
    "BCv5d6-bgzNJ3EmseSvXJNcGKMFwmFMI_bk-CALpT67DSxgY6qT7_mh68egbVsZ2xR9O4OuuzstFz60uzLFFVCk";

// Validate required configuration
const validateFirebaseConfig = () => {
    const requiredFields = ["apiKey", "authDomain", "projectId", "messagingSenderId", "appId"];
    const missingFields = requiredFields.filter((field) => !firebaseConfig[field as keyof typeof firebaseConfig]);

    if (missingFields.length > 0) {
        console.error("Missing Firebase configuration fields:", missingFields);
        throw new Error(`Firebase configuration incomplete. Missing: ${missingFields.join(", ")}`);
    }

    if (!VAPID_KEY) {
        console.error("VAPID key is missing");
        throw new Error("Firebase VAPID key is required for push notifications");
    }
};

// Initialize Firebase with validation
let firebaseApp: any;
let messaging: any;

try {
    validateFirebaseConfig();
    firebaseApp = initializeApp(firebaseConfig);
    messaging = getMessaging(firebaseApp);
    console.log("Firebase initialized successfully");
} catch (error) {
    console.error("Failed to initialize Firebase:", error);
    throw error;
}

export { messaging };

// Notification support detection
export const detectNotificationSupport = (): NotificationSupport => {
    return {
        isSupported: "Notification" in window,
        hasPermission: "Notification" in window && Notification.permission === "granted",
        hasServiceWorker: "serviceWorker" in navigator,
        hasPushManager: "serviceWorker" in navigator && "PushManager" in window,
        userAgent: navigator.userAgent,
    };
};

// Enhanced token management with error handling
export const sendTokenToServer = async (token: string): Promise<TokenServerResponse> => {
    try {
        const res = await client.notification.saveFCMToken({ token });
        console.log("Token sent to server successfully:", token.substring(0, 20) + "...");
        return { success: true, data: res };
    } catch (error) {
        const notificationError = createNotificationError(error, {
            operation: "sendTokenToServer",
            token: token.substring(0, 20) + "...",
        });
        await reportNotificationError(notificationError);

        const userMessage = getUserFriendlyErrorMessage(error);
        console.error("Error sending token to server:", userMessage);

        return {
            success: false,
            error: userMessage,
            statusCode: error?.response?.status || 500,
        };
    }
};

export const removeTokenFromServer = async (token: string): Promise<TokenServerResponse> => {
    try {
        const res = await client.notification.removeFCMToken({ token });
        console.log("Token removed from server successfully");
        return { success: true, data: res };
    } catch (error) {
        const notificationError = createNotificationError(error, {
            operation: "removeTokenFromServer",
            token: token.substring(0, 20) + "...",
        });
        await reportNotificationError(notificationError);

        const userMessage = getUserFriendlyErrorMessage(error);
        console.error("Error removing token from server:", userMessage);

        return {
            success: false,
            error: userMessage,
            statusCode: error?.response?.status || 500,
        };
    }
};

// Enhanced permission request with better error handling
export const requestNotificationPermission = async (
    registration: ServiceWorkerRegistration
): Promise<NotificationPermissionResult> => {
    try {
        const { messagingToken, setMessagingToken } = persistStore.getState();

        // Check if we already have a valid token
        if (messagingToken && messagingToken.length > 0) {
            console.log("Notification token already exists");
            return { success: true, permission: "granted", token: messagingToken };
        }

        // Request permission
        const permission = await Notification.requestPermission();
        console.log("Notification permission:", permission);

        if (permission === "granted") {
            try {
                const token = await getToken(messaging, {
                    vapidKey: VAPID_KEY,
                    serviceWorkerRegistration: registration,
                });

                if (token) {
                    const serverResponse = await sendTokenToServer(token);
                    if (serverResponse.success) {
                        setMessagingToken(token);
                        console.log("Token generated and saved successfully");
                        return { success: true, permission, token };
                    } else {
                        return { success: false, permission, error: serverResponse.error };
                    }
                } else {
                    return { success: false, permission, error: "Failed to generate FCM token" };
                }
            } catch (tokenError) {
                console.error("Error generating token:", tokenError);
                return {
                    success: false,
                    permission,
                    error: tokenError instanceof Error ? tokenError.message : "Token generation failed",
                };
            }
        } else if (permission === "denied") {
            console.log("Notifications denied by user");
            return { success: false, permission, error: "Notifications denied by user" };
        } else {
            console.log("Notification permission not granted:", permission);
            return { success: false, permission, error: "Permission not granted" };
        }
    } catch (error) {
        console.error("Error requesting notification permission:", error);
        return {
            success: false,
            permission: "denied",
            error: error instanceof Error ? error.message : "Permission request failed",
        };
    }
};

// Token refresh handling
export const refreshToken = async (registration: ServiceWorkerRegistration): Promise<NotificationPermissionResult> => {
    try {
        const { setMessagingToken } = persistStore.getState();

        const newToken = await getToken(messaging, {
            vapidKey: VAPID_KEY,
            serviceWorkerRegistration: registration,
        });

        if (newToken) {
            const serverResponse = await sendTokenToServer(newToken);
            if (serverResponse.success) {
                setMessagingToken(newToken);
                console.log("Token refreshed successfully");
                return { success: true, permission: "granted", token: newToken };
            } else {
                return { success: false, permission: "granted", error: serverResponse.error };
            }
        } else {
            return { success: false, permission: "granted", error: "Failed to refresh token" };
        }
    } catch (error) {
        console.error("Error refreshing token:", error);
        return {
            success: false,
            permission: "granted",
            error: error instanceof Error ? error.message : "Token refresh failed",
        };
    }
};

// Get current token state
export const getTokenState = (): TokenState => {
    const { messagingToken } = persistStore.getState();
    return {
        token: messagingToken || null,
        isValid: !!(messagingToken && messagingToken.length > 0),
        lastRefresh: null, // Could be enhanced to track refresh time
        error: null,
    };
};

// Validate token format
export const validateToken = (token: string): boolean => {
    if (!token || typeof token !== "string") return false;
    // Basic FCM token validation - tokens are typically 140-200 characters
    return token.length >= 140 && token.length <= 200;
};

// Foreground message handling
export const setupForegroundMessageHandling = (onMessageReceived?: ForegroundMessageHandler) => {
    try {
        onMessage(messaging, (payload) => {
            console.log("Foreground message received:", payload);

            // Show browser notification if the page is visible
            if (document.visibilityState === "visible" && payload.notification) {
                const { title, body, icon } = payload.notification;

                // Create a custom notification or use the provided callback
                if (onMessageReceived) {
                    onMessageReceived(payload);
                } else if ("serviceWorker" in navigator && "Notification" in window) {
                    // Default behavior: show browser notification
                    navigator.serviceWorker.ready.then((registration) => {
                        registration.showNotification(title || "Chikara Academy", {
                            body: body || "You have a new notification",
                            icon: icon || "/logo192.png",
                            badge: "/logo192.png",
                            tag: "chikara-notification",
                            requireInteraction: false,
                            actions: [
                                {
                                    action: "open",
                                    title: "Open App",
                                },
                            ],
                        });
                    });
                }
            }
        });

        console.log("Foreground message handling setup complete");
        return true;
    } catch (error) {
        console.error("Error setting up foreground message handling:", error);
        return false;
    }
};

// Cleanup function for token removal
export const cleanupNotifications = async (): Promise<boolean> => {
    try {
        const { messagingToken, setMessagingToken } = persistStore.getState();

        if (messagingToken) {
            const result = await removeTokenFromServer(messagingToken);
            if (result.success) {
                setMessagingToken("");
                console.log("Notifications cleanup completed");
                return true;
            } else {
                console.error("Failed to remove token from server:", result.error);
                return false;
            }
        }

        return true;
    } catch (error) {
        console.error("Error during notifications cleanup:", error);
        return false;
    }
};
