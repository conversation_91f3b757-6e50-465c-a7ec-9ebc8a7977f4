import type { UserPet } from "@/features/pets/types/pets";
import { api, type QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

type PetListResponse = UserPet[];

const useGetPetList = (options: QueryOptions = {}) => {
    return useQuery(
        api.pets.list.queryOptions({
            staleTime: 60000,
            ...options,
        })
    );
};

export default useGetPetList;
