import { describe, expect, it } from "vitest";
import parseNestedJson, { deserialize } from "../parseJson";

describe("parseJson", () => {
    describe("deserialize", () => {
        it("should parse valid JSON strings", () => {
            expect(deserialize('{"key": "value"}')).toEqual({ key: "value" });
            expect(deserialize("[1, 2, 3]")).toEqual([1, 2, 3]);
            expect(deserialize('"string"')).toBe("string");
            expect(deserialize("123")).toBe(123);
            expect(deserialize("true")).toBe(true);
            expect(deserialize("null")).toBe(null);
        });

        it("should throw error for invalid JSON", () => {
            expect(() => deserialize('{"invalid": json}')).toThrow("Failed to parse JSON string");
            expect(() => deserialize("invalid")).toThrow("Failed to parse JSON string");
            expect(() => deserialize("{incomplete")).toThrow("Failed to parse JSON string");
        });

        it("should handle empty string", () => {
            expect(() => deserialize("")).toThrow("Failed to parse JSON string");
        });
    });

    describe("parseNestedJson", () => {
        it("should return null for null input", () => {
            expect(parseNestedJson(null)).toBe(null);
        });

        it("should return primitive values unchanged", () => {
            expect(parseNestedJson(123)).toBe(123);
            expect(parseNestedJson(true)).toBe(true);
            expect(parseNestedJson("simple string")).toBe("simple string");
        });

        it("should parse JSON strings", () => {
            expect(parseNestedJson('{"key": "value"}')).toEqual({ key: "value" });
            expect(parseNestedJson("[1, 2, 3]")).toEqual([1, 2, 3]);
            expect(parseNestedJson('"nested string"')).toBe("nested string");
        });

        it("should handle nested JSON strings", () => {
            const nestedJson = '{"data": "{\\"inner\\": \\"value\\"}"}';
            const expected = { data: { inner: "value" } };
            expect(parseNestedJson(nestedJson)).toEqual(expected);
        });

        it("should parse arrays with nested JSON", () => {
            const arrayWithJson = ['{"key": "value"}', 123, "normal string"];
            const expected = [{ key: "value" }, 123, "normal string"];
            expect(parseNestedJson(arrayWithJson)).toEqual(expected);
        });

        it("should parse objects with nested JSON values", () => {
            const objectWithJson = {
                normalKey: "normal value",
                jsonKey: '{"nested": "data"}',
                numberKey: 456,
            };
            const expected = {
                normalKey: "normal value",
                jsonKey: { nested: "data" },
                numberKey: 456,
            };
            expect(parseNestedJson(objectWithJson)).toEqual(expected);
        });

        it("should handle deeply nested structures", () => {
            const deeplyNested = {
                level1: {
                    level2: ['{"level3": "{\\"level4\\": \\"deep value\\"}"}'],
                },
            };
            const expected = {
                level1: {
                    level2: [{ level3: { level4: "deep value" } }],
                },
            };
            expect(parseNestedJson(deeplyNested)).toEqual(expected);
        });

        it("should handle strings that look like JSON but aren't", () => {
            expect(parseNestedJson("not json but has {braces}")).toBe("not json but has {braces}");
            expect(parseNestedJson("also not [json] but has brackets")).toBe("also not [json] but has brackets");
        });

        it("should throw error for malformed JSON strings", () => {
            expect(() => parseNestedJson('{"malformed": json}')).toThrow("Failed to parse JSON string");
            expect(() => parseNestedJson("[incomplete")).toThrow("Malformed JSON structure");
        });

        it("should handle arrays with malformed JSON and provide context", () => {
            const arrayWithBadJson = ['{"good": "json"}', '{"bad": json}', "normal string"];
            expect(() => parseNestedJson(arrayWithBadJson)).toThrow("Failed to parse array element at index 1");
        });

        it("should handle empty strings and whitespace", () => {
            expect(parseNestedJson("")).toBe("");
            expect(parseNestedJson("   ")).toBe("   ");
            expect(parseNestedJson("\t\n")).toBe("\t\n");
        });

        it("should handle mixed data types in arrays", () => {
            const mixedArray = ['{"json": "object"}', 123, true, null, "string", ['{"nested": "array"}']];
            const expected = [{ json: "object" }, 123, true, null, "string", [{ nested: "array" }]];
            expect(parseNestedJson(mixedArray)).toEqual(expected);
        });

        it("should handle objects with null and undefined values", () => {
            const objectWithNulls = {
                nullValue: null,
                undefinedValue: undefined,
                jsonString: '{"valid": "json"}',
            };
            const expected = {
                nullValue: null,
                undefinedValue: undefined,
                jsonString: { valid: "json" },
            };
            expect(parseNestedJson(objectWithNulls)).toEqual(expected);
        });

        it("should preserve number precision", () => {
            const numberData = {
                integer: 123,
                float: 123.456,
                jsonNumber: '"789.012"',
            };
            const expected = {
                integer: 123,
                float: 123.456,
                jsonNumber: 789.012, // JSON string gets parsed to number
            };
            expect(parseNestedJson(numberData)).toEqual(expected);
        });
    });
});
