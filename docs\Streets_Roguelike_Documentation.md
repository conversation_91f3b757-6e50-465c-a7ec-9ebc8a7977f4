# Streets Roguelike Gamemode Documentation

## Overview

Streets is an endless roguelike adventure mode set in the perilous corners of Tokyo. It's an action-oriented gamemode where players navigate through procedurally generated maps, facing various encounters while collecting items, battling enemies, and progressing through increasingly challenging zones.

## Core Mechanics

### Map Generation

- **Map Structure**: Each map consists of 18-20 randomly generated nodes connected by edges
- **Node Types**: 6 different encounter types are possible at each node
- **Progression**: Players start at a base node and advance through connected nodes toward a boss encounter
- **Map Layout**: Maps use a branching path structure where nodes can have 1-2 connections (30% chance for double connections)

### Zone System

- **Zone Levels**: Players progress through numbered zones (starting from Zone 1)
- **Zone Selection**: Players can choose any zone up to their highest achieved zone (roguelikeHighscore)
- **Difficulty Scaling**: Enemy strength and rewards scale with zone level
- **Highscore Tracking**: Completing a boss at your current highest zone increases your roguelikeHighscore by 1

### Progression & Persistence

- **Zone Persistence**: Option to "Stay on this Zone" to repeat the same difficulty level
- **Buff Carry-over**: Temporary stat buffs persist between maps within the same run
- **Reset on Defeat**: Death resets current map progress but maintains zone progression
- **Leaderboards**: Zone completion tracked on global leaderboards

## Encounter Types

### 1. Base Node

- **Function**: Starting point of every map
- **Features**: Safe zone where players begin their journey
- **Connections**: Always has 3 outgoing paths

### 2. Battle Encounters (Weight: 9)

- **Enemy Scaling**: Enemies scale based on zone level and player level
- **Battle Types**: Normal combat encounters with standard NPCs
- **Timeout**: 10-minute battle time limit
- **Rewards**: XP, potential item drops, achievement progress
- **Enemy Selection**: 40% chance for quest-specific enemies, otherwise random

### 3. Boss Encounters

- **Map Completion**: Defeating the boss completes the entire map
- **Difficulty**: Bosses have reduced scaling multipliers to balance difficulty:
    - Zones 1-4: 50% of normal scaling
    - Zones 5-9: 60% of normal scaling
    - Zones 10-14: 70% of normal scaling
    - Zone 15+: 80% of normal scaling
- **Timeout**: 10-minute battle time limit
- **Rewards**: Higher XP, guaranteed progression, potential rare drops
- **Highscore**: Beating a boss at your current max zone increases your highscore

### 4. Character Encounters (Weight: 5)

- **NPC Interactions**: Meet various characters who offer rewards or cause problems
- **Outcome Chances**:
    - Good outcomes: 80% chance (100% if roguelikeHighscore < 4)
    - Bad outcomes: 20% chance (mugging, jail, or injury)
- **Rewards**: Cash (20 + zone×10 to 20 + zone×30), potential item drops (40% chance)
- **Special Encounters**:
    - **Apollo**: 5% chance for healing encounter (30% max health recovery)
    - **Mugging**: 20% chance of losing cash instead of gaining it
- **Consequences**:
    - Jail time: 15% chance (10 minutes)
    - Hospitalization: 5% chance (10 minutes)

### 5. Buff Encounters (Weight: 1)

- **Stat Boosts**: Random permanent stat increases for the current map
- **Buff Types**: Strength, Defense, or Dexterity
- **Buff Amounts**: 5-12% increases (0.05-0.12 multiplier)
- **Cap**: Maximum 1.5x multiplier (50% increase) per stat
- **Persistence**: Buffs carry over between maps in the same run

### 6. Scavenge Encounters (Weight: 4.5)

- **Unlocked**: Available starting at Zone 15
- **Time Limit**: 5 minutes to make a choice
- **Options**: Choose between 2 random scavenge types:
    - Trash, Medical, Upgrade, Herb, Tech, Ore
- **Success Rate**: 70% chance of success, 30% chance of failure
- **Success Rewards**: Relevant items based on scavenge type
- **Failure Consequences**:
    - Jail: 20% chance (10 minutes)
    - Injury: 80% chance (minor status effects based on scavenge type)

## Locations

### School (Zone 1+)

- **Minimum Zone**: 1 (always available)
- **Drop Types**: Food, Crafting Materials
- **Difficulty**: Starter location

### Church (Zone 3+)

- **Minimum Zone**: 3
- **Drop Types**: Ores
- **Difficulty**: Easy-Medium

### Mall (Zone 7+)

- **Minimum Zone**: 7
- **Drop Types**: Food, Crafting Materials
- **Difficulty**: Medium

### Shrine (Zone 10+)

- **Minimum Zone**: 10
- **Drop Types**: Gems, Food
- **Difficulty**: Medium-Hard

### Sewers (Zone 12+)

- **Minimum Zone**: 12
- **Requirement**: Complete "The Suspicious Figure" quest
- **Drop Types**: Gems, Crafting Materials
- **Difficulty**: Hard

### Alley (Zone 16+)

- **Minimum Zone**: 16
- **Drop Types**: Ores, Gems
- **Difficulty**: Very Hard

## Combat System

### Battle Mechanics

- **Attack Types**: Melee (STR-based) or Ranged (DEX-based)
- **Buff Application**: Roguelike buffs apply during combat
    - STR buff: +50% of buff value to damage when attacking NPCs
    - DEX buff: +50% of buff value to damage when attacking NPCs
    - DEF buff: +50% of buff value to defense when defending against NPCs
- **Battle State**: Full turn-based combat with status effects, skills, and equipment
- **Victory Rewards**: XP, potential item drops, gang crate drops (15% chance)

### Enemy Scaling

- **Zone Scaling**: Enemies scale exponentially with zone level difference
    - Higher zones: 1.2^(enemy_level - player_level) multiplier
    - Lower zones: 0.9^(player_level - enemy_level) multiplier
- **Type Scaling**: Different multipliers for normal vs boss enemies
- **Level Scaling**: Reduced difficulty for lower-level players

## Reward Systems

### Item Drops

- **Drop Calculation**: Complex system based on zone level and location
- **Quest Priority**: Quest items prioritized in drop calculations
- **Rarity Scaling**: Higher zones offer better drop chances
- **Shrine Buffs**: Daily shrine buffs can enhance rare drop rates
- **Location-Specific**: Each location has unique drop tables

### Scavenge Rewards

- **Type-Specific**: Different scavenge types yield appropriate items
- **Quantity**: Variable quantities based on item and zone level
- **Success Rate**: 70% base success rate across all scavenge types

### Character Encounter Rewards

- **Cash**: Zone-scaled monetary rewards
- **Items**: 40% chance for item drops on good outcomes
- **Healing**: Apollo encounters provide significant health recovery

## Prerequisites & Access

### Unlock Requirements

- **Tutorial Completion**: Must complete "tutorial_buy_and_equip" task
- **Level Gates**: Each location has minimum zone requirements
- **Quest Gates**: Sewers requires specific quest completion

### Resource Costs

- **Action Points**: 1 AP required to start a map
- **No Energy Cost**: Streets mode doesn't consume energy
- **Time Investment**: Maps can take significant time to complete

## Technical Features

### Map Visualization

- **Interactive Map**: Visual representation of nodes and connections
- **Node Status**: Different visual states for completed/available/future nodes
- **Path Planning**: Shows available routes and potential encounters
- **Buff Display**: Current stat buffs shown on map interface

### Session Management

- **Save State**: Current progress saved between sessions
- **Battle Integration**: Seamless transition to dedicated battle interface
- **Timeout Handling**: Automatic resolution for timed encounters
- **Recovery**: Robust error handling and state recovery

### Performance Optimizations

- **Lazy Loading**: Encounter details generated only when needed
- **Efficient Updates**: Minimal data transfer for map state changes
- **Caching**: Strategic caching of map and encounter data

## Configuration & Balance

### Encounter Weights

- Battle: 9 (most common)
- Character: 5
- Scavenge: 4.5
- Buff: 1 (rarest)

### Timeouts

- Battles: 10 minutes
- Boss Battles: 10 minutes
- Scavenge: 5 minutes
- Jail/Hospital: 10 minutes

### Scaling Parameters

- Map Size: 18-20 nodes
- Double Edge Chance: 30%
- Max Buff Cap: 150% (1.5x multiplier)
- Quest Enemy Chance: 40% (normal), 100% (boss)

## Integration with Game Systems

### Achievement System

- NPC battle wins tracked
- Zone completion milestones
- Map completion achievements

### Quest System

- Adventure encounter objectives
- NPC kill objectives by location
- Roguelike level requirements
- Item collection from specific zones

### Economic Integration

- Item drops feed into main economy
- Cash rewards from character encounters
- Equipment and consumable usage

### Social Features

- Leaderboard integration for zone progression
- Gang crate drops for guild members
- Achievement sharing and comparison

## Future Considerations

### Potential Expansions

- Additional locations with unique mechanics
- Seasonal events or limited-time zones
- Cooperative multiplayer maps
- Daily/weekly challenge modes

### Balance Monitoring

- Drop rate adjustments based on economy
- Difficulty scaling refinements
- Encounter weight rebalancing
- Reward structure optimization

This comprehensive system provides an engaging, replayable roguelike experience that integrates deeply with the broader game economy and progression systems while maintaining its own unique identity and challenge structure.
