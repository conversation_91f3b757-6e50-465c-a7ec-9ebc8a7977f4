import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";

const useCancelMining = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.mining.cancelMining.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.mining.getMiningSession.key() });
            },
        })
    );
};

export default useCancelMining;
