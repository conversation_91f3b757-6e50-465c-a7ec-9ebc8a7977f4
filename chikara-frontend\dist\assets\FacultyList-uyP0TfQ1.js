import{a8 as e,a6 as N,a7 as C,bl as k,at as F,au as D,r as u,ae as d,a9 as T,ap as G,aM as R}from"./index-hZ2cjOe1.js";import{D as L}from"./DataTable-IzuqENlT.js";import"./ag-theme-quartz-B8VIcOdW.js";const q=({model:a,onModelChange:l})=>{const n=a&&a.filter||"",i=({target:{value:t}})=>{l(t===""||!t?null:{...a||{type:"equals"},filter:t})};return e.jsx("div",{className:"ag-floating-filter-input-wrapper",children:e.jsxs("select",{value:n,className:"ag-input-field-input ag-text-field-input rounded-md border-gray-600 font-bold text-sm focus:border-[#2196f3] lg:py-1 lg:text-base",onChange:i,children:[e.jsx("option",{value:"",children:"All"}),e.jsx("option",{value:"mizu",children:"Mizu"}),e.jsx("option",{value:"honoo",children:"Honoo"}),e.jsx("option",{value:"tsuchi",children:"Tsuchi"}),e.jsx("option",{value:"kaze",children:"Kaze"})]})})},w=({model:a,onModelChange:l})=>{const n=a&&a.filter||"",i=({target:{value:t}})=>{l(t===""||!t?null:{...a||{type:"equals"},filter:t})};return e.jsx("div",{className:"ag-floating-filter-input-wrapper",children:e.jsxs("select",{value:n,className:"ag-input-field-input ag-text-field-input rounded-md border-gray-600 font-bold text-sm focus:border-[#2196f3] lg:py-1 lg:text-base",onChange:i,children:[e.jsx("option",{value:"",children:"All"}),e.jsx("option",{value:"student",children:"Student"}),e.jsx("option",{value:"prefect",children:"Prefect"}),e.jsx("option",{value:"admin",children:"Staff"})]})})},H=()=>N(C.user.getUserList.queryOptions({staleTime:5*60*1e3,placeholderData:k})),z={filterOptions:["equals"],maxNumConditions:1},B=(a,l,n,i)=>n.data.id-i.data.id;function U(){const{isLoading:a,error:l,data:n}=H(),{data:i}=F(),t=D(),m=s=>s?s.id===1?"text-green-500":"text-red-400":"text-gray-400",x=s=>{const r=s?.value;return e.jsx("div",{className:"mt-4 whitespace-nowrap px-2 py-4 font-bold md:px-6",children:e.jsx("div",{className:d(r==="admin"?"text-red-600":"text-green-600","text-xs md:text-sm dark:text-stroke-sm"),children:r==="admin"?"Staff":R(r)})})},p=s=>{const{gang:r}=s?.data;return e.jsx("div",{className:"mt-4 whitespace-nowrap px-2 py-4 text-center font-bold md:px-6",children:e.jsx("div",{className:d(r?.id===i?.gangId?"text-green-500":"text-red-400",!r&&"text-gray-400","text-xs md:text-sm dark:text-stroke-sm"),children:r?r?.name:"No Gang"})})},f=s=>{const{username:r,id:o,gang:c}=s?.data;return e.jsxs("div",{className:d(s.data?.defeated&&"grayscale",s.data?.disabled&&"opacity-25 grayscale","relative flex h-full items-center justify-normal px-0.5 py-0 font-lili md:w-full md:gap-2"),children:[e.jsxs("div",{className:"mt-1.5 flex min-w-13 flex-col items-center justify-center gap-1 text-center md:mt-0",children:[e.jsx(T,{src:s?.data,className:"aspect-square! size-11 rounded-full border border-blue-800 md:size-13"}),e.jsxs("small",{className:"block font-semibold text-gray-500 text-xs md:hidden dark:text-blue-400",children:["#",o]})]}),e.jsx(G,{to:`/profile/${o}`,children:e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"font-bold text-blue-600 text-sm text-stroke-sm md:text-base",children:[r," "]}),e.jsxs("div",{className:"hidden text-gray-500 text-stroke-sm text-xs md:block md:text-sm dark:text-gray-400",children:["ID ",e.jsxs("span",{className:"text-indigo-400",children:["#",o]})]}),e.jsx("div",{className:d(m(c),"mt-1 block font-semibold text-xs md:hidden md:text-sm dark:text-stroke-sm"),children:c===null?"No Gang":c?.name})]})})]})},[h,M]=u.useState([{headerName:"Name",field:"username",comparator:B,cellRenderer:f,minWidth:t?200:250,maxWidth:t?200:null,suppressFloatingFilterButton:!0,filterParams:{filterOptions:["contains"],defaultOption:"contains"}},{headerName:t?"Lvl":"Level",field:"level",cellClass:"mt-5 text-sm font-bold text-custom-yellow !md:px-0 text-center!",maxWidth:t?100:160,headerClass:"centerGridHeader",filterParams:{filterOptions:["equals","greaterThan","lessThan","inRange"],defaultOption:"equals"}},{headerName:"Gang",cellRenderer:p,field:"gang.name",hide:t,headerClass:"centerGridHeader",suppressFloatingFilterButton:!0},{headerName:"Role",field:"userType",cellRenderer:x,headerClass:"centerGridHeader",cellClass:"text-center",filterParams:z,floatingFilterComponent:w,suppressFloatingFilterButton:!0,suppressHeaderMenuButton:!0},{headerName:"Class",field:"class",cellClass:"text-center mt-5 text-sm font-semibold text-white !md:px-0 text-center!",hide:t,headerClass:"centerGridHeader",floatingFilterComponent:q,suppressFloatingFilterButton:!0,suppressHeaderMenuButton:!0}]),g=u.useRef(null),[b,v]=u.useState(null),j=[],y={};return l?"An error has occurred: "+l.message:e.jsx("div",{className:"mb-8 md:mx-auto md:mb-0 md:max-w-6xl",children:e.jsx(L,{dataList:n,colDefs:h,isLoading:a,initialFilter:y,currentTab:b,setCurrentTab:v,tabs:j,customGridRef:g})})}export{U as default};
