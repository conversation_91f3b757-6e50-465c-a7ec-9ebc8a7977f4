import { api, type QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

interface UseJobRequirementsParams {
    level: number;
    jobId: number;
    options?: QueryOptions;
}

const useJobRequirements = ({ level, jobId, options = {} }: UseJobRequirementsParams) => {
    return useQuery(
        api.jobs.getRequirements.queryOptions({
            input: { level, jobId },
            ...options,
        })
    );
};

export default useJobRequirements;
