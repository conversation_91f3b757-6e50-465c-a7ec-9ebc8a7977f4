import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useHealUser = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.infirmary.revivePlayer.mutationOptions({
            onSuccess: () => {
                toast.success(`You healed the user!`);
                queryClient.invalidateQueries({
                    queryKey: api.infirmary.getInjuredList.key(),
                });
            },
            onError: (error: Error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    return {
        healUser: mutation,
    };
};

export default useHealUser;
