import { describe, expect, it } from "vitest";
import { formatCurrency, formatCompactCurrency, calculateFee, calculateTotalWithFee } from "../currencyHelpers";

describe("currencyHelpers", () => {
    describe("formatCurrency", () => {
        it("should format basic amounts with yen symbol", () => {
            expect(formatCurrency(1000)).toBe("￥1,000");
            expect(formatCurrency(1234567)).toBe("￥1,234,567");
            expect(formatCurrency(0)).toBe("￥0");
        });

        it("should format without symbol when showSymbol is false", () => {
            expect(formatCurrency(1000, { showSymbol: false })).toBe("1,000");
            expect(formatCurrency(1234567, { showSymbol: false })).toBe("1,234,567");
        });

        it("should handle fraction digits", () => {
            // JPY doesn't support fraction digits, so these should round
            expect(formatCurrency(1000.5, { minimumFractionDigits: 0 })).toBe("￥1,001");
            expect(formatCurrency(1000.99, { maximumFractionDigits: 0 })).toBe("￥1,001");
        });

        it("should handle negative amounts", () => {
            expect(formatCurrency(-1000)).toBe("-￥1,000");
            expect(formatCurrency(-1234567)).toBe("-￥1,234,567");
        });

        it("should handle decimal amounts", () => {
            expect(formatCurrency(1000.99)).toBe("￥1,001");
            expect(formatCurrency(999.1)).toBe("￥999");
        });
    });

    describe("formatCompactCurrency", () => {
        it("should format small amounts normally", () => {
            expect(formatCompactCurrency(999)).toBe("¥999");
            expect(formatCompactCurrency(500)).toBe("¥500");
        });

        it("should format thousands with compact notation", () => {
            expect(formatCompactCurrency(1000)).toBe("¥1000"); // Japanese doesn't use K for thousands
            expect(formatCompactCurrency(1500)).toBe("¥1500");
            expect(formatCompactCurrency(12000)).toBe("¥1.2万");
        });

        it("should format millions with compact notation", () => {
            expect(formatCompactCurrency(1000000)).toBe("¥100万");
            expect(formatCompactCurrency(1500000)).toBe("¥150万");
            expect(formatCompactCurrency(12000000)).toBe("¥1200万");
        });

        it("should format billions with compact notation", () => {
            expect(formatCompactCurrency(1000000000)).toBe("¥10億");
            expect(formatCompactCurrency(1500000000)).toBe("¥15億");
        });

        it("should format without symbol when showSymbol is false", () => {
            expect(formatCompactCurrency(1000, false)).toBe("1000");
            expect(formatCompactCurrency(1500000, false)).toBe("150万");
        });

        it("should handle zero and negative values", () => {
            expect(formatCompactCurrency(0)).toBe("¥0");
            expect(formatCompactCurrency(-1000)).toBe("¥-1000");
        });
    });

    describe("calculateFee", () => {
        it("should calculate percentage fees correctly", () => {
            expect(calculateFee(1000, 5)).toBe(50); // 5% of 1000
            expect(calculateFee(2000, 10)).toBe(200); // 10% of 2000
            expect(calculateFee(500, 2.5)).toBe(13); // 2.5% of 500, rounded
        });

        it("should handle zero amounts", () => {
            expect(calculateFee(0, 5)).toBe(0);
            expect(calculateFee(1000, 0)).toBe(0);
        });

        it("should round to nearest integer", () => {
            expect(calculateFee(333, 10)).toBe(33); // 33.3 rounded to 33
            expect(calculateFee(777, 10)).toBe(78); // 77.7 rounded to 78
        });

        it("should handle large amounts", () => {
            expect(calculateFee(1000000, 1)).toBe(10000); // 1% of 1M
            expect(calculateFee(999999, 0.1)).toBe(1000); // 0.1% of ~1M, rounded
        });
    });

    describe("calculateTotalWithFee", () => {
        it("should calculate total including fee", () => {
            expect(calculateTotalWithFee(1000, 5)).toBe(1050); // 1000 + 50 fee
            expect(calculateTotalWithFee(2000, 10)).toBe(2200); // 2000 + 200 fee
        });

        it("should handle zero amounts and fees", () => {
            expect(calculateTotalWithFee(0, 5)).toBe(0);
            expect(calculateTotalWithFee(1000, 0)).toBe(1000);
        });

        it("should work with decimal percentages", () => {
            expect(calculateTotalWithFee(1000, 2.5)).toBe(1025); // 1000 + 25 fee
            expect(calculateTotalWithFee(500, 1.5)).toBe(508); // 500 + 8 fee (rounded)
        });

        it("should handle large amounts", () => {
            expect(calculateTotalWithFee(1000000, 5)).toBe(1050000); // 1M + 50K fee
        });
    });
});
