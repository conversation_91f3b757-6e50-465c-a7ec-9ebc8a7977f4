import{ac as g,ad as y,a7 as l,a5 as p,au as b,aB as N,r as j,a8 as e,ae as v,d2 as w,b8 as C,aD as R,bh as c,a6 as k,at as D,ao as B}from"./index-hZ2cjOe1.js";import{A as P}from"./ag-theme-quartz-B8VIcOdW.js";const A=()=>{const t=g();return y(l.rooftop.beginRooftopBattle.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:l.battle.getStatus.key()}),t.invalidateQueries({queryKey:l.rooftop.rooftopList.key()}),t.invalidateQueries({queryKey:l.user.getCurrentUserInfo.key()})}}))},L=t=>{const{value:a}=t;return a?e.jsxs("div",{className:"relative flex size-full p-5 md:p-3",children:[e.jsx(C,{item:a,className:"mx-auto",height:"h-full"}),t.data?.itemRewardQuantity>1&&e.jsxs("p",{className:"-translate-x-1/2 absolute bottom-4 left-1/2 rounded-lg bg-black/25 px-1 text-center font-bold text-custom-yellow text-sm leading-0 md:bottom-1.5",children:["x",t.data?.itemRewardQuantity]})]}):e.jsx("p",{className:"mt-6 text-bold text-gray-400 text-lg",children:"?"})},H=t=>{const{value:a}=t;return e.jsxs("div",{className:v(t.data?.defeated&&"grayscale",t.data?.disabled&&"opacity-25 grayscale","relative flex h-full flex-col items-center justify-center py-0.5 md:w-full md:flex-row md:items-start md:justify-normal md:gap-4 md:p-2"),children:[e.jsx(w,{src:t.data.image,className:"size-14 rounded-lg border border-blue-800 md:h-full md:w-auto"}),e.jsx("p",{className:"text-wrap! text-center! text-xs! 2xl:text-base! font-semibold text-blue-400 md:my-auto",children:a}),e.jsxs("p",{className:"text-custom-yellow text-xs md:hidden",children:[t.data.rank," Rank"]})]})},O=({npcList:t,currentUser:a})=>{const r=p(),o=b(),{ROOFTOP_BATTLE_AP_COST:d}=N(),u=A(),m=async s=>{if(a&&a?.actionPoints<d)return c.error("Not enough AP");try{await u.mutateAsync({battleOpponentId:s}),r("/fight")}catch(i){const n=i instanceof Error?i.message:"Unknown error occurred";console.error(n),c.error(n)}},x=s=>e.jsx("div",{className:"flex size-full items-center justify-center",children:e.jsx(R,{disabled:s.data?.defeated||s.data?.disabled,className:"text-xs! md:text-sm! w-[90%] 2xl:w-1/2",variant:"destructive",onClick:()=>m(s.data.id),children:s.data?.defeated?"Defeated":e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:"md:text-sm!",children:"Attack"}),e.jsxs("p",{className:"text-xs! md:-mt-1 mb-1",children:[d," AP"]})]})})}),[f,Q]=j.useState([{headerName:"NPC",field:"name",cellRenderer:H},{headerName:"Rank",field:"rank",hide:o,cellClass:"mt-4 text-xl font-semibold text-custom-yellow"},{headerName:"Level",field:"level",wrapHeaderText:!0,autoHeaderHeight:!0,cellClass:"mt-5 text-lg font-bold text-red-500 !md:px-0",maxWidth:o?67:null},{headerName:"Reward",field:"item",cellRenderer:L,sortable:!1},{headerName:"",field:"static",cellRenderer:x,sortable:!1}]),h={flex:1,sortable:!0,suppressMovable:!0,filter:!1,resizable:!1,cellClass:"px-0.5! md:px-2! 2xl:px-6!"};return!t||!a?null:e.jsx("div",{className:"ag-theme-quartz-dark",style:{width:"100%",overflow:"auto"},children:e.jsx(P,{suppressCellFocus:!0,suppressRowHoverHighlight:!0,rowData:t,columnDefs:f,defaultColDef:h,domLayout:"autoHeight",rowHeight:o?100:80})})};function S(){const{data:t,isLoading:a}=k(l.rooftop.rooftopList.queryOptions()),{data:r}=D();return e.jsx("section",{className:" mx-auto rounded-lg py-6 md:max-w-7xl",children:e.jsx("div",{className:" mx-auto h-full px-0 md:px-6",children:e.jsx("div",{className:"mx-auto h-full text-center",children:e.jsx("div",{className:"mt-4 w-full md:mx-0",children:e.jsx(B,{isLoading:a,children:e.jsx(O,{npcList:t,currentUser:r})})})})})})}export{S as default};
