import { api, QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

interface ChatHistoryParams {
    roomId: number;
    limit?: number;
}

const useGetChatHistory = (params: ChatHistoryParams, options: QueryOptions = {}) => {
    const { roomId, limit = 200 } = params;

    return useQuery(
        api.chat.getHistory.queryOptions({
            input: { roomId: roomId.toString(), limit },
            ...options,
        })
    );
};

export default useGetChatHistory;
