import { describe, expect, it } from "vitest";
import { convertGridToPixelPosition, createIsolatedMapLayout, EXPLORE_GRID_CONFIG } from "../mapUtils";
import type { MapNode } from "../../types";

describe("Grid-based Map Utils", () => {
    describe("EXPLORE_GRID_CONFIG", () => {
        it("should have correct 5x5 grid configuration", () => {
            expect(EXPLORE_GRID_CONFIG.SIZE).toBe(5);
            expect(EXPLORE_GRID_CONFIG.MIN_COORD).toBe(0);
            expect(EXPLORE_GRID_CONFIG.MAX_COORD).toBe(4);
            expect(EXPLORE_GRID_CONFIG.TOTAL_POSITIONS).toBe(25);
        });
    });

    describe("convertGridToPixelPosition", () => {
        it("should convert grid coordinates to pixel positions correctly", () => {
            const containerWidth = 800;
            const containerHeight = 600;

            // Test corner positions
            const topLeft = convertGridToPixelPosition({ x: 0, y: 0 }, containerWidth, containerHeight);
            const topRight = convertGridToPixelPosition({ x: 4, y: 0 }, containerWidth, containerHeight);
            const bottomLeft = convertGridToPixelPosition({ x: 0, y: 4 }, containerWidth, containerHeight);
            const bottomRight = convertGridToPixelPosition({ x: 4, y: 4 }, containerWidth, containerHeight);
            const center = convertGridToPixelPosition({ x: 2, y: 2 }, containerWidth, containerHeight);

            // Top-left should be near the padding
            expect(topLeft.x).toBeGreaterThan(0);
            expect(topLeft.y).toBeGreaterThan(0);

            // Top-right should be near the right edge
            expect(topRight.x).toBeGreaterThan(topLeft.x);
            expect(topRight.y).toBe(topLeft.y);

            // Bottom-left should be near the bottom edge
            expect(bottomLeft.x).toBe(topLeft.x);
            expect(bottomLeft.y).toBeGreaterThan(topLeft.y);

            // Bottom-right should be at the far corner
            expect(bottomRight.x).toBe(topRight.x);
            expect(bottomRight.y).toBe(bottomLeft.y);

            // Center should be roughly in the middle
            expect(center.x).toBeGreaterThan(topLeft.x);
            expect(center.x).toBeLessThan(topRight.x);
            expect(center.y).toBeGreaterThan(topLeft.y);
            expect(center.y).toBeLessThan(bottomLeft.y);
        });

        it("should handle invalid grid coordinates gracefully", () => {
            const containerWidth = 800;
            const containerHeight = 600;

            // Test invalid coordinates
            const invalidNegative = convertGridToPixelPosition({ x: -1, y: -1 }, containerWidth, containerHeight);
            const invalidPositive = convertGridToPixelPosition({ x: 5, y: 5 }, containerWidth, containerHeight);

            // Should return fallback position
            expect(invalidNegative.x).toBe(80);
            expect(invalidNegative.y).toBe(80);
            expect(invalidPositive.x).toBe(80);
            expect(invalidPositive.y).toBe(80);
        });

        it("should adapt to different container sizes", () => {
            const smallContainer = convertGridToPixelPosition({ x: 2, y: 2 }, 400, 300);
            const largeContainer = convertGridToPixelPosition({ x: 2, y: 2 }, 1200, 900);

            // Larger container should have more spacing
            expect(largeContainer.x).toBeGreaterThan(smallContainer.x);
            expect(largeContainer.y).toBeGreaterThan(smallContainer.y);
        });

        it("should use responsive padding", () => {
            const smallContainer = convertGridToPixelPosition({ x: 0, y: 0 }, 200, 200);
            const largeContainer = convertGridToPixelPosition({ x: 0, y: 0 }, 1000, 1000);

            // Both should have reasonable padding relative to container size
            expect(smallContainer.x).toBeGreaterThan(0);
            expect(smallContainer.y).toBeGreaterThan(0);
            expect(largeContainer.x).toBeGreaterThan(smallContainer.x);
            expect(largeContainer.y).toBeGreaterThan(smallContainer.y);
        });

        it("should maintain minimum spacing", () => {
            // Test with very small container - the function should handle this gracefully
            const tinyContainer = convertGridToPixelPosition({ x: 1, y: 1 }, 100, 100);
            const origin = convertGridToPixelPosition({ x: 0, y: 0 }, 100, 100);

            // With a 100x100 container, the function will use minimum padding of 50
            // Available space = 100 - 2*50 = 0, so spacing will be 0/(5-1) = 0
            // This is expected behavior for extremely small containers
            const spacingX = tinyContainer.x - origin.x;
            const spacingY = tinyContainer.y - origin.y;

            // For very small containers, spacing might be 0, which is acceptable
            expect(spacingX).toBeGreaterThanOrEqual(0);
            expect(spacingY).toBeGreaterThanOrEqual(0);

            // Test with a more reasonable container size
            const reasonableContainer = convertGridToPixelPosition({ x: 1, y: 1 }, 400, 400);
            const reasonableOrigin = convertGridToPixelPosition({ x: 0, y: 0 }, 400, 400);
            const reasonableSpacingX = reasonableContainer.x - reasonableOrigin.x;
            const reasonableSpacingY = reasonableContainer.y - reasonableOrigin.y;

            // With 400x400, we should get reasonable spacing
            expect(reasonableSpacingX).toBeGreaterThanOrEqual(50);
            expect(reasonableSpacingY).toBeGreaterThanOrEqual(50);
        });

        it("should use custom padding when provided", () => {
            const customPadding = 100;
            const position = convertGridToPixelPosition({ x: 0, y: 0 }, 800, 600, customPadding);

            expect(position.x).toBe(customPadding);
            expect(position.y).toBe(customPadding);
        });
    });

    describe("createIsolatedMapLayout", () => {
        it("should create nodes with grid-based positions when using grid system", () => {
            const nodeCount = 9; // Less than 25 to fit in 5x5 grid
            const nodes = createIsolatedMapLayout(nodeCount, undefined, 1, true);

            expect(nodes).toHaveLength(nodeCount);

            // All nodes should have valid positions
            nodes.forEach((node, index) => {
                expect(node.position).toBeDefined();
                expect(node.position.x).toBeGreaterThan(0);
                expect(node.position.y).toBeGreaterThan(0);
                expect(node.nodeType).toBe("STORY");
                expect(node.isStatic).toBe(false);
                expect(node.location).toBe("shibuya");
            });
        });

        it("should fallback to original layout when exceeding grid capacity", () => {
            const nodeCount = 30; // More than 25 grid positions
            const nodes = createIsolatedMapLayout(nodeCount, undefined, 1, true);

            expect(nodes).toHaveLength(nodeCount);

            // Should still create all nodes with valid positions
            nodes.forEach((node) => {
                expect(node.position).toBeDefined();
                expect(node.position.x).toBeGreaterThan(0);
                expect(node.position.y).toBeGreaterThan(0);
            });
        });

        it("should use original layout when grid system is disabled", () => {
            const nodeCount = 9;
            const spacing = { horizontal: 200, vertical: 150 };
            const nodes = createIsolatedMapLayout(nodeCount, spacing, 1, false);

            expect(nodes).toHaveLength(nodeCount);

            // Should use original spacing-based layout
            const firstNode = nodes[0];
            const secondNode = nodes[1];

            expect(firstNode.position.x).toBe(100); // Original padding
            expect(firstNode.position.y).toBe(100);
            expect(secondNode.position.x).toBe(100 + spacing.horizontal);
            expect(secondNode.position.y).toBe(100);
        });

        it("should create nodes with correct properties", () => {
            const nodeCount = 5;
            const startingId = 10;
            const nodes = createIsolatedMapLayout(nodeCount, undefined, startingId, true);

            expect(nodes).toHaveLength(nodeCount);

            nodes.forEach((node, index) => {
                expect(node.id).toBe(startingId + index);
                expect(node.title).toBe(`Node ${startingId + index}`);
                expect(node.description).toBe(`Isolated node ${startingId + index} - no connections`);
                expect(node.nodeType).toBe("STORY");
                expect(node.status).toBe("available");
                expect(node.isStatic).toBe(false);
                expect(node.location).toBe("shibuya");
                expect(node.connections).toBeUndefined();
                expect(node.chapter).toBeUndefined();
            });
        });

        it("should distribute nodes across grid positions", () => {
            const nodeCount = 9; // 3x3 within 5x5 grid
            const nodes = createIsolatedMapLayout(nodeCount, undefined, 1, true);

            // Check that nodes are distributed across different positions
            const positions = nodes.map((node) => `${node.position.x},${node.position.y}`);
            const uniquePositions = new Set(positions);

            expect(uniquePositions.size).toBe(nodeCount); // All positions should be unique
        });
    });

    describe("Grid positioning integration", () => {
        it("should handle mock explore nodes with grid coordinates", () => {
            // Create mock nodes with grid coordinates
            const mockNodes: MapNode[] = [
                {
                    id: 1,
                    title: "Battle Node",
                    description: "A battle encounter",
                    nodeType: "BATTLE",
                    position: { x: 0, y: 0 }, // Grid coordinates
                    isStatic: false,
                    status: "available",
                    location: "shibuya",
                },
                {
                    id: 2,
                    title: "Shop Node",
                    description: "A shop",
                    nodeType: "SHOP",
                    position: { x: 4, y: 4 }, // Grid coordinates
                    isStatic: true,
                    status: "available",
                    location: "shibuya",
                },
            ];

            // Test that grid coordinates are within valid range
            mockNodes.forEach((node) => {
                expect(node.position.x).toBeGreaterThanOrEqual(EXPLORE_GRID_CONFIG.MIN_COORD);
                expect(node.position.x).toBeLessThanOrEqual(EXPLORE_GRID_CONFIG.MAX_COORD);
                expect(node.position.y).toBeGreaterThanOrEqual(EXPLORE_GRID_CONFIG.MIN_COORD);
                expect(node.position.y).toBeLessThanOrEqual(EXPLORE_GRID_CONFIG.MAX_COORD);
            });

            // Test conversion to pixel positions
            const pixelPositions = mockNodes.map((node) => convertGridToPixelPosition(node.position));

            pixelPositions.forEach((pos) => {
                expect(pos.x).toBeGreaterThan(0);
                expect(pos.y).toBeGreaterThan(0);
            });

            // Corner positions should be different
            expect(pixelPositions[0].x).toBeLessThan(pixelPositions[1].x);
            expect(pixelPositions[0].y).toBeLessThan(pixelPositions[1].y);
        });
    });

    describe("Grid overlay support", () => {
        it("should generate all 25 grid positions for overlay", () => {
            const allPositions = [];
            for (let x = EXPLORE_GRID_CONFIG.MIN_COORD; x <= EXPLORE_GRID_CONFIG.MAX_COORD; x++) {
                for (let y = EXPLORE_GRID_CONFIG.MIN_COORD; y <= EXPLORE_GRID_CONFIG.MAX_COORD; y++) {
                    allPositions.push({ x, y });
                }
            }

            expect(allPositions).toHaveLength(EXPLORE_GRID_CONFIG.TOTAL_POSITIONS);

            // All positions should convert to valid pixel coordinates
            const pixelPositions = allPositions.map((pos) => convertGridToPixelPosition(pos));

            pixelPositions.forEach((pos) => {
                expect(pos.x).toBeGreaterThan(0);
                expect(pos.y).toBeGreaterThan(0);
                expect(pos.x).toBeLessThan(1000); // Reasonable upper bound
                expect(pos.y).toBeLessThan(1000);
            });
        });

        it("should handle reserved positions for grid overlay", () => {
            const reservedPositions = [
                { x: 0, y: 0 },
                { x: 4, y: 4 },
                { x: 2, y: 2 },
            ];

            // All reserved positions should be valid grid coordinates
            reservedPositions.forEach((pos) => {
                expect(pos.x).toBeGreaterThanOrEqual(EXPLORE_GRID_CONFIG.MIN_COORD);
                expect(pos.x).toBeLessThanOrEqual(EXPLORE_GRID_CONFIG.MAX_COORD);
                expect(pos.y).toBeGreaterThanOrEqual(EXPLORE_GRID_CONFIG.MIN_COORD);
                expect(pos.y).toBeLessThanOrEqual(EXPLORE_GRID_CONFIG.MAX_COORD);
            });

            // Should convert to valid pixel positions
            const pixelPositions = reservedPositions.map((pos) => convertGridToPixelPosition(pos));

            pixelPositions.forEach((pos) => {
                expect(pos.x).toBeGreaterThan(0);
                expect(pos.y).toBeGreaterThan(0);
            });
        });

        it("should maintain consistent relative positions across different container sizes", () => {
            const gridPosition = { x: 2, y: 2 }; // Center position

            // Test with different container sizes
            const mobileSize = convertGridToPixelPosition(gridPosition, 375, 667); // iPhone size
            const tabletSize = convertGridToPixelPosition(gridPosition, 768, 1024); // iPad size
            const desktopSize = convertGridToPixelPosition(gridPosition, 1200, 800); // Desktop size

            // All should be valid positions
            [mobileSize, tabletSize, desktopSize].forEach((pos) => {
                expect(pos.x).toBeGreaterThan(0);
                expect(pos.y).toBeGreaterThan(0);
            });

            // Center position should be roughly in the middle for all sizes
            // Check that it's within the middle 60% of the container
            expect(mobileSize.x).toBeGreaterThan(375 * 0.2);
            expect(mobileSize.x).toBeLessThan(375 * 0.8);
            expect(mobileSize.y).toBeGreaterThan(667 * 0.2);
            expect(mobileSize.y).toBeLessThan(667 * 0.8);

            expect(desktopSize.x).toBeGreaterThan(1200 * 0.2);
            expect(desktopSize.x).toBeLessThan(1200 * 0.8);
            expect(desktopSize.y).toBeGreaterThan(800 * 0.2);
            expect(desktopSize.y).toBeLessThan(800 * 0.8);
        });

        it("should ensure all grid positions stay within container boundaries", () => {
            const containerSizes = [
                { width: 300, height: 200 }, // Very small
                { width: 375, height: 667 }, // Mobile
                { width: 768, height: 1024 }, // Tablet
                { width: 1200, height: 800 }, // Desktop
            ];

            containerSizes.forEach(({ width, height }) => {
                // Test all corner and edge positions
                const testPositions = [
                    { x: 0, y: 0 }, // Top-left
                    { x: 4, y: 0 }, // Top-right
                    { x: 0, y: 4 }, // Bottom-left
                    { x: 4, y: 4 }, // Bottom-right
                    { x: 2, y: 2 }, // Center
                ];

                testPositions.forEach((gridPos) => {
                    const pixelPos = convertGridToPixelPosition(gridPos, width, height);

                    // All positions should be within container boundaries
                    expect(pixelPos.x).toBeGreaterThanOrEqual(0);
                    expect(pixelPos.x).toBeLessThanOrEqual(width);
                    expect(pixelPos.y).toBeGreaterThanOrEqual(0);
                    expect(pixelPos.y).toBeLessThanOrEqual(height);
                });
            });
        });
    });
});
