import { create } from "zustand";

interface NormalState {
    marqueeText: string | null;
    setMarqueeText: (marqueeText: string | null) => void;
    refreshPagePrompt: boolean;
    setRefreshPagePrompt: (refreshPagePrompt: boolean) => void;
    iframeActive: boolean;
    setIframeActive: (iframeActive: boolean) => void;
    justJailed: boolean;
    setJustJailed: (justJailed: boolean) => void;
    craftCollectReady: boolean;
    setCraftCollectReady: (craftCollectReady: boolean) => void;
    unreadChatMessages: number;
    addUnreadChatMessages: () => void;
    resetUnreadChatMessages: () => void;
    preventNavigation: boolean;
    setPreventNavigation: (preventNavigation: boolean) => void;
    isInMaintenance: boolean;
    setMaintenanceMode: (status: boolean) => void;
}

export const normalStore = create<NormalState>()((set) => ({
    marqueeText: null,
    setMarqueeText: (marqueeText) => set(() => ({ marqueeText })),
    refreshPagePrompt: false,
    setRefreshPagePrompt: (refreshPagePrompt) => set(() => ({ refreshPagePrompt })),
    iframeActive: false,
    setIframeActive: (iframeActive) => set(() => ({ iframeActive })),
    justJailed: false,
    setJustJailed: (justJailed) => set(() => ({ justJailed })),
    craftCollectReady: false,
    setCraftCollectReady: (craftCollectReady) => set(() => ({ craftCollectReady })),
    unreadChatMessages: 0,
    addUnreadChatMessages: () => set((state) => ({ unreadChatMessages: state.unreadChatMessages + 1 })),
    resetUnreadChatMessages: () => set({ unreadChatMessages: 0 }),
    preventNavigation: false,
    setPreventNavigation: (preventNavigation) => set(() => ({ preventNavigation })),
    isInMaintenance: false,
    setMaintenanceMode: (status) => set({ isInMaintenance: status }),
}));
