import { describe, expect, it, vi, beforeEach } from "vitest";
import { renderHook } from "@testing-library/react";
import useGameConfig from "../useGameConfig";
import { usePersistStore } from "@/app/store/stores";

// Mock the persist store
vi.mock("@/app/store/stores", () => ({
    usePersistStore: vi.fn(),
}));

describe("useGameConfig", () => {
    const mockGameConfig = {
        SHOP1_LEVEL_GATE: 5,
        SHOP2_LEVEL_GATE: 10,
        JOBS_LEVEL_GATE: 3,
        CRAFTING_LEVEL_GATE: 8,
        MARKET_LEVEL_GATE: 4,
        ARCADE_LEVEL_GATE: 25,
        MARQUEE_BANNER_DISABLED: false,
        MAX_ENERGY: 100,
        ENERGY_REGEN_RATE: 1,
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it("should return game config when available", () => {
        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: mockGameConfig,
        });

        const { result } = renderHook(() => useGameConfig());
        expect(result.current).toEqual(mockGameConfig);
    });

    it("should return empty object when game config is null", () => {
        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: null,
        });

        const { result } = renderHook(() => useGameConfig());
        expect(result.current).toEqual({});
    });

    it("should return empty object when game config is undefined", () => {
        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: undefined,
        });

        const { result } = renderHook(() => useGameConfig());
        expect(result.current).toEqual({});
    });

    it("should allow destructuring without errors when config is null", () => {
        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: null,
        });

        const { result } = renderHook(() => {
            const config = useGameConfig();
            // This should not throw an error
            const { SHOP1_LEVEL_GATE, JOBS_LEVEL_GATE } = config;
            return { SHOP1_LEVEL_GATE, JOBS_LEVEL_GATE };
        });

        expect(result.current).toEqual({
            SHOP1_LEVEL_GATE: undefined,
            JOBS_LEVEL_GATE: undefined,
        });
    });

    it("should allow property access without errors when config is null", () => {
        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: null,
        });

        const { result } = renderHook(() => {
            const config = useGameConfig();
            return {
                shopGate: config.SHOP1_LEVEL_GATE,
                jobGate: config.JOBS_LEVEL_GATE,
                bannerDisabled: config.MARQUEE_BANNER_DISABLED,
            };
        });

        expect(result.current).toEqual({
            shopGate: undefined,
            jobGate: undefined,
            bannerDisabled: undefined,
        });
    });

    it("should update when game config changes", () => {
        const { result, rerender } = renderHook(() => useGameConfig());

        // Initially null
        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: null,
        });
        rerender();
        expect(result.current).toEqual({});

        // Then with config
        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: mockGameConfig,
        });
        rerender();
        expect(result.current).toEqual(mockGameConfig);
    });

    it("should handle partial game config", () => {
        const partialConfig = {
            SHOP1_LEVEL_GATE: 5,
            JOBS_LEVEL_GATE: 3,
        };

        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: partialConfig,
        });

        const { result } = renderHook(() => useGameConfig());
        expect(result.current).toEqual(partialConfig);
    });

    it("should handle empty game config object", () => {
        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: {},
        });

        const { result } = renderHook(() => useGameConfig());
        expect(result.current).toEqual({});
    });

    it("should maintain referential stability for empty object", () => {
        vi.mocked(usePersistStore).mockReturnValue({
            gameConfig: null,
        });

        const { result, rerender } = renderHook(() => useGameConfig());
        const firstResult = result.current;

        rerender();
        const secondResult = result.current;

        // Should return the same empty object reference
        expect(firstResult).toBe(secondResult);
    });
});
