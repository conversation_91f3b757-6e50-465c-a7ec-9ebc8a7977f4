# Local Network
# VITE_API_BASE_URL = "https://*************:3000"
# VITE_CHAT_SOCKET_API_URL = "wss://*************:3000/"

# Prod Environment
# VITE_API_BASE_URL = "https://api.battleacademy.io:3000"
# VITE_CHAT_SOCKET_API_URL = "wss://api.battleacademy.io/"

VITE_API_BASE_URL = "http://localhost:3000"
VITE_CHAT_SOCKET_API_URL = "wss://localhost:3000/"

VITE_IMAGE_CDN_URL = "https://cloudflare-image.jamessut.workers.dev"
# VITE_IMAGE_CDN_URL = "https://cdn.battleacademy.io"
# VITE_IMAGE_CDN_URL="https://d13cmcqz8qkryo.cloudfront.net"

ADMIN_PANEL_URL="https://admin.battleacademy.io"
VITE_MAINTENANCE_MODE_ENABLED=false
VITE_SENTRY_ENABLED=false
VITE_POSTHOG_ENABLED=false

# LOCAL
VITE_DISCORD_OAUTH_URL=https://discord.com/oauth2/authorize?client_id=1230584898092404868&response_type=token&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fdiscord&scope=identify+guilds.join

# PROD
# VITE_DISCORD_OAUTH_URL=https://discord.com/oauth2/authorize?client_id=1230584898092404868&response_type=token&redirect_uri=https%3A%2F%2Fapp.battleacademy.io%2Fdiscord&scope=identify+guilds.join

# Firebase Configuration for Push Notifications
# Get these values from Firebase Console > Project Settings
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Firebase Cloud Messaging VAPID Key
# Get this from Firebase Console > Project Settings > Cloud Messaging > Web Push certificates
VITE_FIREBASE_VAPID_KEY=your_vapid_key_here