import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";

interface DiscordButtonProps {
    onClick: (() => void) | null;
    isLinked?: boolean;
}

export default function Discord() {
    const [isLinked, setIsLinked] = useState<boolean>(false);
    const navigate = useNavigate();
    const fragment = new URLSearchParams(window.location.hash.slice(1));
    const [accessToken] = [fragment.get("access_token")];

    const linkDiscordMutation = useMutation(
        api.user.linkDiscord.mutationOptions({
            onSuccess: () => {
                navigate("/discord");
                toast.success("Successfully linked with Discord!");
                setIsLinked(true);
            },
            onError: (error) => {
                navigate("/discord");
                toast.error("Failed to link with Discord");
                console.error("Failed to link with Discord:", error);
            },
        })
    );

    function linkWithDiscord(token: string): void {
        linkDiscordMutation.mutate({ token });
    }

    useEffect(() => {
        if (accessToken) {
            linkWithDiscord(accessToken);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleAuth = (): void => {
        window.location.href = import.meta.env.VITE_DISCORD_OAUTH_URL;
    };

    return (
        <div
            className={cn(
                isLinked ? "md:border-green-600" : "md:border-blue-600",
                "flex h-[calc(100dvh-9.75rem)] flex-col overflow-hidden p-4 text-shadow text-white shadow-sm md:mx-auto md:mt-96 md:h-full md:max-w-2xl md:rounded-lg md:border md:bg-gray-800"
            )}
        >
            <div
                className={cn(
                    isLinked ? "border-green-600" : "border-blue-600",
                    "m-auto flex flex-col items-center justify-center gap-4 rounded-lg border bg-gray-800 p-4 md:rounded-none md:border-transparent md:bg-inherit md:p-0 "
                )}
            >
                <p className="mx-auto text-center font-normal text-xl">
                    Link your Discord to get access to the <span className="text-blue-500">Alpha Tester</span> role
                </p>
                {!isLinked ? (
                    <DiscordButton className="" onClick={() => handleAuth()} />
                ) : (
                    <DiscordButton isLinked onClick={null} />
                )}
            </div>
        </div>
    );
}

const DiscordButton = ({ onClick, isLinked }: DiscordButtonProps) => {
    return (
        <button
            className={cn(
                "flex w-48 items-center rounded-lg border border-indigo-400 bg-[#5865F2] px-6 py-2 font-medium font-body text-sm text-white shadow-md transition-transform hover:brightness-110 focus:outline-hidden focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            )}
            onClick={onClick}
        >
            {isLinked ? (
                <div className="mx-auto flex h-full gap-2">
                    <Check className="my-auto size-6" />
                    <span className="my-auto">Linked</span>
                </div>
            ) : (
                <>
                    <svg
                        className="mr-2 size-6"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlnsXlink="http://www.w3.org/1999/xlink"
                        width="800px"
                        height="800px"
                        viewBox="0 -28.5 256 256"
                        version="1.1"
                        preserveAspectRatio="xMidYMid"
                    >
                        <g>
                            <path
                                d="M216.856339,16.5966031 C200.285002,8.84328665 182.566144,3.2084988 164.041564,0 C161.766523,4.11318106 159.108624,9.64549908 157.276099,14.0464379 C137.583995,11.0849896 118.072967,11.0849896 98.7430163,14.0464379 C96.9108417,9.64549908 94.1925838,4.11318106 91.8971895,0 C73.3526068,3.2084988 55.6133949,8.86399117 39.0420583,16.6376612 C5.61752293,67.146514 -3.4433191,116.400813 1.08711069,164.955721 C23.2560196,181.510915 44.7403634,191.567697 65.8621325,198.148576 C71.0772151,190.971126 75.7283628,183.341335 79.7352139,175.300261 C72.104019,172.400575 64.7949724,168.822202 57.8887866,164.667963 C59.7209612,163.310589 61.5131304,161.891452 63.2445898,160.431257 C105.36741,180.133187 151.134928,180.133187 192.754523,160.431257 C194.506336,161.891452 196.298154,163.310589 198.110326,164.667963 C191.183787,168.842556 183.854737,172.420929 176.223542,175.320965 C180.230393,183.341335 184.861538,190.991831 190.096624,198.16893 C211.238746,191.588051 232.743023,181.531619 254.911949,164.955721 C260.227747,108.668201 245.831087,59.8662432 216.856339,16.5966031 Z M85.4738752,135.09489 C72.8290281,135.09489 62.4592217,123.290155 62.4592217,108.914901 C62.4592217,94.5396472 72.607595,82.7145587 85.4738752,82.7145587 C98.3405064,82.7145587 108.709962,94.5189427 108.488529,108.914901 C108.508531,123.290155 98.3405064,135.09489 85.4738752,135.09489 Z M170.525237,135.09489 C157.88039,135.09489 147.510584,123.290155 147.510584,108.914901 C147.510584,94.5396472 157.658606,82.7145587 170.525237,82.7145587 C183.391518,82.7145587 193.761324,94.5189427 193.539891,108.914901 C193.539891,123.290155 183.391518,135.09489 170.525237,135.09489 Z"
                                fill="#FFFFFF"
                                fillRule="nonzero"
                            ></path>
                        </g>
                    </svg>
                    <span>Link with Discord</span>
                </>
            )}
        </button>
    );
};
