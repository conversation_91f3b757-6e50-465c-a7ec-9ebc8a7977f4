# Architectural Plan: Event-Driven Battle System

This document outlines the plan to refactor the existing battle system to use an event-driven architecture.

**1. Goals:**

- **Decoupling:** Separate the core battle mechanics (state changes, turn processing) from consequential actions (notifications, quest updates, achievement progress, inventory changes, logging, etc.).
- **Modularity:** Allow new features or side effects related to battle events to be added easily by creating new event listeners, without modifying the core battle controller.
- **Scalability:** Enable asynchronous processing of non-critical side effects, potentially offloading work to separate worker processes if needed in the future.
- **Maintainability:** Improve code readability and reduce complexity within the main battle controller logic.
- **Testability:** Make it easier to test the core battle logic and event handlers independently.

**2. Recommended Technology: BullMQ**

- **Reasoning:** The project already utilizes Redis for managing battle state. BullMQ is a robust, Redis-based message queue specifically designed for Node.js. It offers features like:
    - Job persistence
    - Delayed jobs
    - Retry mechanisms for failed jobs
    - Rate limiting
    - Concurrency control
    - A clean API well-suited for TypeScript.
- **Alternative:** While Redis Pub/Sub is simpler, it lacks persistence and guaranteed delivery, making it less suitable for critical side effects. An in-memory `EventEmitter` doesn't scale beyond a single process.

**3. Core Concepts:**

- **Event Emitter:** A central service responsible for publishing events to the queue. The battle controllers (`battle.controller.ts`, `rooftop.controller.ts`) will use this service.
- **Event Queue:** BullMQ will manage named queues for different event types or categories.
- **Event Workers/Listeners:** Dedicated modules or services that listen to specific queues, process incoming event jobs, and interact with the relevant application services (e.g., `NotificationService`, `QuestService`, `UserService`).
- **Event Schemas:** Clearly defined TypeScript interfaces for the data payload of each event type to ensure consistency.

**4. Defined Events (Initial List):**

Clear interfaces will be defined for each event payload.

- `BattleInitiated`: `(battleId: string, battleType: BattleType, attackerId: string, defenderId: string)`
- `BattleActionProcessed`: `(battleId: string, round: number, actingPlayerId: string, targetPlayerId: string, actionType: string, damageDealt: number, effects: string[], attackerHealth: number, targetHealth: number)`
- `BattleFleeAttempted`: `(battleId: string, playerId: string, success: boolean)`
- `BattleWon`: `(battleId: string, winnerId: string, loserId: string, battleType: BattleType, winnerHealth: number, isBoss?: boolean)`
- `BattleLost`: `(battleId: string, loserId: string, winnerId: string, battleType: BattleType, loserHealth: number, isBoss?: boolean)`
- `PostBattleActionTaken`: `(battleId: string, attackerId: string, targetId: string, action: "mug" | "cripple" | "leave", mugAmount?: number, isAnonymous?: boolean)`
- `UserHospitalised`: `(userId: number, reason: string, attackerId?: number | string, attackerName?: string, injuryName?: string, injuryTier?: number)`
- `UserJailed`: `(userId: number, durationMs: number, reason: string, details?: object)`
- `ItemDropped`: `(userId: number, itemId: number, amount: number, battleId?: string)`
- `ConsumableUsed`: `(userId: number, itemId: number, battleId: string)`
- `QuestObjectiveUpdated`: `(userId: number, eventType: string, eventDetails: object)` // Generic, details depend on quest type
- `AchievementProgress`: `(userId: number, achievementKey: string, progressIncrement?: number)`
- `GangRewardProcessed`: `(userId: number, targetId: number, essenceReward?: number, respectReward?: number, targetRespectChange?: number)`
- `BountyClaimed`: `(bountyId: number, claimerId: number, targetId: number)`
- `BountyPlaced`: `(targetId: number, amount: number, reason: string)`
- `ActionLogged`: `(action: string, userId: number, info: object)` // To replace direct `logAction` calls

**5. Refactoring Plan:**

1.  **Setup BullMQ:**
    - Install `bullmq`.
    - Configure BullMQ queues and workers (e.g., a main `battle-events` queue or separate queues like `notifications`, `quests`, `user-updates`).
    - Create an `EventService` (or similar) to abstract publishing events to the queue(s).
2.  **Create Event Handlers:**
    - Create new files/modules for handling specific event categories (e.g., `src/features/battle/handlers/notification.handler.ts`, `src/features/battle/handlers/quest.handler.ts`, `src/features/battle/handlers/user-stats.handler.ts`, etc.).
    - Each handler will define BullMQ worker functions that listen to the relevant queue(s).
    - Inside the workers, parse the event job data and call the appropriate existing services (`NotificationService`, `QuestService`, `UserService`, `AchievementHelpers`, `InventoryService`, `BountyHelper`, `GangHelper`, `logAction`).
3.  **Refactor Controllers (`battle.controller.ts`, `rooftop.controller.ts`):**
    - Inject the `EventService`.
    - Go through functions like `initiateBattle`, `processAttack`, `processFlee`, `processVictoryAction`, `handleVictoryNPC`, `hospitaliseUser`, `handleRooftopBattleWin`, etc.
    - **Remove direct calls** to services like `NotificationService`, `QuestService`, `AchievementHelpers`, `InventoryService`, `UserService` (for side effects like XP, cash, jail, non-health stats), `BountyHelper`, `GangHelper`, `logAction`.
    - **Replace** these calls with `eventService.publish('EventName', { ...payload })`.
    - **Keep** core battle logic: state updates in Redis (`updateBattleState`), damage calculations (`BattleHelpers.ProcessAction`), health/stamina updates _within_ the `BattlePlayer` state object before saving. Direct calls to `UserService.updateUser` for _immediate_ health changes might remain if synchronous updates are critical, or they could become high-priority events. Calls like `UserService.AddXPToUser` should definitely become events.
    - Ensure events are published _after_ the relevant core state change has been persisted (e.g., after `updateBattleState`).

**6. Diagrams:**

- **Component Diagram:**

    ```mermaid
    graph TD
        subgraph "API Layer"
            BR[battle.routes.ts] --> BC[battle.controller.ts]
            RR[rooftop.routes.ts] --> RC[rooftop.controller.ts]
        end

        subgraph "Battle Core"
            BC --> BS[Battle State (Redis)]
            RC --> BS
            BC --> BH[battle.helpers.ts]
            RC --> BH
            BC --> ES[Event Service (Publisher)]
            RC --> ES
        end

        subgraph "Event System (BullMQ)"
            ES --> EQ[Event Queue(s)]
            EQ --> EH[Event Handlers (Workers)]
        end

        subgraph "Event Handlers (Workers)"
            EH --> NS[NotificationService]
            EH --> QS[QuestService]
            EH --> AS[AchievementService]
            EH --> IS[InventoryService]
            EH --> US[UserService]
            EH --> Bounty[BountyHelper]
            EH --> Gang[GangHelper]
            EH --> Log[ActionLogger]
            EH --> Other[...]
        end

        subgraph "Services & Helpers"
            NS
            QS
            AS
            IS
            US
            Bounty
            Gang
            Log
            Other
        end

        style BS fill:#f9f,stroke:#333,stroke-width:2px
        style EQ fill:#ccf,stroke:#333,stroke-width:2px
    ```

- **Sequence Diagram (Example: `processAttack`)**

    ```mermaid
    sequenceDiagram
        participant Client
        participant Routes as battle.routes.ts
        participant Controller as battle.controller.ts
        participant State as Battle State (Redis)
        participant Helpers as battle.helpers.ts
        participant EventService
        participant EventQueue as BullMQ Queue
        participant Handlers as Event Handlers

        Client->>+Routes: POST /battle/attack (action)
        Routes->>+Controller: processAttack(userId, action)
        Controller->>+State: validateBattleState(userId)
        State-->>-Controller: battleState, playerState, targetState
        Controller->>+Helpers: ProcessAction(playerState, targetState, action)
        Helpers-->>-Controller: playerDamage
        Controller->>+Helpers: ProcessAction(targetState, playerState, aiAction)
        Helpers-->>-Controller: targetDamage
        Controller->>Controller: Update player/target health/stamina in state objects
        Controller->>+State: updateBattleState(battleId, updatedState)
        State-->>-Controller: OK
        Controller->>+EventService: publish('BattleActionProcessed', payload)
        EventService->>+EventQueue: Add Job(BattleActionProcessed)
        alt Battle Ended?
            Controller->>EventService: publish('BattleWon'/'BattleLost', payload)
            EventService->>EventQueue: Add Job(BattleWon/Lost)
        end
        Controller-->>-Routes: Result (current round log)
        Routes-->>-Client: Response

        Note right of EventQueue: Later...
        EventQueue->>+Handlers: Process Job(BattleActionProcessed)
        Handlers->>Handlers: Call relevant services (Logging, etc.)
        Handlers-->>-EventQueue: Job Complete

        EventQueue->>+Handlers: Process Job(BattleWon/Lost)
        Handlers->>Handlers: Call relevant services (UserStats, Quests, Achievements, Notifications, etc.)
        Handlers-->>-EventQueue: Job Complete
    ```

**7. Implementation Steps:**

1.  Install and configure BullMQ. Set up basic queue(s) and worker structure.
2.  Define TypeScript interfaces for all event payloads.
3.  Implement the `EventService` for publishing events.
4.  Create the first event handler (e.g., for `ActionLogged`) and its worker.
5.  Refactor one function in `battle.controller.ts` (e.g., `initiateBattle`) to replace `logAction` with `eventService.publish('ActionLogged', ...)` and `eventService.publish('BattleInitiated', ...)`.
6.  Test this single refactored function and the corresponding handler.
7.  Incrementally create other handlers (Notifications, UserStats, Quests, etc.).
8.  Incrementally refactor the remaining functions in `battle.controller.ts` and `rooftop.controller.ts`, replacing direct service calls with event publications. Test each step.
9.  Address complex cases like `processVictoryAction` which involves multiple side effects (XP, cash, jail, hospitalisation, notifications, achievements, quests, bounties, gang rewards, logging). This will likely result in publishing multiple distinct events.

**8. Testing Strategy:**

- **Unit Tests:**
    - Test core battle logic functions in helpers, mocking dependencies.
    - Test controller functions, mocking the `EventService` and verifying that the correct events are published with the expected payloads.
    - Test individual event handlers, mocking the services they call (e.g., test the `NotificationEventHandler` by mocking `NotificationService`).
- **Integration Tests:**
    - Test the full flow from API request through controller logic, event publication, queue processing, and handler execution, potentially using an in-memory Redis for testing BullMQ.

**9. Potential Challenges:**

- **Event Ordering:** Ensure events are processed in a logical order if dependencies exist (though BullMQ jobs are generally processed independently). This usually isn't an issue if handlers are idempotent and operate on distinct data.
- **Idempotency:** Design handlers to be idempotent (safe to run multiple times with the same input) to handle potential retries gracefully.
- **Error Handling:** Implement robust error handling and retry strategies within BullMQ workers. Decide what happens if an event consistently fails processing.
- **Synchronous Needs:** Identify any side effects that _must_ happen synchronously before returning a response to the user. These might need to remain as direct calls or be handled by high-priority, immediately processed events. Updating user health might be one such case.
- **Transactionality:** Consider if multiple state updates across different services need to happen atomically. Event-driven systems make traditional database transactions harder. Sagas or compensating actions might be needed for complex workflows, but might be overkill initially.
