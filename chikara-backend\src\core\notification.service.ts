import {
    sendPushNotifications,
    sendPushNotificationsLegacy,
    type NotificationPayload,
    type BatchNotificationResult,
} from "../config/firebase.js";
import { notificationQueue, getNotificationPriority, NotificationPriority } from "../services/notificationQueue.js";
import * as sockets from "../config/socket.js";
import { db } from "../lib/db.js";
import { NotificationTypes } from "../types/notification.js";
import { LogErrorStack, logger } from "../utils/log.js";

const READ_BY_DEFAULT_NOTIFICATION_TYPES = new Set<NotificationTypes>([
    NotificationTypes.hospitalised,
    NotificationTypes.jail,
    NotificationTypes.transfer_sent,
    NotificationTypes.fight_win,
    NotificationTypes.banned,
]);

export const createNotification = async (
    userId: number,
    notificationType: NotificationTypes,
    details: string,
    read: boolean
) => {
    return await db.notification.create({
        data: {
            notificationType,
            details,
            read,
            userId,
        },
    });
};

export const fetchUserPushTokens = async (userId: number) => {
    return await db.push_token.findMany({
        where: { userId },
        select: {
            userId: true,
            token: true,
        },
    });
};

export async function NotifyUser(
    userId: number,
    notificationType: NotificationTypes,
    details: Record<string, unknown>,
    read?: boolean
): Promise<void> {
    try {
        const normalisedDetails = JSON.stringify(details);
        logger.debug("Notification details: " + normalisedDetails);
        if (notificationType !== NotificationTypes.message) {
            await createNotification(
                userId,
                notificationType,
                normalisedDetails,
                read ?? READ_BY_DEFAULT_NOTIFICATION_TYPES.has(notificationType)
            );
        }
        sockets.SendNotification(userId, {
            type: notificationType,
            details: normalisedDetails,
        });
    } catch (error) {
        LogErrorStack({ message: "Failed to send notification:", error });
    }
}

export function NotifyMessageRemoved(): void {
    // TODO: support other chat rooms
    sockets.NotifyMessageRemoved();
}

export async function sendSinglePushNotification(userId: number, message: string) {
    try {
        // Fetch the user's push tokens from the database
        const pushTokens = await fetchUserPushTokens(userId);

        if (pushTokens.length === 0) {
            logger.error("No push tokens found for user: " + userId);
            return false;
        }

        await sendPushNotificationsLegacy(pushTokens, message);
        return true;
    } catch (error) {
        LogErrorStack({ message: "Error sending push notification:", error });
        return false;
    }
}

// Enhanced notification sending with better payload structure
export async function sendEnhancedPushNotification(
    userId: number,
    notificationType: NotificationTypes,
    title: string,
    body: string,
    additionalData?: Record<string, string>
): Promise<BatchNotificationResult | null> {
    try {
        // Fetch the user's push tokens from the database
        const pushTokens = await fetchUserPushTokens(userId);

        if (pushTokens.length === 0) {
            logger.warn(`No push tokens found for user: ${userId}`);
            return null;
        }

        const payload: NotificationPayload = {
            title,
            body,
            icon: "/logo192.png",
            clickAction: "/",
            data: {
                notificationType,
                userId: userId.toString(),
                ...additionalData,
            },
        };

        const result = await sendPushNotifications(pushTokens, payload);

        // Clean up invalid tokens
        if (result.invalidTokens.length > 0) {
            logger.info(`Cleaning up ${result.invalidTokens.length} invalid tokens for user ${userId}`);
            // TODO: Implement token cleanup in repository
        }

        return result;
    } catch (error) {
        LogErrorStack({ message: "Error sending enhanced push notification:", error });
        return null;
    }
}

// Optimized notification sending using queue system
export async function sendOptimizedPushNotification(
    userId: number,
    notificationType: NotificationTypes,
    title: string,
    body: string,
    additionalData?: Record<string, string>,
    customPriority?: NotificationPriority
): Promise<string[]> {
    try {
        // Fetch the user's push tokens from the database
        const pushTokens = await fetchUserPushTokens(userId);

        if (pushTokens.length === 0) {
            logger.warn(`No push tokens found for user: ${userId}`);
            return [];
        }

        const payload: NotificationPayload = {
            title,
            body,
            icon: "/logo192.png",
            clickAction: "/",
            data: {
                notificationType,
                userId: userId.toString(),
                ...additionalData,
            },
        };

        // Determine priority
        const priority = customPriority ?? getNotificationPriority(notificationType);

        // Queue notifications for each token
        const queueIds: string[] = [];
        for (const tokenRecord of pushTokens) {
            const queueId = await notificationQueue.enqueue(
                userId,
                tokenRecord.token,
                payload,
                notificationType,
                priority
            );
            queueIds.push(queueId);
        }

        logger.info(
            `Queued ${queueIds.length} notifications for user ${userId} (type: ${notificationType}, priority: ${priority})`
        );
        return queueIds;
    } catch (error) {
        LogErrorStack({ message: "Error queuing optimized push notification:", error });
        return [];
    }
}

// Enhanced NotifyUser function with optional push notification optimization
export async function NotifyUserWithPush(
    userId: number,
    notificationType: NotificationTypes,
    details: Record<string, unknown>,
    read?: boolean,
    enablePushNotification = true,
    pushTitle?: string,
    pushBody?: string
): Promise<void> {
    try {
        const normalisedDetails = JSON.stringify(details);
        logger.debug("Notification details: " + normalisedDetails);

        // Create database notification (except for messages)
        if (notificationType !== NotificationTypes.message) {
            await createNotification(
                userId,
                notificationType,
                normalisedDetails,
                read ?? READ_BY_DEFAULT_NOTIFICATION_TYPES.has(notificationType)
            );
        }

        // Send socket notification
        sockets.SendNotification(userId, {
            type: notificationType,
            details: normalisedDetails,
        });

        // Send push notification if enabled
        if (enablePushNotification && pushTitle && pushBody) {
            await sendOptimizedPushNotification(userId, notificationType, pushTitle, pushBody, {
                details: normalisedDetails,
            });
        }
    } catch (error) {
        LogErrorStack({ message: "Failed to send notification with push:", error });
    }
}

export { sendPushNotifications, sendPushNotificationsLegacy } from "../config/firebase.js";

export default {
    NotifyUser,
    NotifyUserWithPush,
    NotifyMessageRemoved,
    fetchUserPushTokens,
    sendPushNotifications,
    sendSinglePushNotification,
    sendOptimizedPushNotification,
};
