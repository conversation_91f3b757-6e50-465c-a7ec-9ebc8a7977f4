import * as dropChanceRepository from "../../repositories/dropchance.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import { DropChanceModel } from "../../lib/db.js";
import { DropChanceTypes, LocationTypes } from "@prisma/client";

export const getDropTables = async () => {
    const drops = await dropChanceRepository.findAllDrops();
    return { data: drops };
};

export const createDropChance = async function (body: {
    itemId: number;
    dropRate: number;
    dropChanceType: DropChanceTypes;
    location?: LocationTypes;
    minLevel?: number;
    maxLevel?: number;
    scavengeType?: string;
    quantity?: number;
    creatureId?: number;
}) {
    const itemId = body.itemId;
    const dropRate = body.dropRate;
    const dropChanceType = body.dropChanceType;
    const location = body.location;
    const minLevel = body.minLevel;
    const maxLevel = body.maxLevel;
    const scavengeType = body.scavengeType;
    const quantity = body.quantity;

    // Validate item exists
    if (!itemId || !(await ItemRepository.findItemById(itemId))) {
        return { error: "Item does not exist" };
    }

    // Validate drop rate
    if (dropRate < 0 || dropRate > 1) {
        return { error: "Invalid drop rate" };
    }

    // Validate drop chance type
    if (!dropChanceRepository.getAllDropChanceTypes().includes(dropChanceType)) {
        return { error: "Invalid drop chance type" };
    }

    // Commented out code for creature validation
    // if (dropChanceType == "creature") { // Using string literal instead of DropChanceTypes.values.CREATURE
    //     if (!(await Creature.findByPk(creatureId))) {
    //         return {error: "Creature does not exist"}
    //     }

    //     if (
    //         await dropChanceRepository.findDropChanceByItemAndCreature(itemId, creatureId)
    //     ) {
    //         return {error: "Drop chance already exists for creature and item combination"}
    //     }
    // }

    const data: Partial<DropChanceModel> = {
        itemId: itemId,
        dropRate: dropRate,
        dropChanceType: dropChanceType,
        location: location || LocationTypes.any,
        quantity: quantity || 1,
        minLevel,
        maxLevel,
        scavengeType,
    };

    const dropChance = await dropChanceRepository.createDropChance(data);
    return { data: dropChance };
};

export const editDropChance = async function (body: {
    id: number;
    itemId: number;
    dropRate: number;
    dropChanceType: DropChanceTypes;
    location?: LocationTypes;
    minLevel?: number;
    maxLevel?: number;
    scavengeType?: string;
    quantity?: number;
    creatureId?: number;
}) {
    const id = body.id;
    const itemId = body.itemId;
    const dropRate = body.dropRate;
    const dropChanceType = body.dropChanceType;
    const location = body.location;
    const minLevel = body.minLevel;
    const maxLevel = body.maxLevel;
    const scavengeType = body.scavengeType;
    const quantity = body.quantity;

    // Validate drop rate
    if (dropRate < 0 || dropRate > 1) {
        return { error: "Invalid drop rate" };
    }

    // Validate drop chance type
    if (!dropChanceRepository.getAllDropChanceTypes().includes(dropChanceType)) {
        return { error: "Invalid drop chance type" };
    }

    // Validate item exists
    if (!itemId || !(await ItemRepository.findItemById(itemId))) {
        return { error: "Item does not exist" };
    }

    // Commented out code for creature validation
    // if (dropChanceType == "creature") { // Using string literal instead of DropChanceTypes.values.CREATURE
    //     if (!(await Creature.findByPk(creatureId))) {
    //         return res.status(400).send("Creature does not exist");
    //     }

    //     if (
    //         await dropChanceRepository.findDropChanceByItemAndCreature(itemId, creatureId)
    //     ) {
    //         return res.status(400).send("Drop chance already exists for creature and item combination");
    //     }
    // }

    const dropChance = await dropChanceRepository.findDropChanceById(id);

    if (!dropChance) {
        return { error: "Drop chance does not exist" };
    }

    // Prepare update data
    const updateData: Partial<DropChanceModel> = {
        itemId,
        dropRate,
        dropChanceType,
    };

    if (location) {
        updateData.location = location;
    }

    if (minLevel) {
        updateData.minLevel = minLevel;
    }

    if (maxLevel) {
        updateData.maxLevel = maxLevel;
    }

    if (scavengeType != "null") {
        updateData.scavengeType = scavengeType;
    }

    if (quantity) {
        updateData.quantity = quantity;
    }

    const updatedDropChance = await dropChanceRepository.updateDropChance(dropChance, updateData);
    return { data: updatedDropChance };
};

export const deleteDropChance = async (id: number) => {
    const count = await dropChanceRepository.deleteDropChanceById(id);
    return { data: "DropChances deleted: " + count };
};
