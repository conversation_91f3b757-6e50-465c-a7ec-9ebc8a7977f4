import bronzeMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Bronze.png";
import goldMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Gold.png";
import silverMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Silver.png";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import Spinner from "@/components/Spinners/Spinner";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { formatTimeToNow } from "@/helpers/dateHelpers";
import { api, QueryOptions } from "@/helpers/api";

interface LeaderboardData {
    level: UserData[];
    pvpwins: UserData[];
    money: UserData[];
    hench: UserData[];
    defensive: UserData[];
    zones: UserData[];
    joblevel: UserData[];
    crafts: UserData[];
    npcwins: UserData[];
    quests: UserData[];
    intelligent: UserData[];
    dexterous: UserData[];
    endurance: UserData[];
    vitality: UserData[];
    muggingGain: UserData[];
    muggingLoss: UserData[];
    casinoWinner: UserData[];
    casinoLoser: UserData[];
    totalMissionHours: UserData[];
    totalStats: UserData[];
    totalBountyRewards: UserData[];
    marketItemsSold: UserData[];
    marketMoneyMade: UserData[];
}

interface LeaderboardResponse {
    data: LeaderboardData;
    lastFetch: number;
}

const useGetLeaderboards = (options: QueryOptions = {}) => {
    return useQuery(
        api.leaderboards.getLeaderBoards.queryOptions({
            staleTime: 300000, // 5 minutes
            ...options,
        })
    ) as { isLoading: boolean; error: any; data: LeaderboardResponse | undefined };
};

function Leaderboard() {
    const leaderboardsConfig = useGameConfig();

    const { isLoading, error, data } = useGetLeaderboards();

    const leaderboardData = data?.data;

    if (leaderboardsConfig?.LEADERBOARDS_DISABLED) {
        return (
            <div className="mt-10 flex flex-col dark:text-slate-200">
                <div className="mx-auto text-center">
                    <h2 className="text-xl">Leaderboards currently Disabled</h2>
                    <p>Please return later.</p>
                </div>
            </div>
        );
    }
    const lastFetch = data?.lastFetch ? data.lastFetch.toString() : null;

    if (error || (!isLoading && !leaderboardData)) {
        return "Failed to fetch leaderboards";
    }

    return (
        <>
            {/* // Add skeleton here // */}

            {isLoading ? (
                <Spinner center />
            ) : (
                <>
                    <p className="lg:-mt-2 my-0.5 text-right text-sm lg:mb-1">
                        {lastFetch && `Last Updated: ${formatTimeToNow(lastFetch)} ago`}
                    </p>
                    <div className="grid 3xl:grid-cols-4 grid-cols-1 gap-4 text-shadow md:mx-auto md:max-w-(--breakpoint-2xl) md:grid-rows-2 xl:grid-cols-2 2xl:grid-cols-3">
                        <LeaderOnlyBoard
                            hideStats
                            leaderboardData={leaderboardData?.["totalStats"]}
                            attributeName="strength"
                            leaderTitleText="Most Stats"
                            atrributeDisplayName="Total Stats"
                        />
                        <LeaderOnlyBoard
                            hideStats
                            leaderboardData={leaderboardData?.["hench"]}
                            attributeName="strength"
                            leaderTitleText="Strongest Student"
                            atrributeDisplayName="Strength"
                        />
                        <LeaderOnlyBoard
                            hideStats
                            leaderboardData={leaderboardData?.["defensive"]}
                            attributeName="defence"
                            leaderTitleText="Most Defensive"
                            atrributeDisplayName="Defence"
                        />
                        <LeaderOnlyBoard
                            hideStats
                            leaderboardData={leaderboardData?.["dexterous"]}
                            attributeName="dexterity"
                            leaderTitleText="Most Dexterous"
                            atrributeDisplayName="Dexterity"
                        />
                        <LeaderOnlyBoard
                            hideStats
                            leaderboardData={leaderboardData?.["intelligent"]}
                            attributeName="intelligence"
                            leaderTitleText="Most Intelligent"
                            atrributeDisplayName="Intelligence"
                        />
                        <LeaderOnlyBoard
                            hideStats
                            leaderboardData={leaderboardData?.["endurance"]}
                            attributeName="endurance"
                            leaderTitleText="Most Endurance"
                            atrributeDisplayName="Endurance"
                        />
                        <LeaderOnlyBoard
                            hideStats
                            leaderboardData={leaderboardData?.["vitality"]}
                            attributeName="vitality"
                            leaderTitleText="Most Vitality"
                            atrributeDisplayName="Vitality"
                        />
                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["pvpwins"]}
                            leaderTitleText="Most Feared Fighter"
                            attributeName="battleWins"
                            atrributeDisplayName="PvP Wins"
                            hideStats={false}
                        />
                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["level"]}
                            leaderTitleText="Biggest Grinder"
                            attributeName="level"
                            atrributeDisplayName="Level"
                            hideStats={false}
                        />

                        <IndividualLeaderboard
                            hideStats
                            leaderboardData={leaderboardData?.["money"]}
                            leaderTitleText="Richest Oligarch"
                            attributeName="bank_balance"
                            atrributeDisplayName="Money"
                        />
                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["zones"]}
                            attributeName="roguelikeMapsCompleted"
                            leaderTitleText="Highest Zone Reached"
                            atrributeDisplayName="Zone"
                            hideStats={false}
                        />
                        <IndividualLeaderboard
                            hideStats
                            leaderboardData={leaderboardData?.["joblevel"]}
                            attributeName="joblevel"
                            leaderTitleText="Highest Job Level"
                            atrributeDisplayName="Job Level"
                        />

                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["crafts"]}
                            attributeName="craftsCompleted"
                            leaderTitleText="Most items crafted"
                            atrributeDisplayName="Items Crafted"
                            hideStats={false}
                        />

                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["npcwins"]}
                            attributeName="npcBattleWins"
                            leaderTitleText="Most NPCs defeated"
                            atrributeDisplayName="NPC Wins"
                            hideStats={false}
                        />

                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["quests"]}
                            attributeName="questsCompleted"
                            leaderTitleText="Most tasks completed"
                            atrributeDisplayName="Tasks complete"
                            hideStats={false}
                        />

                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["muggingGain"]}
                            attributeName="totalMuggingGain"
                            leaderTitleText="Most mugging gains"
                            atrributeDisplayName="Gained from mugging"
                        />
                        <IndividualLeaderboard
                            isNegative
                            leaderboardData={leaderboardData?.["muggingLoss"]}
                            attributeName="totalMuggingLoss"
                            leaderTitleText="Most mugging losses"
                            atrributeDisplayName="Lost from mugging"
                        />
                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["casinoWinner"]}
                            attributeName="totalCasinoProfitLoss"
                            leaderTitleText="Best Gambler"
                            atrributeDisplayName="Casino Profit"
                        />

                        <IndividualLeaderboard
                            isRed
                            leaderboardData={leaderboardData?.["casinoLoser"]}
                            attributeName="totalCasinoProfitLoss"
                            leaderTitleText="Worst Gambler"
                            atrributeDisplayName="Casino Loss"
                        />
                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["totalBountyRewards"]}
                            attributeName="totalBountyRewards"
                            leaderTitleText="Total Bounty Claimed"
                            atrributeDisplayName="Bounty Claimed"
                        />
                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["totalMissionHours"]}
                            attributeName="totalMissionHours"
                            leaderTitleText="Total Mission Hours"
                            atrributeDisplayName="Mission Hours"
                        />
                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["marketItemsSold"]}
                            attributeName="marketItemsSold"
                            leaderTitleText="Market Items Sold"
                            atrributeDisplayName="Items Sold"
                        />
                        <IndividualLeaderboard
                            leaderboardData={leaderboardData?.["marketMoneyMade"]}
                            attributeName="marketMoneyMade"
                            leaderTitleText="Market Money Made"
                            atrributeDisplayName="Yen Made"
                        />
                    </div>
                </>
            )}
        </>
    );
}

interface UserData {
    id: number;
    username: string;
    avatar: string | null;
    level?: number;
    bank_balance?: number;
    strength?: number;
    defence?: number;
    dexterity?: number;
    intelligence?: number;
    endurance?: number;
    vitality?: number;
    joblevel?: number;
    user_achievements?: {
        battleWins?: number | null;
        roguelikeMapsCompleted?: number | null;
        craftsCompleted?: number | null;
        npcBattleWins?: number | null;
        questsCompleted?: number | null;
        totalMuggingGain?: number | null;
        totalMuggingLoss?: number | null;
        totalCasinoProfitLoss?: number | null;
        totalMissionHours?: number | null;
        totalBountyRewards?: number | null;
        marketItemsSold?: number | null;
        marketMoneyMade?: number | null;
    } | null;
    [key: string]: unknown;
}

const getAttribute = (user: UserData, attributeName: string): number => {
    // Handle achievement-based attributes
    if (user.user_achievements && attributeName in user.user_achievements) {
        const value = user.user_achievements[attributeName as keyof typeof user.user_achievements];
        return typeof value === "number" ? value : 0;
    }

    // Handle direct user attributes
    const value = user[attributeName];
    return typeof value === "number" ? value : 0;
};

function IndividualLeaderboard(props: {
    leaderboardData: UserData[] | undefined;
    attributeName: string;
    atrributeDisplayName: string;
    leaderTitleText: string;
    hideStats?: boolean;
    isNegative?: boolean;
    isRed?: boolean;
}) {
    const { USERS_PER_BOARD } = useGameConfig();
    if (!props?.leaderboardData?.length) return null;
    if (Object.keys(props?.leaderboardData)?.length < 1) return;
    const leaderboardData = props.leaderboardData.slice(1);
    const attributeName = props.attributeName;
    const attributeDisplayName = props.atrributeDisplayName;
    const leaderTitleText = props.leaderTitleText;
    const hideStats = props.hideStats;
    const isNegative = props.isNegative;
    const isRed = props.isRed;

    const leader = props.leaderboardData[0];

    return (
        <div className="row-span-2 m-1 md:m-0">
            <div className="flex w-full flex-col overflow-hidden rounded-md bg-white pt-4 pb-6 shadow-sm dark:bg-gray-800">
                <p className="-mt-4 mb-3 flex h-10 truncate pb-2 text-center font-semibold text-gray-900 text-md tracking-wide xl:text-lg dark:bg-indigo-600 dark:font-medium dark:text-slate-100 dark:text-stroke-sm">
                    <span className="mx-auto mt-2 md:mt-1.5">{leaderTitleText}</span>
                </p>

                <div className="m-auto flex flex-row gap-5 pr-6">
                    <img className="h-14 w-16" src={goldMedal} alt="Gold medal" />
                    <Link to={`/profile/${leader.id}`}>
                        <DisplayAvatar className="size-14 rounded-md ring-2 ring-indigo-500" src={leader} />
                    </Link>
                    <div className="flex flex-col">
                        <p
                            className={cn(
                                "text-green-600",
                                "font-semibold text-lg dark:font-medium dark:text-stroke-sm"
                            )}
                        >
                            {leader.username}
                        </p>

                        <p className="text-gray-900 text-lg dark:text-slate-200 dark:text-stroke-sm">
                            {attributeDisplayName}:{" "}
                            <span className={cn(isNegative || isRed ? "text-red-500" : "text-green-500")}>
                                {isNegative ? " -" : ""}
                                {hideStats ? "?" : getAttribute(leader, attributeName)}
                            </span>
                        </p>
                    </div>
                </div>
                <div className="mx-3 mt-6 flex flex-col">
                    <div className="inline-block w-full align-middle">
                        <div className="overflow-hidden rounded-md border-gray-200 border-b shadow-sm dark:border-gray-800">
                            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
                                {/* <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th
                      scope="col"
                      className="pl-32 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-slate-200"
                    >
                      Name
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-slate-200"
                    >
                      {attributeDisplayName}
                    </th>
                  </tr>
                </thead> */}
                                <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-700">
                                    {leaderboardData.slice(0, USERS_PER_BOARD).map((person, i) => (
                                        <tr key={person.id}>
                                            <td className="whitespace-nowrap px-6 py-4">
                                                <div className="flex items-center">
                                                    <div className="mr-5 flex size-10">
                                                        {i === 0 && (
                                                            <img
                                                                className="m-auto size-10"
                                                                src={silverMedal}
                                                                alt="Silver medal"
                                                            />
                                                        )}
                                                        {i === 1 && (
                                                            <img
                                                                className="ml-0.5 h-10 w-8"
                                                                src={bronzeMedal}
                                                                alt="Bronze medal"
                                                            />
                                                        )}
                                                    </div>
                                                    <div className="size-10 shrink-0">
                                                        <Link to={`/profile/${person.id}`}>
                                                            <DisplayAvatar
                                                                className="size-10 rounded-full"
                                                                src={person}
                                                                loading="lazy"
                                                            />
                                                        </Link>
                                                    </div>

                                                    <div className="ml-4">
                                                        <div className="text-gray-900 text-sm dark:text-slate-100 dark:text-stroke-sm">
                                                            {person.username}
                                                        </div>
                                                        <div className="text-gray-500 text-sm dark:text-slate-400 dark:text-stroke-sm">
                                                            Student #{person.id}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>

                                            <td className="whitespace-nowrap px-10 py-4">
                                                <div
                                                    className={cn(
                                                        isNegative || isRed ? "text-red-500" : "text-green-500",
                                                        "text-sm dark:text-stroke-sm"
                                                    )}
                                                >
                                                    {isNegative ? " -" : ""}
                                                    {hideStats ? "" : getAttribute(person, attributeName)}
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

function LeaderOnlyBoard(props: {
    leaderboardData: UserData[] | undefined;
    attributeName: string;
    atrributeDisplayName: string;
    leaderTitleText: string;
    hideStats?: boolean;
}) {
    if (!props?.leaderboardData?.length) return null;
    const attributeName = props.attributeName;
    const attributeDisplayName = props.atrributeDisplayName;
    const leaderTitleText = props.leaderTitleText;
    const hideStats = props.hideStats;

    const leader = props.leaderboardData[0];

    return (
        <div className="m-1 md:m-0">
            <div className="flex w-full flex-col overflow-hidden rounded-md bg-white pt-4 pb-6 shadow-sm dark:bg-gray-800">
                <p className="-mt-4 mb-3 flex h-10 truncate pb-2 text-center font-semibold text-gray-900 text-md tracking-wide xl:text-lg dark:bg-indigo-600 dark:font-medium dark:text-slate-100 dark:text-stroke-sm">
                    <span className="mx-auto mt-2 md:mt-1.5">{leaderTitleText}</span>
                </p>

                <div className="m-auto flex flex-row gap-5 pr-6">
                    <img className="h-14 w-16" src={goldMedal} alt="Gold medal" />
                    <Link to={`/profile/${leader.id}`}>
                        <DisplayAvatar className="size-14 rounded-md ring-2 ring-indigo-500" src={leader} />
                    </Link>
                    <div className="flex flex-col">
                        <p
                            className={cn(
                                "text-green-600",
                                "font-semibold text-lg dark:font-medium dark:text-stroke-sm"
                            )}
                        >
                            {leader.username}
                        </p>
                        <p className="text-gray-900 text-lg dark:text-slate-200 dark:text-stroke-sm">
                            {attributeDisplayName}: {hideStats ? "?" : getAttribute(leader, attributeName)}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Leaderboard;
