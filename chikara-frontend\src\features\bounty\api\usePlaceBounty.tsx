import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import toast from "react-hot-toast";

/**
 * Hook for placing bounties
 */
export const usePlaceBounty = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.bounties.placeBounty.mutationOptions({
            onSuccess: () => {
                // Invalidate related queries
                queryClient.invalidateQueries({
                    queryKey: api.bounties.getActiveBountyList.key(),
                });

                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                toast.success("Bounty placed successfully!");

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error) => {
                const errorMessage = error.message || "Failed to place bounty";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};
