import { describe, expect, it, vi } from "vitest";
import { renderHook } from "@testing-library/react";
import { useQuery } from "@tanstack/react-query";
import useIsQuestCompleted from "../useIsQuestCompleted";

// Mock the useQuery hook
vi.mock("@tanstack/react-query", () => ({
    useQuery: vi.fn(),
}));

// Mock the API
vi.mock("@/helpers/api", () => ({
    api: {
        quests: {
            getProgress: {
                queryOptions: vi.fn((options) => options),
            },
        },
    },
}));

describe("useIsQuestCompleted", () => {
    const mockQuestProgress = [
        {
            id: 1,
            questStatus: "complete",
            quest: { id: 1, name: "First Quest" },
        },
        {
            id: 2,
            questStatus: "in_progress",
            quest: { id: 2, name: "Second Quest" },
        },
        {
            id: 3,
            questStatus: "complete",
            quest: { id: 3, name: "Third Quest" },
        },
        {
            id: 4,
            questStatus: "not_started",
            quest: { id: 4, name: "Fourth Quest" },
        },
    ];

    it("should return true for completed quest", () => {
        vi.mocked(useQuery).mockReturnValue({
            data: mockQuestProgress,
        } as any);

        const { result } = renderHook(() => useIsQuestCompleted("First Quest"));
        expect(result.current).toBe(true);
    });

    it("should return false for non-completed quest", () => {
        vi.mocked(useQuery).mockReturnValue({
            data: mockQuestProgress,
        } as any);

        const { result } = renderHook(() => useIsQuestCompleted("Second Quest"));
        expect(result.current).toBe(false);
    });

    it("should return false for quest that doesn't exist", () => {
        vi.mocked(useQuery).mockReturnValue({
            data: mockQuestProgress,
        } as any);

        const { result } = renderHook(() => useIsQuestCompleted("Nonexistent Quest"));
        expect(result.current).toBe(false);
    });

    it("should return false when quest progress data is null", () => {
        vi.mocked(useQuery).mockReturnValue({
            data: null,
        } as any);

        const { result } = renderHook(() => useIsQuestCompleted("First Quest"));
        expect(result.current).toBe(false);
    });

    it("should return false when quest progress data is undefined", () => {
        vi.mocked(useQuery).mockReturnValue({
            data: undefined,
        } as any);

        const { result } = renderHook(() => useIsQuestCompleted("First Quest"));
        expect(result.current).toBe(false);
    });

    it("should return false when quest progress is empty array", () => {
        vi.mocked(useQuery).mockReturnValue({
            data: [],
        } as any);

        const { result } = renderHook(() => useIsQuestCompleted("First Quest"));
        expect(result.current).toBe(false);
    });

    it("should handle multiple completed quests with same name", () => {
        const duplicateQuestProgress = [
            ...mockQuestProgress,
            {
                id: 5,
                questStatus: "complete",
                quest: { id: 5, name: "First Quest" }, // Duplicate name
            },
        ];

        vi.mocked(useQuery).mockReturnValue({
            data: duplicateQuestProgress,
        } as any);

        const { result } = renderHook(() => useIsQuestCompleted("First Quest"));
        expect(result.current).toBe(true);
    });

    it("should be case sensitive for quest names", () => {
        vi.mocked(useQuery).mockReturnValue({
            data: mockQuestProgress,
        } as any);

        const { result } = renderHook(() => useIsQuestCompleted("first quest"));
        expect(result.current).toBe(false);
    });

    it("should call useQuery with correct options", () => {
        vi.mocked(useQuery).mockReturnValue({
            data: mockQuestProgress,
        } as any);

        renderHook(() => useIsQuestCompleted("First Quest"));

        expect(useQuery).toHaveBeenCalledWith({
            input: { activeOnly: false },
            staleTime: Number.POSITIVE_INFINITY,
        });
    });
});
